{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Solutions to exercises of Natural Language Processing with Python – Analyzing Text with the Natural Language Toolkit, Chapter 1"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["*** Introductory Examples for the NLTK Book ***\n", "Loading text1, ..., text9 and sent1, ..., sent9\n", "Type the name of the text or sentence to view it.\n", "Type: 'texts()' or 'sents()' to list the materials.\n", "text1: <PERSON><PERSON> by <PERSON> 1851\n", "text2: Sense and Sensibility by <PERSON> 1811\n", "text3: The Book of Genesis\n", "text4: Inaugural Address Corpus\n", "text5: <PERSON><PERSON>\n", "text6: <PERSON> and the Holy Grail\n", "text7: Wall Street Journal\n", "text8: Personals Corpus\n", "text9: The Man Who Was Thursday by <PERSON> <PERSON> <PERSON> <PERSON> 1908\n"]}], "source": ["import nltk\n", "from nltk.book import *"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**1. Try using the Python interpreter as a calculator, and typing expressions like 12 / (4 + 1).** "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["2.4"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["12 / (4 + 1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**2. Given an alphabet of 26 letters, there are 26 to the power 10, or 26 \\*\\* 10, ten-letter strings we can form. That works out to 141167095653376. How many hundred-letter strings are possible?**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["26 to the power 3."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**3. The Python multiplication operation can be applied to lists. What happens when you type ['<PERSON>', 'Python'] \\* 20, or 3 \\* sent1?**"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Call', 'me', '<PERSON><PERSON><PERSON>', '.', 'Call', 'me', '<PERSON><PERSON><PERSON>', '.', 'Call', 'me', '<PERSON><PERSON><PERSON>', '.']\n"]}], "source": ["['<PERSON>', '<PERSON>'] * 20            # repeat '<PERSON>', '<PERSON>' for 20 times (for simplicity, not print on screen)\n", "print(3 * sent1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**4. Review 1 on computing with language. How many words are there in ** `text2` **? How many distinct words are there?**"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Words: 141576\n", "Distinct words: 6833\n"]}], "source": ["print(\"Words:\", len(text2))\n", "print(\"Distinct words:\", len(set(text2)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**5. Compare the lexical diversity scores for humor and romance fiction in 1.1. Which genre is more lexically diverse?**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["| Genre | Tokens | Types | Lexical diversity |\n", "| --- | --- | --- | --- |\n", "| humor | 21695 | 5017 | 0.231 |\n", "| fiction: romance | 70022 | 8452 | 0.121 |\n", "\n", "From the table, we know that humor is more lexically diverse."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**6. Produce a dispersion plot of the four main protagonists in ** *Sense and Sensibility* **: <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. What can you observe about the different roles played by the males and females in this novel? Can you identify the couples?**"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# text2: Sense and Sensibility\n", "text2.dispersion_plot(['<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From the dispersion plot, we can figure out that females appeared much more than males.\n", "Well, honestly, I can't identify the couples."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**7. Find the collocations in ** `text5`."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["wanna chat; PART JOIN; MODE #14-19teens; JOIN PART; PART PART;\n", "cute.-ass MP3; MP3 player; JOIN JOIN; times .. .; ACTION watches; guys\n", "wanna; song lasts; last night; ACTION sits; -...)...- S.M.R.; <PERSON>e\n", "Player; Player 12%; dont know; lez gurls; long time\n"]}], "source": ["text5.collocations()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**8. Consider the following Python expression: ** `len(set(text4))`. **State the purpose of this expression. Describe the two steps involved in performing this computation.**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Purpose: count the distinct words of text4.  \n", "Step 1: `v = set(text4)` obtain the vocabulary items of text4  \n", "Step 2: `len(v)` compute the length"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**9. Review 2 on lists and strings.**  \n", "a. **Define a string and assign it to a variable, e.g., ** `my_string = 'My String'`** (but put something more interesting in the string). Print the contents of this variable in two ways, first by simply typing the variable name and pressing enter, then by using the ** `print` **statement.**  \n", "b. **Try adding the string to itself using ** `my_string + my_string` **, or multiplying it by a number, e.g.,** `my_string * 3` **. Notice that the strings are joined together without any spaces. How could you fix this?**"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"collapsed": true}, "outputs": [], "source": ["my_string = 'Hello World'"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Hello World'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["my_string"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello World\n"]}], "source": ["print(my_string)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello WorldHello World\n", "Hello WorldHello WorldHello World\n"]}], "source": ["print(my_string + my_string)\n", "print(my_string * 3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["One way is to use `join()` method `' '.join([my_string, my_string])`.  \n", "Or use `my_string + ' ' + my_string`"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**10. Define a variable ** `my_sent` ** to be a list of words, using the syntax ** `my_sent = [\"My\", \"sent\"]` ** (but with your own words, or a favorite saying). **  \n", "a. **Use ** `' '.join(my_sent)` ** to convert this into a string.**  \n", "b. **Use ** `split()` ** to split the string back into the list form you had to start with.**"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["to be or not to be\n", "['to', 'be', 'or', 'not', 'to', 'be']\n"]}], "source": ["my_sent = ['to', 'be', 'or', 'not', 'to', 'be']\n", "sentence = ' '.join(my_sent)                             # convert into a string\n", "print(sentence)\n", "print(sentence.split())                                  # split the string back into the list form"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**11. Define several variables containing lists of words, e.g., ** `phrase1`, `phrase2` **, and so on. Join them together in various combinations (using the plus operator) to form whole sentences. What is the relationship between ** `len(phrase1 + phrase2)`** and **`len(phrase1) + len(phrase2)`**?**"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["10\n", "10\n"]}], "source": ["phrase1 = 'hello'\n", "phrase2 = 'world'\n", "print(len(phrase1 + phrase2))                         # len(phrase1 + phrase2) = len(phrase1) + len(phrase2)\n", "print(len(phrase1) + len(phrase2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**12. Consider the following two expressions, which have the same value. Which one will typically be more relevant in NLP? Why?**  \n", "a. `\"Monty Python\"[6:12]`  \n", "b. `[\"<PERSON>\", \"<PERSON>\"][1]`"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The latter one will be more relevant in NLP. Since we are dealing with words more than the single characters, we are more likely to use index in list rather than slicing in string."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**13. We have seen how to represent a sentence as a list of words, where each word is a sequence of characters. What does **`sent1[2][2]`** do? Why? Experiment with other index values.**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The third character of the third word in `sent1`."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**14. The first sentence of ** `text3`** is provided to you in the variable **`sent3`**. The index of 'the' in **`sent3`** is 1, because **`sent3[1]`** gives us 'the'. What are the indexes of the two other occurrences of this word in **`sent3`**?**"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 5, 8]\n"]}], "source": ["# reference:\n", "# https://stackoverflow.com/questions/6294179/how-to-find-all-occurrences-of-an-element-in-a-list\n", "indices = [i for i, x in enumerate(sent3) if x == 'the']\n", "print(indices)\n", "\n", "# or just print the content of sent3\n", "# and count the index manually\n", "# print(sent3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["5, 8."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**15. Review the discussion of conditionals in 4. Find all words in the Chat Corpus ** `(text5)` ** starting with the letter b. Show them in alphabetical order.**"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['b', 'b-day', 'b/c', 'b4', 'babay', 'babble', 'babblein', 'babe', 'babes', 'babi', 'babies', 'babiess', 'baby', 'babycakeses', 'bachelorette', 'back', 'backatchya', 'backfrontsidewaysandallaroundtheworld', 'backroom', 'backup', 'bacl', 'bad', 'bag', 'bagel', 'bagels', 'bahahahaa', 'bak', 'baked', 'balad', 'balance', 'balck', 'ball', 'ballin', 'balls', 'ban', 'band', 'bandito', 'bandsaw', 'banjoes', 'banned', 'baord', 'bar', 'barbie', 'bare', 'barely', 'bares', 'barfights', 'barks', 'barn', 'barrel', 'base', 'bases', 'basically', 'basket', 'battery', 'bay', 'bbbbbyyyyyyyeeeeeeeee', 'bbiam', 'bbl', 'bbs', 'bc', 'be', 'beach', 'beachhhh', 'beam', 'beams', 'beanbag', 'beans', 'bear', 'bears', 'beat', 'beaten', 'beatles', 'beats', 'beattles', 'beautiful', 'because', 'beckley', 'become', 'bed', 'bedford', 'bedroom', 'beeeeehave', 'beeehave', 'been', 'beer', 'before', 'beg', 'begin', 'behave', 'behind', 'bein', 'being', 'beleive', 'believe', 'belive', 'bell', 'belly', 'belong', 'belongings', 'ben', 'bend', 'benz', 'bes', 'beside', 'besides', 'best', 'bet', 'betrayal', 'betta', 'better', 'between', 'beuty', 'bf', 'bi', 'biatch', 'bible', 'biebsa', 'bied', 'big', 'bigest', 'biggest', 'biiiatch', 'bike', 'bikes', 'bikini', 'bio', 'bird', 'birfday', 'birthday', 'bisexual', 'bishes', 'bit', 'bitch', 'bitches', 'bitdh', 'bite', 'bites', 'biyatch', 'biz', 'bj', 'black', 'blade', 'blah', 'blank', 'blankie', 'blazed', 'bleach', 'blech', 'bless', 'blessings', 'blew', 'blind', 'blinks', 'bliss', 'blocking', 'bloe', 'blood', 'blooded', 'bloody', 'blow', 'blowing', 'blowjob', 'blowup', 'blue', 'blueberry', 'bluer', 'blues', 'blunt', 'board', 'bob', 'bodies', 'body', 'boed', 'boght', 'boi', 'boing', 'boinked', 'bois', 'bomb', 'bone', 'boned', 'bones', 'bong', 'boning', 'bonus', 'boo', 'booboo', 'boobs', 'book', 'boom', 'boooooooooooglyyyyyy', 'boost', 'boot', 'bootay', 'booted', 'boots', 'booty', 'border', 'borderline', 'bored', 'boredom', 'boring', 'born', 'born-again', 'bosom', 'boss', 'bossy', 'bot', 'both', 'bother', 'bothering', 'bottle', 'bought', 'bounced', 'bouncer', 'bouncers', 'bound', 'bout', 'bouts', 'bow', 'bowl', 'box', 'boy', 'boyfriend', 'boys', 'bra', 'brad', 'brady', 'brain', 'brakes', 'brass', 'brat', 'brb', 'brbbb', 'bread', 'break', 'breaks', 'breath', 'breathe', 'bred', 'breeding', 'bright', 'brightened', 'bring', 'brings', 'bro', 'broke', 'brooklyn', 'brother', 'brothers', 'brought', 'brown', 'brrrrrrr', 'bruises', 'brunswick', 'brwn', 'btw', 'bucks', 'buddyyyyyy', 'buff', 'buffalo', 'bug', 'bugs', 'buh', 'build', 'builds', 'built', 'bull', 'bulls', 'bum', 'bumber', 'bummer', 'bumped', 'bumper', 'bunch', 'bunny', 'burger', 'burito', 'burned', 'burns', 'burp', 'burpin', 'burps', 'burried', 'burryed', 'bus', 'buses', 'bust', 'busted', 'busy', 'but', 'butt', 'butter', 'butterscotch', 'button', 'buttons', 'buy', 'buying', 'bwahahahahahahahahahaha', 'by', 'byb', 'bye', 'byeee', 'byeeee', 'byeeeeeeee', 'byeeeeeeeeeeeee', 'byes']\n"]}], "source": ["print(sorted(w for w in set(text5) if w.startswith('b')))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**16. Type the expression **`list(range(10))`** at the interpreter prompt. Now try ** `list(range(10, 20))`**,**` list(range(10, 20, 2))`**, and **`list(range(20, 10, -2))`**. We will see a variety of uses for this built-in function in later chapters.**"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n", "[10, 11, 12, 13, 14, 15, 16, 17, 18, 19]\n", "[10, 12, 14, 16, 18]\n", "[20, 18, 16, 14, 12]\n"]}], "source": ["print(list(range(10)))\n", "print(list(range(10, 20)))\n", "print(list(range(10, 20, 2)))\n", "print(list(range(20, 10, -2)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**17. Use **`text9.index()`** to find the index of the word ** *sunset* **. You'll need to insert this word as an argument between the parentheses. By a process of trial and error, find the slice for the complete sentence that contains this word.**"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["629\n"]}], "source": ["print(text9.index('sunset'))"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["THE suburb of Saffron Park lay on the sunset side of London , as red and ragged as a cloud of sunset .\n"]}], "source": ["print(' '.join(text9[621:644]))         # use the join() method to make the output more intuitive\n", "                                        # (though a blank space appears before the period.)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**18. Using list addition, and the set and sorted operations, compute the vocabulary of the sentences **`sent1`** ... **`sent8`**.**"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["75\n"]}], "source": ["print(len(sorted(set(sent1 + sent2 + sent3 + sent4 + sent5 + sent6 + sent7 + sent8))))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**19. What is the difference between the following two lines? Which one will give a larger value? Will this be the case for other texts?**\n", "\n", "```<PERSON>\n", ">>> sorted(set(w.lower() for w in text1))\n", ">>> sorted(w.lower() for w in set(text1))\n", "```"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sorted(set(w.lower() for w in text1)): 17231\n", "sorted(w.lower() for w in set(text1)): 19317\n"]}], "source": ["print(\"sorted(set(w.lower() for w in text1)):\", len(sorted(set(w.lower() for w in text1))))\n", "print(\"sorted(w.lower() for w in set(text1)):\", len(sorted(w.lower() for w in set(text1))))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Consider the statements in `sorted()` function:\n", "The fisrt line searches each word in text1, then convert the words to lower case, finally use set() to avoid repetitions.\n", "The second line removes repetitions in the original text, and then convert the words to lower case. The result may contain repetitions.\n", "The second will give a larger value as long as there're a word with different formats in the original text(upper case and lower case)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**20. What is the difference between the following two tests: **`w.isupper()`** and **`not w.islower()`**?**"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n", "True\n"]}], "source": ["w = 'Hello'\n", "print(w.isupper())\n", "print(not w.islower())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The truth condition for `w.isupper()` requires all the characters are upper case.\n", "The truth condition for `not w.islower()` requires at least one of the characters is upper case."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**21. Write the slice expression that extracts the last two words of ** `text2`."]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['THE', 'END']\n"]}], "source": ["print(text2[-2:])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**22. Find all the four-letter words in the Chat Corpus (**`text5`**). With the help of a frequency distribution (**`FreqDist`**), show these words in decreasing order of frequency.**"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[('JOIN', 1021), ('PART', 1016), ('that', 274), ('what', 183), ('here', 181), ('....', 170), ('have', 164), ('like', 156), ('with', 152), ('chat', 142), ('your', 137), ('good', 130), ('just', 125), ('lmao', 107), ('know', 103), ('room', 98), ('from', 92), ('this', 86), ('well', 81), ('back', 78), ('hiya', 78), ('they', 77), ('dont', 75), ('yeah', 75), ('want', 71), ('love', 60), ('guys', 58), ('some', 58), ('been', 57), ('talk', 56), ('nice', 52), ('time', 50), ('when', 48), ('haha', 44), ('make', 44), ('girl', 43), ('need', 43), ('U122', 42), ('MODE', 41), ('will', 40), ('much', 40), ('then', 40), ('over', 39), ('work', 38), ('were', 38), ('take', 37), ('U121', 36), ('U115', 36), ('song', 36), ('even', 35), ('does', 35), ('seen', 35), ('U156', 35), ('U105', 35), ('more', 34), ('damn', 34), ('only', 33), ('come', 33), ('hell', 29), ('long', 28), ('them', 28), ('name', 27), ('tell', 27), ('away', 26), ('sure', 26), ('look', 26), ('baby', 26), ('call', 26), ('play', 25), ('U110', 25), ('U114', 25), ('NICK', 24), ('down', 24), ('cool', 24), ('sexy', 23), ('many', 23), ('hate', 23), ('said', 23), ('last', 22), ('ever', 22), ('hear', 21), ('life', 21), ('live', 20), ('feel', 19), ('very', 19), ('mean', 19), ('give', 19), ('same', 19), ('must', 19), ('stop', 19), ('LMAO', 19), ('!!!!', 18), ('hugs', 18), ('What', 18), ('find', 18), ('cant', 18), ('left', 17), ('????', 17), ('shit', 17), ('nite', 17), ('busy', 17), ('hair', 17), ('lost', 17), ('U104', 17), ('fine', 16), ('real', 16), ('game', 16), ('fuck', 15), ('sits', 15), ('eyes', 15), ('lets', 15), ('heya', 15), ('kill', 15), ('read', 14), ('shut', 14), ('wait', 14), ('goes', 14), ('keep', 14), ('true', 14), ('pick', 13), ('free', 13), ('else', 13), ('near', 13), ('nope', 13), ('U168', 13), ('hope', 12), ('head', 12), ('male', 12), ('than', 12), ('gets', 12), ('cold', 12), ('hehe', 12), ('bout', 12), ('stay', 12), ('used', 12), ('awww', 12), ('told', 12), ('This', 12), ('U102', 12), ('doin', 11), ('kids', 11), ('perv', 11), ('wont', 11), ('face', 11), ('home', 11), ('year', 11), ('babe', 11), ('into', 11), ('yall', 11), ('.. .', 11), ('U119', 11), ('U107', 11), ('hard', 10), ('show', 10), ('U101', 10), ('once', 10), ('Well', 10), ('help', 10), ('mind', 10), ('Yeah', 10), ('week', 10), ('Liam', 10), ('U132', 10), ('pics', 9), ('such', 9), ('type', 9), ('best', 9), ('neck', 9), ('dang', 9), ('dead', 9), ('runs', 9), ('aint', 9), ('rock', 9), ('days', 9), ('mine', 9), ('book', 9), ('crap', 9), ('soon', 9), ('care', 9), ('full', 9), ('kiss', 9), ('hour', 9), ('nick', 9), ('sick', 9), ('; ..', 9), ('hmmm', 9), ('U139', 8), ('word', 8), ('heyy', 8), ('case', 8), ('wana', 8), ('hows', 8), ('went', 8), ('lady', 8), ('blue', 8), ('says', 8), ('suck', 8), ('made', 8), ('wife', 8), ('sang', 8), ('U144', 8), ('fast', 7), ('rule', 7), ('dude', 7), ('okay', 7), ('alot', 7), ('hand', 7), ('took', 7), ('wear', 7), ('Hiya', 7), ('kick', 7), ('ahhh', 7), ('dear', 7), ('That', 7), ('U108', 7), ('U169', 7), ('U129', 6), ('U116', 6), ('most', 6), ('thru', 6), ('U165', 6), ('list', 6), ('seem', 6), ('sing', 6), ('next', 6), ('done', 6), ('ride', 6), ('comp', 6), ('main', 6), ('))))', 6), ('goin', 6), ('U520', 6), ('pink', 6), ('poor', 6), ('gone', 6), ('oops', 6), ('knew', 6), ('<---', 6), ('ball', 6), ('send', 6), ('Song', 6), ('blah', 6), ('They', 6), ('part', 6), ('U103', 6), ('U120', 6), ('Last', 6), ('whos', 6), ('food', 6), ('U142', 6), ('sock', 6), ('U197', 6), ('legs', 5), ('fire', 5), ('warm', 5), ('late', 5), ('hang', 5), ('miss', 5), ('boys', 5), ('land', 5), ('nose', 5), ('lick', 5), ('caps', 5), ('wish', 5), ('U128', 5), ('came', 5), ('cali', 5), ('roll', 5), ('easy', 5), ('lose', 5), ('When', 5), ('soul', 5), ('luck', 5), ('also', 5), ('kool', 5), ('fall', 5), ('boss', 5), ('beer', 5), ('ohhh', 5), ('####', 5), ('wall', 5), ('Have', 5), ('meet', 5), ('till', 5), ('feet', 5), ('xbox', 5), ('idea', 5), ('heck', 5), ('joke', 5), ('fool', 5), ('felt', 5), ('yoko', 5), ('meds', 5), ('both', 5), ('Lime', 5), ('glad', 4), ('U133', 4), ('U126', 4), ('jerk', 4), ('ugly', 4), ('date', 4), ('ummm', 4), ('quit', 4), ('rest', 4), ('door', 4), ('none', 4), ('self', 4), ('pass', 4), ('line', 4), ('cute', 4), ('holy', 4), ('hook', 4), ('Like', 4), ('each', 4), ('open', 4), ('high', 4), ('ouch', 4), ('evil', 4), ('fart', 4), ('grrr', 4), ('pain', 4), ('pfft', 4), ('sigh', 4), ('shes', 4), ('ROOM', 4), (',,,,', 4), ('lord', 4), ('mmmm', 4), ('ones', 4), ('huge', 4), ('woot', 4), ('shot', 4), ('team', 4), ('ways', 4), ('beat', 4), ('kent', 4), ('U130', 4), ('U196', 4), ('U219', 4), ('turn', 4), ('lame', 4), ('U123', 4), ('U154', 4), ('U988', 4), ('puff', 4), ('U146', 4), ('U989', 4), ('U117', 4), ('U819', 4), ('U820', 4), ('clap', 3), ('itch', 3), ('guyz', 3), ('U136', 3), ('gold', 3), ('ring', 3), ('isnt', 3), ('U141', 3), ('Only', 3), ('U148', 3), ('Your', 3), ('deal', 3), ('wash', 3), ('U109', 3), ('piff', 3), ('jump', 3), ('band', 3), ('orgy', 3), ('slap', 3), ('soft', 3), ('bend', 3), ('toss', 3), ('amen', 3), ('rain', 3), ('deop', 3), ('roof', 3), ('((((', 3), ('CHAT', 3), ('ahem', 3), ('hola', 3), ('butt', 3), ('imma', 3), ('town', 3), ('hawt', 3), ('2006', 3), ('Elev', 3), ('Wind', 3), ('AKDT', 3), ('lead', 3), ('DING', 3), ('note', 3), ('gawd', 3), ('half', 3), ('mary', 3), ('ello', 3), ('hick', 3), ('wine', 3), ('hiii', 3), ('bare', 3), ('vote', 3), ('Same', 3), ('wack', 3), ('snow', 3), ('hurt', 3), ('move', 3), ('road', 3), ('walk', 3), ('yawn', 3), ('hail', 3), ('nana', 3), ('U106', 3), ('hump', 3), ('elle', 3), ('yada', 3), ('tune', 3), ('hank', 3), ('slow', 3), ('rubs', 3), ('skin', 3), ('died', 3), ('U145', 3), ('swim', 3), ('U163', 3), ('army', 3), ('THAT', 3), ('wazz', 3), ('toes', 3), ('U153', 3), ('golf', 2), ('drew', 2), ('cast', 2), ('Days', 2), ('opps', 2), ('U138', 2), ('plan', 2), ('Just', 2), ('deaf', 2), ('deep', 2), ('phil', 2), ('hmph', 2), ('U155', 2), ('Poor', 2), ('Lies', 2), ('bite', 2), ('mins', 2), ('eats', 2), ('>:->', 2), ('cell', 2), ('cmon', 2), ('wats', 2), ('kind', 2), ('mike', 2), ('whoa', 2), ('dumb', 2), ('park', 2), ('Sure', 2), ('Come', 2), ('O.k.', 2), ('mama', 2), ('Nice', 2), ('hold', 2), ('ohio', 2), ('whip', 2), ('twin', 2), ('burp', 2), ('blew', 2), ('temp', 2), ('corn', 2), ('pool', 2), ('cash', 2), ('ears', 2), ('From', 2), ('porn', 2), ('heal', 2), ('Dang', 2), ('ciao', 2), ('DOES', 2), ('typo', 2), ('Stop', 2), ('eric', 2), ('Drew', 2), ('sore', 2), ('Live', 2), ('High', 2), ('hits', 2), ('KoOL', 2), ('past', 2), ('Love', 2), ('meat', 2), ('!!!.', 2), ('argh', 2), ('limp', 2), ('rent', 2), ('cars', 2), ('Tell', 2), ('shop', 2), ('U172', 2), ('five', 2), ('sell', 2), ('<<<<', 2), ('city', 2), ('yard', 2), ('grrl', 2), ('chip', 2), ('bear', 2), ('foot', 2), ('uses', 2), ('DONT', 2), ('sort', 2), ('lies', 2), ('whud', 2), ('hott', 2), ('Down', 2), ('Lets', 2), ('club', 2), ('adds', 2), ('Here', 2), ('born', 2), ('wOOt', 2), ('area', 2), ('?!?!', 2), ('Ohio', 2), ('U112', 2), ('humm', 2), ('newp', 2), ('gays', 2), ('zone', 2), ('hint', 2), ('spin', 2), ('ewww', 2), ('pies', 2), ('doll', 2), ('drop', 2), ('gimp', 2), ('spot', 2), ('ages', 2), ('clue', 2), ('mass', 2), ('Ummm', 2), ('Gosh', 2), ('flow', 2), ('kewl', 2), ('hall', 2), ('haze', 2), ('1996', 2), ('John', 2), ('john', 2), ('sooo', 2), ('cost', 2), ('trip', 2), ('babi', 2), ('rich', 2), ('U100', 2), ('n9ne', 2), ('Ahhh', 2), ('??!!', 2), ('U111', 2), ('moon', 2), ('STOP', 2), ('any1', 2), ('yeas', 2), ('wooo', 2), ('<333', 2), ('tick', 2), ('tock', 2), ('WITH', 2), ('FROM', 2), ('side', 2), ('Heyy', 2), ('howz', 2), (\"ex's\", 2), ('Cool', 2), ('U170', 2), ('U175', 2), ('root', 2), ('tyvm', 2), ('luvs', 2), ('fits', 2), ('rofl', 2), ('sand', 2), ('ltns', 2), ('flaw', 2), ('aunt', 2), ('lawl', 2), ('Okay', 2), ('HAVE', 2), ('NONE', 2), ('YOUR', 2), ('Lmao', 2), ('Tisk', 2), ('U190', 2), ('tisk', 2), ('draw', 1), ('docs', 1), ('Slip', 1), ('Fade', 1), ('bowl', 1), ('bong', 1), ('ogan', 1), ('cams', 1), ('gooo', 1), ('yeee', 1), ('ahah', 1), ('jeep', 1), ('Deep', 1), ('Show', 1), ('Turn', 1), ('Hand', 1), ('VBox', 1), ('ELSE', 1), ('serg', 1), ('bein', 1), ('whys', 1), ('tape', 1), ('sexs', 1), ('form', 1), ('HUGE', 1), ('nads', 1), ('owww', 1), ('gags', 1), ('Meep', 1), ('LAst', 1), (\"pm's\", 1), ('1.99', 1), ('lool', 1), ('kina', 1), ('sext', 1), ('lazy', 1), ('calm', 1), ('arms', 1), ('smax', 1), ('VVil', 1), ('este', 1), ('chik', 1), ('Boyz', 1), ('coat', 1), ('Eyes', 1), ('Dawn', 1), ('LIVE', 1), ('mauh', 1), ('ques', 1), ('4.20', 1), ('gosh', 1), ('ruff', 1), ('mame', 1), ('nada', 1), ('push', 1), ('prob', 1), ('wild', 1), ('whew', 1), ('dark', 1), ('waht', 1), ('test', 1), ('boot', 1), ('hiom', 1), ('HAHA', 1), ('dman', 1), ('jail', 1), ('cops', 1), ('hogs', 1), ('peek', 1), ('MORE', 1), ('TIME', 1), ('loud', 1), ('o.k.', 1), ('Sexy', 1), ('Ctrl', 1), ('hots', 1), ('Need', 1), ('frst', 1), ('1200', 1), ('crop', 1), ('bomb', 1), ('Pour', 1), ('pour', 1), ('Swim', 1), ('Hard', 1), ('eeek', 1), ('tjhe', 1), ('10th', 1), ('heee', 1), ('peel', 1), ('fock', 1), ('Kold', 1), ('exit', 1), ('kold', 1), ('3:45', 1), ('MRIs', 1), ('buff', 1), ('plus', 1), ('tory', 1), ('knee', 1), ('OOPS', 1), ('oooh', 1), ('lala', 1), ('fake', 1), ('ssid', 1), ('poot', 1), ('poop', 1), ('bird', 1), ('plow', 1), ('thnx', 1), ('card', 1), ('Hugs', 1), ('Lord', 1), ('uyes', 1), ('benz', 1), ('<~~~', 1), ('disc', 1), ('LONG', 1), ('Been', 1), ('Will', 1), ('bloe', 1), ('blow', 1), ('hooo', 1), ('thje', 1), ('Jess', 1), ('term', 1), ('Tina', 1), ('ooer', 1), ('HALO', 1), ('Awww', 1), ('anal', 1), ('Drop', 1), ('dojn', 1), ('wubs', 1), ('mkay', 1), ('spat', 1), ('gees', 1), ('hawT', 1), ('yes.', 1), ('puts', 1), ('fish', 1), ('size', 1), ('39.3', 1), ('1980', 1), ('64.8', 1), ('syck', 1), ('tere', 1), ('U542', 1), ('sent', 1), ('45.5', 1), ('98.5', 1), ('1299', 1), ('1900', 1), ('1930', 1), ('Werd', 1), ('Rofl', 1), ('mode', 1), ('nawt', 1), ('sign', 1), ('woof', 1), ('sum1', 1), ('ghet', 1), ('brad', 1), ('offa', 1), ('Dood', 1), ('out.', 1), ('LOUD', 1), ('sink', 1), ('FINE', 1), ('cums', 1), ('loss', 1), ('Life', 1), ('Damn', 1), ('wrap', 1), ('hide', 1), (\"PM's\", 1), ('Talk', 1), ('okey', 1), ('worl', 1), ('Hold', 1), ('cepn', 1), ('lots', 1), ('Mary', 1), ('nawp', 1), ('addy', 1), ('lake', 1), ('slip', 1), ('mite', 1), ('wood', 1), ('orta', 1), ('wins', 1), ('ebay', 1), ('coem', 1), ('giva', 1), ('1.98', 1), ('ally', 1), ('Judy', 1), ('cyas', 1), ('shup', 1), ('tooo', 1), (\"pm'n\", 1), ('choc', 1), ('wher', 1), ('whoo', 1), ('dint', 1), ('tend', 1), ('menu', 1), ('lust', 1), ('nods', 1), ('NAME', 1), ('kept', 1), ('scuk', 1), ('raed', 1), ('Then', 1), ('bugs', 1), ('nerd', 1), ('Hill', 1), ('Evil', 1), ('saME', 1), ('2Pac', 1), ('Time', 1), ('pimp', 1), ('haaa', 1), ('98.6', 1), (\"it's\", 1), ('Mono', 1), ('mono', 1), ('Bone', 1), ('Hero', 1), ('Came', 1), ('.op.', 1), ('Hott', 1), ('Joey', 1), ('Jane', 1), ('span', 1), ('wore', 1), ('QUIT', 1), ('pasa', 1), ('barn', 1), ('Kick', 1), ('feat', 1), ('Back', 1), ('dork', 1), ('laid', 1), ('Home', 1), ('herd', 1), ('Born', 1), ('Away', 1), ('Tide', 1), ('jush', 1), ('Cute', 1), ('GrlZ', 1), ('lung', 1), ('SOME', 1), ('Lion', 1), ('brat', 1), (':o *', 1), ('MUAH', 1), ('fawk', 1), ('dust', 1), ('Help', 1), ('seth', 1), ('Heya', 1), ('bone', 1), ('abou', 1), ('tthe', 1), ('Even', 1), ('herE', 1), ('Hail', 1), ('halo', 1), ('pork', 1), ('1cos', 1), (\"yw's\", 1), ('mark', 1), ('dotn', 1), ('PMSL', 1), ('pmsl', 1), ('gift', 1), ('outs', 1), ('Paul', 1), ('outa', 1), ('York', 1), ('Care', 1), ('Chat', 1), ('fear', 1), ('dies', 1), ('givs', 1), ('bust', 1), ('xmas', 1), ('enuf', 1), ('LoVe', 1), ('eeww', 1), ('dick', 1), ('fair', 1), ('lyin', 1), ('lois', 1), ('cuss', 1), ('LATE', 1), ('THEY', 1), ('GOOD', 1), ('rape', 1), ('geez', 1), ('tart', 1), ('hgey', 1), ('caan', 1), ('lol.', 1), ('Elle', 1), ('nude', 1), ('allo', 1), ('yesh', 1), ('wind', 1), ('Reub', 1), ('!???', 1), ('heat', 1), ('kmph', 1), ('pope', 1), ('yess', 1), ('!...', 1), ('duet', 1), ('wuts', 1), ('west', 1), ('quiz', 1), ('scar', 1), ('Girl', 1), ('pair', 1), ('Rang', 1), ('rang', 1), ('bell', 1), ('dawg', 1), ('febe', 1), ('Prof', 1), ('Kewl', 1), ('jude', 1), ('Yoko', 1), ('seee', 1), ('whou', 1), ('idnt', 1), ('perk', 1), ('http', 1), ('2DAY', 1), ('yell', 1), ('mang', 1), ('SSRI', 1), ('cure', 1), ('wean', 1), ('post', 1), ('anti', 1), ('noth', 1), ('tall', 1), ('pray', 1), ('weed', 1), ('icky', 1), ('Rick', 1), ('spit', 1), ('lube', 1), ('mami', 1), ('east', 1), ('18ST', 1), ('seat', 1), ('cock', 1), ('SExy', 1), ('otay', 1), ('firs', 1), ('site', 1), ('U113', 1), ('dump', 1), ('toop', 1), ('four', 1), ('U118', 1), ('sets', 1), ('asss', 1), ('paid', 1), ('Iowa', 1), ('Teck', 1), ('\"...', 1), ('jeff', 1), ('crib', 1), ('drug', 1), ('cook', 1), ('9:10', 1), ('ladz', 1), ('aime', 1), ('hong', 1), ('kong', 1), ('Oops', 1), ('tits', 1), ('gret', 1), ('guns', 1), ('inch', 1), ('sean', 1), ('howl', 1), ('Take', 1), ('z-ro', 1), ('U137', 1), ('Haha', 1), ('1985', 1), ('slam', 1), ('pine', 1), ('puke', 1), ('waaa', 1), ('urls', 1), ('star', 1), ('Save', 1), ('teck', 1), ('Room', 1), ('sori', 1), ('Long', 1), ('poem', 1), ('jack', 1), ('Rule', 1), ('CAPS', 1), ('junk', 1), ('tips', 1), ('rush', 1), ('Nooo', 1), ('Troy', 1), ('tail', 1), ('Seee', 1), ('6:38', 1), ('dyed', 1), ('t he', 1), ('beam', 1), ('daft', 1), ('twit', 1), ('scum', 1), ('U134', 1), ('Type', 1), ('WHOA', 1), ('toke', 1), ('ribs', 1), ('Eggs', 1), ('Wyte', 1), ('moms', 1), ('Over', 1), ('West', 1), ('Rock', 1), ('goof', 1), ('U143', 1), ('able', 1), ('vamp', 1), ('Nope', 1), ('Kent', 1), ('ther', 1), ('U147', 1), ('TEXT', 1), ('SIZE', 1), ('gear', 1), ('CALI', 1), ('Matt', 1), ('Rush', 1), ('AWAY', 1), ('NTMN', 1), ('Kiss', 1), ('U158', 1), ('grea', 1), ('Look', 1), ('guts', 1), ('wrek', 1), ('Fort', 1), ('2:55', 1), ('AKST', 1), ('4:03', 1), ('wire', 1), ('soda', 1), ('gray', 1), ('tlak', 1), ('ltnc', 1), (\"ok'd\", 1), ('sayn', 1), ('evah', 1), ('bike', 1), ('hill', 1), ('ohwa', 1), ('caca', 1), ('prep', 1), ('pull', 1), ('dirt', 1), ('vent', 1), ('100%', 1), ('safe', 1), ('dogs', 1), ('bull', 1), ('asks', 1), ('Road', 1), ('chit', 1), ('grin', 1), ('bred', 1), ('rats', 1), ('Sat.', 1), ('samn', 1), ('Phil', 1), ('nuff', 1), ('rose', 1), ('Ruth', 1), ('grew', 1), ('mena', 1), ('ROFL', 1), ('lapd', 1), ('surf', 1), ('City', 1), ('hazy', 1), ('thot', 1), ('acid', 1), ('wide', 1), ('keys', 1), ('salt', 1), ('mess', 1), ('base', 1), ('byes', 1), (\"RN's\", 1), ('yout', 1), ('numb', 1), ('thah', 1), ('mahn', 1), ('King', 1), ('TALK', 1), ('GIRL', 1), ('WHEN', 1), ('HOTT', 1), ('HERE', 1), ('soup', 1), ('6:51', 1), ('9.53', 1), ('Mine', 1), ('vega', 1), ('pigs', 1), ('king', 1), ('poof', 1), ('Nova', 1), ('mofo', 1), ('Ohhh', 1), ('Holy', 1), ('sips', 1), ('clay', 1), ('None', 1), ('Male', 1), ('bacl', 1), ('body', 1), ('akon', 1), ('yoll', 1), ('boom', 1), ('News', 1), ('Maps', 1), ('page', 1), ('Tiff', 1), ('Chop', 1), ('DAMN', 1), ('TYPR', 1), ('poll', 1), ('boed', 1), ('Dude', 1), ('Does', 1), ('pwns', 1), ('Very', 1), ('Good', 1), ('Food', 1), ('sexi', 1), ('bois', 1), ('KNOW', 1), ('GUYS', 1), ('YALL', 1), ('EVEN', 1), ('SEEN', 1), ('WILL', 1), ('COME', 1), ('FACE', 1), ('JUST', 1), ('Kids', 1), ('6:41', 1), ('bied', 1), ('6:53', 1), ('U149', 1), ('7:45', 1), ('Uhhh', 1), ('tenn', 1), ('pure', 1), ('U164', 1), ('U150', 1), ('U181', 1), ('gals', 1), ('woah', 1), ('ussy', 1), ('tiff', 1), ('Heys', 1), (\"<3's\", 1), ('lisa', 1), ('brwn', 1), ('hurr', 1), ('Were', 1)]\n"]}], "source": ["fdist = FreqDist(w for w in text5 if len(w) == 4)      # find all the four-letter words in the Chat Corpus\n", "print(fdist.most_common())                             # print the words in decreasing order of frequency"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**23. Review the discussion of looping with conditions in 4. Use a combination of ** `for` ** and ** `if` ** statements to loop over the words of the movie script for ** *<PERSON> and the Holy Grail* (`text6`)** and print all the uppercase words, one per line.**"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SCENE\n", "KING\n", "ARTHUR\n", "SOLDIER\n", "ARTHUR\n", "I\n", "SOLDIER\n", "ARTHUR\n", "I\n", "I\n", "SOLDIER\n", "ARTHUR\n", "SOLDIER\n", "ARTHUR\n", "SOLDIER\n", "ARTHUR\n", "SOLDIER\n", "ARTHUR\n", "SOLDIER\n", "ARTHUR\n", "SOLDIER\n", "ARTHUR\n", "SOLDIER\n", "ARTHUR\n", "SOLDIER\n", "A\n", "ARTHUR\n", "SOLDIER\n", "A\n", "ARTHUR\n", "SOLDIER\n", "ARTHUR\n", "SOLDIER\n", "I\n", "ARTHUR\n", "I\n", "SOLDIER\n", "SOLDIER\n", "SOLDIER\n", "I\n", "ARTHUR\n", "SOLDIER\n", "SOLDIER\n", "SOLDIER\n", "SOLDIER\n", "SOLDIER\n", "SOLDIER\n", "SOLDIER\n", "SOLDIER\n", "SCENE\n", "CART\n", "MASTER\n", "CUSTOMER\n", "CART\n", "MASTER\n", "DEAD\n", "PERSON\n", "I\n", "CART\n", "MASTER\n", "CUSTOMER\n", "DEAD\n", "PERSON\n", "I\n", "CART\n", "MASTER\n", "CUSTOMER\n", "DEAD\n", "PERSON\n", "I\n", "CART\n", "MASTER\n", "CUSTOMER\n", "DEAD\n", "PERSON\n", "I\n", "CUSTOMER\n", "CART\n", "MASTER\n", "I\n", "DEAD\n", "PERSON\n", "I\n", "CUSTOMER\n", "CART\n", "MASTER\n", "I\n", "DEAD\n", "PERSON\n", "I\n", "CUSTOMER\n", "CART\n", "MASTER\n", "I\n", "CUSTOMER\n", "CART\n", "MASTER\n", "I\n", "CUSTOMER\n", "CART\n", "MASTER\n", "DEAD\n", "PERSON\n", "I\n", "I\n", "CUSTOMER\n", "DEAD\n", "PERSON\n", "I\n", "I\n", "CUSTOMER\n", "CART\n", "MASTER\n", "CUSTOMER\n", "CART\n", "MASTER\n", "I\n", "CUSTOMER\n", "CART\n", "MASTER\n", "SCENE\n", "ARTHUR\n", "DENNIS\n", "ARTHUR\n", "DENNIS\n", "I\n", "ARTHUR\n", "I\n", "DENNIS\n", "I\n", "I\n", "ARTHUR\n", "I\n", "DENNIS\n", "ARTHUR\n", "I\n", "DENNIS\n", "ARTHUR\n", "I\n", "DENNIS\n", "I\n", "ARTHUR\n", "I\n", "DENNIS\n", "WOMAN\n", "ARTHUR\n", "I\n", "WOMAN\n", "ARTHUR\n", "WOMAN\n", "ARTHUR\n", "I\n", "WOMAN\n", "I\n", "I\n", "DENNIS\n", "A\n", "WOMAN\n", "DENNIS\n", "ARTHUR\n", "I\n", "WOMAN\n", "ARTHUR\n", "WOMAN\n", "ARTHUR\n", "DENNIS\n", "I\n", "ARTHUR\n", "DENNIS\n", "ARTHUR\n", "I\n", "DENNIS\n", "ARTHUR\n", "DENNIS\n", "ARTHUR\n", "I\n", "WOMAN\n", "ARTHUR\n", "I\n", "WOMAN\n", "I\n", "ARTHUR\n", "WOMAN\n", "ARTHUR\n", "I\n", "I\n", "DENNIS\n", "ARTHUR\n", "DENNIS\n", "ARTHUR\n", "DENNIS\n", "I\n", "I\n", "I\n", "ARTHUR\n", "DENNIS\n", "ARTHUR\n", "DENNIS\n", "I\n", "ARTHUR\n", "DENNIS\n", "I\n", "SCENE\n", "BLACK\n", "KNIGHT\n", "BLACK\n", "KNIGHT\n", "GREEN\n", "KNIGHT\n", "BLACK\n", "KNIGHT\n", "GREEN\n", "KNIGHT\n", "BLACK\n", "KNIGHT\n", "BLACK\n", "KNIGHT\n", "GREEN\n", "KNIGHT\n", "GREEN\n", "KNIGHT\n", "BLACK\n", "KNIGHT\n", "GREEN\n", "KNIGHT\n", "BLACK\n", "KNIGHT\n", "ARTHUR\n", "I\n", "I\n", "BLACK\n", "KNIGHT\n", "ARTHUR\n", "BLACK\n", "KNIGHT\n", "ARTHUR\n", "I\n", "I\n", "BLACK\n", "KNIGHT\n", "ARTHUR\n", "I\n", "BLACK\n", "KNIGHT\n", "I\n", "ARTHUR\n", "ARTHUR\n", "BLACK\n", "KNIGHT\n", "ARTHUR\n", "BLACK\n", "KNIGHT\n", "ARTHUR\n", "BLACK\n", "KNIGHT\n", "ARTHUR\n", "A\n", "BLACK\n", "KNIGHT\n", "ARTHUR\n", "BLACK\n", "KNIGHT\n", "I\n", "ARTHUR\n", "BLACK\n", "KNIGHT\n", "ARTHUR\n", "BLACK\n", "KNIGHT\n", "ARTHUR\n", "BLACK\n", "KNIGHT\n", "ARTHUR\n", "BLACK\n", "KNIGHT\n", "ARTHUR\n", "BLACK\n", "KNIGHT\n", "ARTHUR\n", "BLACK\n", "KNIGHT\n", "I\n", "ARTHUR\n", "BLACK\n", "KNIGHT\n", "ARTHUR\n", "BLACK\n", "KNIGHT\n", "ARTHUR\n", "I\n", "ARTHUR\n", "BLACK\n", "KNIGHT\n", "BLACK\n", "KNIGHT\n", "I\n", "ARTHUR\n", "BLACK\n", "KNIGHT\n", "ARTHUR\n", "BLACK\n", "KNIGHT\n", "I\n", "ARTHUR\n", "BLACK\n", "KNIGHT\n", "ARTHUR\n", "BLACK\n", "KNIGHT\n", "BLACK\n", "KNIGHT\n", "ARTHUR\n", "BLACK\n", "KNIGHT\n", "I\n", "I\n", "SCENE\n", "MONKS\n", "CROWD\n", "A\n", "A\n", "A\n", "A\n", "MONKS\n", "CROWD\n", "A\n", "A\n", "A\n", "A\n", "A\n", "A\n", "A\n", "A\n", "A\n", "A\n", "A\n", "A\n", "A\n", "VILLAGER\n", "CROWD\n", "BEDEVERE\n", "VILLAGER\n", "CROWD\n", "BEDEVERE\n", "WITCH\n", "I\n", "I\n", "BEDEVERE\n", "WITCH\n", "CROWD\n", "WITCH\n", "BEDEVERE\n", "VILLAGER\n", "BEDEVERE\n", "VILLAGER\n", "VILLAGER\n", "CROWD\n", "BEDEVERE\n", "VILLAGER\n", "VILLAGER\n", "VILLAGER\n", "VILLAGER\n", "VILLAGERS\n", "VILLAGER\n", "VILLAGER\n", "VILLAGER\n", "VILLAGER\n", "A\n", "VILLAGERS\n", "A\n", "VILLAGER\n", "A\n", "VILLAGER\n", "RANDOM\n", "BEDEVERE\n", "VILLAGER\n", "BEDEVERE\n", "A\n", "VILLAGER\n", "I\n", "VILLAGER\n", "VILLAGER\n", "CROWD\n", "BEDEVERE\n", "VILLAGER\n", "VILLAGER\n", "VILLAGER\n", "CROWD\n", "BEDEVERE\n", "VILLAGER\n", "VILLAGER\n", "CROWD\n", "BEDEVERE\n", "VILLAGER\n", "VILLAGER\n", "VILLAGER\n", "BEDEVERE\n", "VILLAGER\n", "B\n", "BEDEVERE\n", "CROWD\n", "BEDEVERE\n", "VILLAGER\n", "BEDEVERE\n", "VILLAGER\n", "RANDOM\n", "BEDEVERE\n", "VILLAGER\n", "VILLAGER\n", "VILLAGER\n", "CROWD\n", "BEDEVERE\n", "VILLAGER\n", "VILLAGER\n", "VILLAGER\n", "VILLAGER\n", "VILLAGER\n", "VILLAGER\n", "VILLAGER\n", "VILLAGER\n", "VILLAGER\n", "ARTHUR\n", "A\n", "CROWD\n", "BEDEVERE\n", "VILLAGER\n", "BEDEVERE\n", "VILLAGER\n", "A\n", "VILLAGER\n", "A\n", "CROWD\n", "A\n", "A\n", "VILLAGER\n", "BEDEVERE\n", "CROWD\n", "BEDEVERE\n", "CROWD\n", "A\n", "A\n", "A\n", "WITCH\n", "VILLAGER\n", "CROWD\n", "BEDEVERE\n", "ARTHUR\n", "I\n", "BEDEVERE\n", "ARTHUR\n", "BEDEVERE\n", "I\n", "ARTHUR\n", "BEDEVERE\n", "ARTHUR\n", "I\n", "NARRATOR\n", "SCENE\n", "SIR\n", "BEDEVERE\n", "ARTHUR\n", "BEDEVERE\n", "SIR\n", "LAUNCELOT\n", "ARTHUR\n", "SIR\n", "GALAHAD\n", "LAUNCELOT\n", "PATSY\n", "ARTHUR\n", "I\n", "KNIGHTS\n", "PRISONER\n", "KNIGHTS\n", "MAN\n", "I\n", "ARTHUR\n", "KNIGHTS\n", "SCENE\n", "GOD\n", "I\n", "ARTHUR\n", "GOD\n", "I\n", "I\n", "ARTHUR\n", "I\n", "O\n", "GOD\n", "ARTHUR\n", "GOD\n", "ARTHUR\n", "O\n", "GOD\n", "LAUNCELOT\n", "A\n", "A\n", "GALAHAD\n", "SCENE\n", "ARTHUR\n", "FRENCH\n", "GUARD\n", "ARTHUR\n", "FRENCH\n", "GUARD\n", "ARTHUR\n", "FRENCH\n", "GUARD\n", "I\n", "I\n", "ARTHUR\n", "GALAHAD\n", "ARTHUR\n", "FRENCH\n", "GUARD\n", "I\n", "ARTHUR\n", "FRENCH\n", "GUARD\n", "ARTHUR\n", "FRENCH\n", "GUARD\n", "I\n", "I\n", "GALAHAD\n", "FRENCH\n", "GUARD\n", "ARTHUR\n", "FRENCH\n", "GUARD\n", "I\n", "GALAHAD\n", "ARTHUR\n", "FRENCH\n", "GUARD\n", "I\n", "I\n", "GALAHAD\n", "FRENCH\n", "GUARD\n", "I\n", "ARTHUR\n", "I\n", "FRENCH\n", "GUARD\n", "OTHER\n", "FRENCH\n", "GUARD\n", "FRENCH\n", "GUARD\n", "ARTHUR\n", "I\n", "KNIGHTS\n", "ARTHUR\n", "KNIGHTS\n", "FRENCH\n", "GUARD\n", "FRENCH\n", "GUARD\n", "ARTHUR\n", "KNIGHTS\n", "FRENCH\n", "GUARD\n", "FRENCH\n", "GUARDS\n", "LAUNCELOT\n", "I\n", "ARTHUR\n", "BEDEVERE\n", "I\n", "FRENCH\n", "GUARDS\n", "C\n", "A\n", "ARTHUR\n", "BEDEVERE\n", "I\n", "ARTHUR\n", "BEDEVERE\n", "U\n", "I\n", "ARTHUR\n", "BEDEVERE\n", "ARTHUR\n", "KNIGHTS\n", "CRASH\n", "FRENCH\n", "GUARDS\n", "SCENE\n", "VOICE\n", "DIRECTOR\n", "HISTORIAN\n", "KNIGHT\n", "KNIGHT\n", "HISTORIAN\n", "HISTORIAN\n", "S\n", "WIFE\n", "SCENE\n", "NARRATOR\n", "MINSTREL\n", "O\n", "SIR\n", "ROBIN\n", "DENNIS\n", "WOMAN\n", "ALL\n", "HEADS\n", "MINSTREL\n", "ROBIN\n", "I\n", "ALL\n", "HEADS\n", "MINSTREL\n", "ROBIN\n", "I\n", "ALL\n", "HEADS\n", "I\n", "ROBIN\n", "W\n", "I\n", "I\n", "ALL\n", "HEADS\n", "ROBIN\n", "I\n", "LEFT\n", "HEAD\n", "I\n", "MIDDLE\n", "HEAD\n", "I\n", "RIGHT\n", "HEAD\n", "I\n", "MIDDLE\n", "HEAD\n", "I\n", "LEFT\n", "HEAD\n", "I\n", "RIGHT\n", "HEAD\n", "LEFT\n", "HEAD\n", "ROBIN\n", "I\n", "LEFT\n", "HEAD\n", "I\n", "RIGHT\n", "HEAD\n", "MIDDLE\n", "HEAD\n", "LEFT\n", "HEAD\n", "RIGHT\n", "HEAD\n", "MIDDLE\n", "HEAD\n", "LEFT\n", "HEAD\n", "MIDDLE\n", "HEAD\n", "LEFT\n", "HEAD\n", "I\n", "MIDDLE\n", "HEAD\n", "RIGHT\n", "HEAD\n", "LEFT\n", "HEAD\n", "MIDDLE\n", "HEAD\n", "RIGHT\n", "HEAD\n", "LEFT\n", "HEAD\n", "ALL\n", "HEADS\n", "MIDDLE\n", "HEAD\n", "RIGHT\n", "HEAD\n", "MINSTREL\n", "ROBIN\n", "MINSTREL\n", "ROBIN\n", "I\n", "MINSTREL\n", "ROBIN\n", "MINSTREL\n", "ROBIN\n", "I\n", "MINSTREL\n", "ROBIN\n", "I\n", "MINSTREL\n", "ROBIN\n", "MINSTREL\n", "ROBIN\n", "I\n", "CARTOON\n", "MONKS\n", "CARTOON\n", "CHARACTER\n", "CARTOON\n", "MONKS\n", "CARTOON\n", "CHARACTERS\n", "CARTOON\n", "MONKS\n", "CARTOON\n", "CHARACTER\n", "VOICE\n", "CARTOON\n", "CHARACTER\n", "SCENE\n", "NARRATOR\n", "GALAHAD\n", "GIRLS\n", "ZOOT\n", "GALAHAD\n", "ZOOT\n", "GALAHAD\n", "ZOOT\n", "GALAHAD\n", "ZOOT\n", "MIDGET\n", "CRAPPER\n", "O\n", "ZOOT\n", "MIDGET\n", "CRAPPER\n", "ZOOT\n", "GALAHAD\n", "I\n", "I\n", "ZOOT\n", "GALAHAD\n", "ZOOT\n", "GALAHAD\n", "ZOOT\n", "GALAHAD\n", "I\n", "ZOOT\n", "GALAHAD\n", "I\n", "I\n", "ZOOT\n", "I\n", "GALAHAD\n", "ZOOT\n", "PIGLET\n", "GALAHAD\n", "ZOOT\n", "GALAHAD\n", "B\n", "ZOOT\n", "WINSTON\n", "GALAHAD\n", "PIGLET\n", "GALAHAD\n", "PIGLET\n", "GALAHAD\n", "I\n", "PIGLET\n", "GALAHAD\n", "I\n", "PIGLET\n", "GALAHAD\n", "I\n", "I\n", "I\n", "GIRLS\n", "GALAHAD\n", "GIRLS\n", "GALAHAD\n", "DINGO\n", "I\n", "GALAHAD\n", "I\n", "DINGO\n", "GALAHAD\n", "I\n", "I\n", "DINGO\n", "GALAHAD\n", "DINGO\n", "I\n", "GALAHAD\n", "DINGO\n", "I\n", "LEFT\n", "HEAD\n", "DENNIS\n", "OLD\n", "MAN\n", "TIM\n", "THE\n", "ENCHANTER\n", "ARMY\n", "OF\n", "KNIGHTS\n", "DINGO\n", "I\n", "GOD\n", "DINGO\n", "GIRLS\n", "A\n", "A\n", "DINGO\n", "AMAZING\n", "STUNNER\n", "LOVELY\n", "DINGO\n", "GIRLS\n", "A\n", "A\n", "DINGO\n", "GIRLS\n", "GALAHAD\n", "I\n", "LAUNCELOT\n", "GALAHAD\n", "LAUNCELOT\n", "GALAHAD\n", "LAUNCELOT\n", "GALAHAD\n", "LAUNCELOT\n", "DINGO\n", "LAUNCELOT\n", "GALAHAD\n", "LAUNCELOT\n", "GALAHAD\n", "I\n", "LAUNCELOT\n", "GIRLS\n", "GALAHAD\n", "I\n", "DINGO\n", "GIRLS\n", "LAUNCELOT\n", "GALAHAD\n", "I\n", "I\n", "DINGO\n", "GIRLS\n", "LAUNCELOT\n", "GALAHAD\n", "I\n", "DINGO\n", "GIRLS\n", "DINGO\n", "LAUNCELOT\n", "GALAHAD\n", "I\n", "I\n", "LAUNCELOT\n", "GALAHAD\n", "LAUNCELOT\n", "GALAHAD\n", "I\n", "LAUNCELOT\n", "GALAHAD\n", "LAUNCELOT\n", "GALAHAD\n", "I\n", "LAUNCELOT\n", "I\n", "NARRATOR\n", "I\n", "I\n", "CROWD\n", "NARRATOR\n", "I\n", "SCENE\n", "OLD\n", "MAN\n", "ARTHUR\n", "OLD\n", "MAN\n", "ARTHUR\n", "OLD\n", "MAN\n", "ARTHUR\n", "OLD\n", "MAN\n", "ARTHUR\n", "OLD\n", "MAN\n", "ARTHUR\n", "OLD\n", "MAN\n", "ARTHUR\n", "OLD\n", "MAN\n", "SCENE\n", "HEAD\n", "KNIGHT\n", "OF\n", "NI\n", "KNIGHTS\n", "OF\n", "NI\n", "ARTHUR\n", "HEAD\n", "KNIGHT\n", "RANDOM\n", "ARTHUR\n", "HEAD\n", "KNIGHT\n", "BEDEVERE\n", "HEAD\n", "KNIGHT\n", "RANDOM\n", "ARTHUR\n", "HEAD\n", "KNIGHT\n", "ARTHUR\n", "HEAD\n", "KNIGHT\n", "KNIGHTS\n", "OF\n", "NI\n", "ARTHUR\n", "HEAD\n", "KNIGHT\n", "ARTHUR\n", "HEAD\n", "KNIGHT\n", "ARTHUR\n", "A\n", "KNIGHTS\n", "OF\n", "NI\n", "ARTHUR\n", "PARTY\n", "ARTHUR\n", "HEAD\n", "KNIGHT\n", "ARTHUR\n", "O\n", "HEAD\n", "KNIGHT\n", "ARTHUR\n", "HEAD\n", "KNIGHT\n", "ARTHUR\n", "HEAD\n", "KNIGHT\n", "CARTOON\n", "CHARACTER\n", "SUN\n", "CARTOON\n", "CHARACTER\n", "SUN\n", "CARTOON\n", "CHARACTER\n", "SUN\n", "CARTOON\n", "CHARACTER\n", "SCENE\n", "NARRATOR\n", "FATHER\n", "PRINCE\n", "HERBERT\n", "FATHER\n", "HERBERT\n", "FATHER\n", "HERBERT\n", "B\n", "I\n", "FATHER\n", "I\n", "I\n", "I\n", "I\n", "I\n", "I\n", "HERBERT\n", "I\n", "I\n", "FATHER\n", "HERBERT\n", "I\n", "FATHER\n", "I\n", "HERBERT\n", "B\n", "I\n", "FATHER\n", "HERBERT\n", "FATHER\n", "HERBERT\n", "I\n", "FATHER\n", "HERBERT\n", "I\n", "I\n", "I\n", "FATHER\n", "I\n", "GUARD\n", "GUARD\n", "FATHER\n", "I\n", "GUARD\n", "FATHER\n", "GUARD\n", "GUARD\n", "FATHER\n", "GUARD\n", "FATHER\n", "GUARD\n", "FATHER\n", "GUARD\n", "GUARD\n", "FATHER\n", "GUARD\n", "FATHER\n", "GUARD\n", "FATHER\n", "GUARD\n", "FATHER\n", "GUARD\n", "FATHER\n", "GUARD\n", "I\n", "FATHER\n", "N\n", "GUARD\n", "FATHER\n", "GUARD\n", "FATHER\n", "GUARD\n", "GUARD\n", "FATHER\n", "GUARD\n", "FATHER\n", "GUARD\n", "GUARD\n", "FATHER\n", "GUARD\n", "FATHER\n", "GUARD\n", "FATHER\n", "GUARD\n", "GUARD\n", "GUARD\n", "I\n", "FATHER\n", "GUARD\n", "GUARD\n", "FATHER\n", "GUARD\n", "FATHER\n", "I\n", "GUARD\n", "I\n", "HERBERT\n", "FATHER\n", "GUARD\n", "FATHER\n", "SCENE\n", "LAUNCELOT\n", "CONCORDE\n", "LAUNCELOT\n", "CONCORDE\n", "LAUNCELOT\n", "I\n", "I\n", "A\n", "A\n", "CONCORDE\n", "I\n", "I\n", "LAUNCELOT\n", "CONCORDE\n", "I\n", "I\n", "I\n", "I\n", "I\n", "LAUNCELOT\n", "I\n", "CONCORDE\n", "I\n", "I\n", "LAUNCELOT\n", "I\n", "I\n", "CONCORDE\n", "LAUNCELOT\n", "CONCORDE\n", "I\n", "LAUNCELOT\n", "CONCORDE\n", "I\n", "I\n", "I\n", "SCENE\n", "PRINCESS\n", "LUCKY\n", "GIRLS\n", "GUEST\n", "SENTRY\n", "SENTRY\n", "SENTRY\n", "LAUNCELOT\n", "SENTRY\n", "LAUNCELOT\n", "PRINCESS\n", "LUCKY\n", "GIRLS\n", "LAUNCELOT\n", "GUESTS\n", "LAUNCELOT\n", "GUARD\n", "LAUNCELOT\n", "O\n", "I\n", "I\n", "HERBERT\n", "LAUNCELOT\n", "I\n", "I\n", "HERBERT\n", "LAUNCELOT\n", "I\n", "HERBERT\n", "I\n", "I\n", "LAUNCELOT\n", "I\n", "HERBERT\n", "FATHER\n", "HERBERT\n", "I\n", "FATHER\n", "LAUNCELOT\n", "I\n", "HERBERT\n", "LAUNCELOT\n", "FATHER\n", "LAUNCELOT\n", "FATHER\n", "LAUNCELOT\n", "I\n", "I\n", "HERBERT\n", "I\n", "FATHER\n", "LAUNCELOT\n", "I\n", "FATHER\n", "I\n", "HERBERT\n", "FATHER\n", "LAUNCELOT\n", "I\n", "FATHER\n", "LAUNCELOT\n", "FATHER\n", "LAUNCELOT\n", "I\n", "I\n", "I\n", "FATHER\n", "HERBERT\n", "LAUNCELOT\n", "I\n", "FATHER\n", "LAUNCELOT\n", "HERBERT\n", "I\n", "FATHER\n", "LAUNCELOT\n", "HERBERT\n", "I\n", "LAUNCELOT\n", "I\n", "HERBERT\n", "LAUNCELOT\n", "I\n", "I\n", "I\n", "FATHER\n", "HERBERT\n", "SCENE\n", "GUESTS\n", "FATHER\n", "GUEST\n", "FATHER\n", "LAUNCELOT\n", "FATHER\n", "LAUNCELOT\n", "I\n", "I\n", "I\n", "GUEST\n", "GUESTS\n", "FATHER\n", "LAUNCELOT\n", "GUEST\n", "GUESTS\n", "FATHER\n", "GUESTS\n", "FATHER\n", "I\n", "I\n", "GUEST\n", "FATHER\n", "GUEST\n", "FATHER\n", "BRIDE\n", "S\n", "FATHER\n", "GUEST\n", "FATHER\n", "I\n", "I\n", "LAUNCELOT\n", "GUEST\n", "GUESTS\n", "CONCORDE\n", "HERBERT\n", "I\n", "FATHER\n", "HERBERT\n", "I\n", "FATHER\n", "HERBERT\n", "I\n", "FATHER\n", "GUESTS\n", "FATHER\n", "GUESTS\n", "FATHER\n", "GUESTS\n", "FATHER\n", "GUESTS\n", "FATHER\n", "GUESTS\n", "CONCORDE\n", "GUESTS\n", "CONCORDE\n", "GUESTS\n", "LAUNCELOT\n", "GUESTS\n", "LAUNCELOT\n", "I\n", "GUESTS\n", "CONCORDE\n", "LAUNCELOT\n", "GUESTS\n", "LAUNCELOT\n", "GUESTS\n", "LAUNCELOT\n", "SCENE\n", "ARTHUR\n", "OLD\n", "CRONE\n", "ARTHUR\n", "CRONE\n", "ARTHUR\n", "I\n", "CRONE\n", "ARTHUR\n", "CRONE\n", "ARTHUR\n", "CRONE\n", "BEDEVERE\n", "ARTHUR\n", "BEDEVERE\n", "ARTHUR\n", "BEDEVERE\n", "ARTHUR\n", "BEDEVERE\n", "ARTHUR\n", "BEDEVERE\n", "ARTHUR\n", "ARTHUR\n", "BEDEVERE\n", "CRONE\n", "BEDEVERE\n", "ARTHUR\n", "CRONE\n", "BEDEVERE\n", "ARTHUR\n", "BEDEVERE\n", "ARTHUR\n", "BEDEVERE\n", "ROGER\n", "THE\n", "SHRUBBER\n", "ARTHUR\n", "ROGER\n", "ARTHUR\n", "ROGER\n", "I\n", "I\n", "BEDEVERE\n", "ARTHUR\n", "SCENE\n", "ARTHUR\n", "O\n", "HEAD\n", "KNIGHT\n", "I\n", "ARTHUR\n", "HEAD\n", "KNIGHT\n", "KNIGHTS\n", "OF\n", "NI\n", "HEAD\n", "KNIGHT\n", "RANDOM\n", "HEAD\n", "KNIGHT\n", "ARTHUR\n", "O\n", "HEAD\n", "KNIGHT\n", "ARTHUR\n", "RANDOM\n", "HEAD\n", "KNIGHT\n", "KNIGHTS\n", "OF\n", "NI\n", "A\n", "A\n", "A\n", "HEAD\n", "KNIGHT\n", "ARTHUR\n", "HEAD\n", "KNIGHT\n", "ARTHUR\n", "KNIGHTS\n", "OF\n", "NI\n", "HEAD\n", "KNIGHT\n", "ARTHUR\n", "HEAD\n", "KNIGHT\n", "I\n", "ARTHUR\n", "KNIGHTS\n", "OF\n", "NI\n", "HEAD\n", "KNIGHT\n", "ARTHUR\n", "KNIGHTS\n", "OF\n", "NI\n", "HEAD\n", "KNIGHT\n", "KNIGHTS\n", "OF\n", "NI\n", "BEDEVERE\n", "MINSTREL\n", "ARTHUR\n", "ROBIN\n", "HEAD\n", "KNIGHT\n", "ARTHUR\n", "MINSTREL\n", "ROBIN\n", "HEAD\n", "KNIGHT\n", "KNIGHTS\n", "OF\n", "NI\n", "ROBIN\n", "I\n", "KNIGHTS\n", "OF\n", "NI\n", "ROBIN\n", "ARTHUR\n", "KNIGHTS\n", "OF\n", "NI\n", "HEAD\n", "KNIGHT\n", "ARTHUR\n", "KNIGHTS\n", "OF\n", "NI\n", "HEAD\n", "KNIGHT\n", "ARTHUR\n", "HEAD\n", "KNIGHT\n", "I\n", "I\n", "I\n", "KNIGHTS\n", "OF\n", "NI\n", "NARRATOR\n", "KNIGHTS\n", "NARRATOR\n", "MINSTREL\n", "NARRATOR\n", "KNIGHTS\n", "NARRATOR\n", "A\n", "CARTOON\n", "CHARACTER\n", "NARRATOR\n", "CARTOON\n", "CHARACTER\n", "NARRATOR\n", "CARTOON\n", "CHARACTER\n", "NARRATOR\n", "CARTOON\n", "CHARACTER\n", "NARRATOR\n", "CARTOON\n", "CHARACTER\n", "NARRATOR\n", "SCENE\n", "KNIGHTS\n", "ARTHUR\n", "TIM\n", "THE\n", "ENCHANTER\n", "I\n", "ARTHUR\n", "TIM\n", "ARTHUR\n", "TIM\n", "ARTHUR\n", "TIM\n", "I\n", "ARTHUR\n", "O\n", "TIM\n", "ROBIN\n", "ARTHUR\n", "KNIGHTS\n", "ARTHUR\n", "BEDEVERE\n", "GALAHAD\n", "ROBIN\n", "BEDEVERE\n", "ROBIN\n", "BEDEVERE\n", "ARTHUR\n", "GALAHAD\n", "ARTHUR\n", "I\n", "I\n", "TIM\n", "A\n", "ARTHUR\n", "A\n", "TIM\n", "A\n", "ARTHUR\n", "I\n", "ROBIN\n", "Y\n", "ARTHUR\n", "GALAHAD\n", "KNIGHTS\n", "TIM\n", "ROBIN\n", "ARTHUR\n", "ROBIN\n", "GALAHAD\n", "ARTHUR\n", "ROBIN\n", "KNIGHTS\n", "ARTHUR\n", "TIM\n", "I\n", "KNIGHTS\n", "TIM\n", "ARTHUR\n", "O\n", "TIM\n", "ARTHUR\n", "SCENE\n", "GALAHAD\n", "ARTHUR\n", "TIM\n", "ARTHUR\n", "GALAHAD\n", "ARTHUR\n", "W\n", "TIM\n", "ARTHUR\n", "TIM\n", "ARTHUR\n", "TIM\n", "ARTHUR\n", "TIM\n", "ARTHUR\n", "TIM\n", "ARTHUR\n", "TIM\n", "ARTHUR\n", "TIM\n", "ROBIN\n", "I\n", "I\n", "TIM\n", "GALAHAD\n", "TIM\n", "GALAHAD\n", "ROBIN\n", "TIM\n", "I\n", "ROBIN\n", "TIM\n", "ARTHUR\n", "BORS\n", "TIM\n", "BORS\n", "ARTHUR\n", "TIM\n", "I\n", "ROBIN\n", "I\n", "TIM\n", "I\n", "I\n", "ARTHUR\n", "TIM\n", "ARTHUR\n", "TIM\n", "KNIGHTS\n", "KNIGHTS\n", "ARTHUR\n", "KNIGHTS\n", "TIM\n", "ARTHUR\n", "LAUNCELOT\n", "GALAHAD\n", "ARTHUR\n", "GALAHAD\n", "ARTHUR\n", "ROBIN\n", "ARTHUR\n", "GALAHAD\n", "ARTHUR\n", "GALAHAD\n", "LAUNCELOT\n", "ARTHUR\n", "LAUNCELOT\n", "ARTHUR\n", "MONKS\n", "ARTHUR\n", "LAUNCELOT\n", "I\n", "ARTHUR\n", "BROTHER\n", "MAYNARD\n", "SECOND\n", "BROTHER\n", "O\n", "MAYNARD\n", "SECOND\n", "BROTHER\n", "MAYNARD\n", "KNIGHTS\n", "ARTHUR\n", "GALAHAD\n", "ARTHUR\n", "SCENE\n", "ARTHUR\n", "LAUNCELOT\n", "GALAHAD\n", "ARTHUR\n", "MAYNARD\n", "GALAHAD\n", "LAUNCELOT\n", "ARTHUR\n", "MAYNARD\n", "ARTHUR\n", "MAYNARD\n", "BEDEVERE\n", "MAYNARD\n", "LAUNCELOT\n", "MAYNARD\n", "ARTHUR\n", "MAYNARD\n", "GALAHAD\n", "ARTHUR\n", "MAYNARD\n", "LAUNCELOT\n", "ARTHUR\n", "BEDEVERE\n", "GALAHAD\n", "BEDEVERE\n", "I\n", "LAUNCELOT\n", "ARTHUR\n", "LAUNCELOT\n", "KNIGHTS\n", "BEDEVERE\n", "LAUNCELOT\n", "BEDEVERE\n", "N\n", "LAUNCELOT\n", "BEDEVERE\n", "I\n", "ARTHUR\n", "GALAHAD\n", "MAYNARD\n", "BROTHER\n", "MAYNARD\n", "BEDEVERE\n", "ARTHUR\n", "KNIGHTS\n", "BEDEVERE\n", "KNIGHTS\n", "NARRATOR\n", "ANIMATOR\n", "NARRATOR\n", "SCENE\n", "GALAHAD\n", "ARTHUR\n", "ROBIN\n", "ARTHUR\n", "BEDEVERE\n", "ARTHUR\n", "GALAHAD\n", "ARTHUR\n", "GALAHAD\n", "ARTHUR\n", "ROBIN\n", "ARTHUR\n", "ROBIN\n", "I\n", "GALAHAD\n", "ARTHUR\n", "ROBIN\n", "ARTHUR\n", "ROBIN\n", "I\n", "LAUNCELOT\n", "I\n", "I\n", "ARTHUR\n", "GALAHAD\n", "ARTHUR\n", "LAUNCELOT\n", "I\n", "ARTHUR\n", "BRIDGEKEEPER\n", "LAUNCELOT\n", "I\n", "BRIDGEKEEPER\n", "LAUNCELOT\n", "BRIDGEKEEPER\n", "LAUNCELOT\n", "BRIDGEKEEPER\n", "LAUNCELOT\n", "BRIDGEKEEPER\n", "LAUNCELOT\n", "ROBIN\n", "BRIDGEKEEPER\n", "ROBIN\n", "I\n", "BRIDGEKEEPER\n", "ROBIN\n", "BRIDGEKEEPER\n", "ROBIN\n", "BRIDGEKEEPER\n", "ROBIN\n", "I\n", "BRIDGEKEEPER\n", "GALAHAD\n", "BRIDGEKEEPER\n", "GALAHAD\n", "I\n", "BRIDGEKEEPER\n", "GALAHAD\n", "BRIDGEKEEPER\n", "ARTHUR\n", "BRIDGEKEEPER\n", "ARTHUR\n", "BRIDGEKEEPER\n", "ARTHUR\n", "BRIDGEKEEPER\n", "I\n", "I\n", "BEDEVERE\n", "ARTHUR\n", "SCENE\n", "ARTHUR\n", "BEDEVERE\n", "ARTHUR\n", "BEDEVERE\n", "ARTHUR\n", "FRENCH\n", "GUARD\n", "ARTHUR\n", "I\n", "FRENCH\n", "GUARD\n", "I\n", "I\n", "ARTHUR\n", "FRENCH\n", "GUARD\n", "I\n", "ARTHUR\n", "FRENCH\n", "GUARDS\n", "ARTHUR\n", "FRENCH\n", "GUARD\n", "ARTHUR\n", "FRENCH\n", "GUARD\n", "FRENCH\n", "GUARDS\n", "ARTHUR\n", "BEDEVERE\n", "ARTHUR\n", "FRENCH\n", "GUARDS\n", "ARTHUR\n", "FRENCH\n", "GUARDS\n", "ARTHUR\n", "FRENCH\n", "GUARDS\n", "ARTHUR\n", "ARMY\n", "OF\n", "KNIGHTS\n", "HISTORIAN\n", "S\n", "WIFE\n", "I\n", "INSPECTOR\n", "OFFICER\n", "HISTORIAN\n", "S\n", "WIFE\n", "OFFICER\n", "INSPECTOR\n", "OFFICER\n", "BEDEVERE\n", "INSPECTOR\n", "OFFICER\n", "INSPECTOR\n", "OFFICER\n", "OFFICER\n", "RANDOM\n", "RANDOM\n", "OFFICER\n", "OFFICER\n", "OFFICER\n", "OFFICER\n", "INSPECTOR\n", "OFFICER\n", "CAMERAMAN\n"]}], "source": ["words = [w for w in text6 if w.isupper()]             # store the words in a list\n", "for w in words:                                       # print all the uppercase words, one per line\n", "    print(w)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**24. Write expressions for finding all words in ** `text6` ** that meet the conditions listed below. The result should be in the form of a list of words: ** `['word1', 'word2', ...]`.  \n", "a. **Ending in ** *ize*  \n", "b. **Containing the letter** *z*  \n", "c. **Containing the sequence of letters** *pz*  \n", "d. **Having all lowercase letters except for an initial capital (i.e., titlecase)**  "]}, {"cell_type": "code", "execution_count": 24, "metadata": {"collapsed": true}, "outputs": [], "source": ["a = [w for w in text6 if w.endswith('ize')]\n", "b = [w for w in text6 if 'z' in w]\n", "c = [w for w in text6 if 'pt' in w]\n", "d = [w for w in text6 if w.istitle()]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**25. Define ** `sent` ** to be the list of words ** `['she', 'sells', 'sea', 'shells', 'by', 'the', 'sea', 'shore']`**. Now write code to perform the following tasks:**  \n", "a. **Print all words beginning with ** *sh*  \n", "b. **Print all words longer than four characters**  "]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['she', 'shells', 'shore']\n", "['sells', 'shells', 'shore']\n"]}], "source": ["sent = ['she', 'sells', 'sea', 'shells', 'by', 'the', 'sea', 'shore']\n", "print([w for w in sent if w.startswith('sh')])\n", "print([w for w in sent if len(w) > 4])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**26. What does the following Python code do?  ** `sum(len(w) for w in text1)` ** Can you use it to work out the average word length of a text?**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Count the number of characters."]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2024-12-01T01:25:49.025685Z", "start_time": "2024-12-01T01:25:49.020762Z"}}, "source": ["total_length = sum(len(w) for w in text1)\n", "total_words = len(text1)\n", "average_word_length = total_length / total_words"], "outputs": [], "execution_count": 8}, {"cell_type": "markdown", "metadata": {"ExecuteTime": {"end_time": "2024-12-01T01:25:57.792012Z", "start_time": "2024-12-01T01:25:57.783346Z"}}, "source": ["**27. Define a function called ** `vocab_size(text)`** that has a single parameter for the text, and which returns the vocabulary size of the text.**"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"collapsed": true}, "outputs": [], "source": ["def vocab_size(text):\n", "    return len(set(text))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**28. Define a function ** `percent(word, text)` ** that calculates how often a given word occurs in a text, and expresses the result as a percentage.**"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"collapsed": true}, "outputs": [], "source": ["def percent(word, text):\n", "    return 100 * text.count(word) / len(text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**29. We have been using sets to store vocabularies. Try the following Python expression: ** `set(sent3) < set(text1)` **. Experiment with this using different arguments to ** `set()` **. What does it do? Can you think of a practical application for this?**"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["set(sent3) < set(text1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To test whether the first set is the second set's subset.\n", "Given a sentence, check whether it's spoken by someone if given that one's quotations."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}, "vscode": {"interpreter": {"hash": "3c6586f27f47a5d8f4efe58460ae93ee0dba0e4d02862306b1516a28fc1f142d"}}}, "nbformat": 4, "nbformat_minor": 2}