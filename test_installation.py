"""
Test script to verify the LLM QA System installation
"""

import sys
import traceback
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()


def test_imports():
    """Test if all required modules can be imported"""
    console.print("[bold blue]🔍 Testing imports...[/bold blue]")
    
    tests = []
    
    # Test standard library imports
    try:
        import json, os, logging, time
        tests.append(("Standard library", "✅", "green"))
    except ImportError as e:
        tests.append(("Standard library", f"❌ {e}", "red"))
    
    # Test third-party imports
    try:
        import requests, typer, rich
        tests.append(("CLI dependencies", "✅", "green"))
    except ImportError as e:
        tests.append(("CLI dependencies", f"❌ {e}", "red"))
    
    try:
        import chromadb, sentence_transformers
        tests.append(("Search dependencies", "✅", "green"))
    except ImportError as e:
        tests.append(("Search dependencies", f"❌ {e}", "red"))
    
    try:
        import numpy, pandas
        tests.append(("Data processing", "✅", "green"))
    except ImportError as e:
        tests.append(("Data processing", f"❌ {e}", "red"))
    
    # Test project imports
    try:
        from llm_qa_system import (
            OllamaClient, SemanticSearchEngine, 
            QuestionAnsweringEngine, QAGenerator, CodebaseIndexer
        )
        tests.append(("Project modules", "✅", "green"))
    except ImportError as e:
        tests.append(("Project modules", f"❌ {e}", "red"))
    
    # Display results
    table = Table(title="Import Tests")
    table.add_column("Component", style="cyan")
    table.add_column("Status", style="white")
    
    for component, status, color in tests:
        table.add_row(component, f"[{color}]{status}[/{color}]")
    
    console.print(table)
    
    # Return True if all tests passed
    return all("✅" in test[1] for test in tests)


def test_ollama_connection():
    """Test Ollama connection"""
    console.print("\n[bold blue]🔗 Testing Ollama connection...[/bold blue]")
    
    try:
        from llm_qa_system import OllamaClient
        
        client = OllamaClient()
        
        if client.check_connection():
            console.print("[green]✅ Ollama connection successful[/green]")
            
            # Test model availability
            models = client.list_models()
            if models:
                console.print(f"[green]✅ Found {len(models)} models:[/green]")
                for model in models[:3]:  # Show first 3
                    console.print(f"  - {model.get('name', 'Unknown')}")
                return True
            else:
                console.print("[yellow]⚠️  No models found. Run 'ollama pull llama3.2'[/yellow]")
                return False
        else:
            console.print("[red]❌ Cannot connect to Ollama[/red]")
            console.print("[yellow]Make sure Ollama is running: ollama serve[/yellow]")
            return False
            
    except Exception as e:
        console.print(f"[red]❌ Ollama test failed: {e}[/red]")
        return False


def test_semantic_search():
    """Test semantic search initialization"""
    console.print("\n[bold blue]🔍 Testing semantic search...[/bold blue]")
    
    try:
        from llm_qa_system import SemanticSearchEngine
        
        # Initialize search engine
        search_engine = SemanticSearchEngine()
        console.print("[green]✅ Semantic search engine initialized[/green]")
        
        # Test collection stats
        stats = search_engine.get_collection_stats()
        console.print(f"[green]✅ Collection stats: {stats.get('total_chunks', 0)} chunks[/green]")
        
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Semantic search test failed: {e}[/red]")
        console.print(f"[dim]Error details: {traceback.format_exc()}[/dim]")
        return False


def test_file_structure():
    """Test if required files and directories exist"""
    console.print("\n[bold blue]📁 Testing file structure...[/bold blue]")
    
    required_files = [
        "main.py",
        "config.py", 
        "requirements.txt",
        "README.md",
        "llm_qa_system/__init__.py",
        "llm_qa_system/ollama_client.py",
        "llm_qa_system/semantic_search.py",
        "llm_qa_system/qa_engine.py",
        "llm_qa_system/qa_generator.py",
        "llm_qa_system/codebase_indexer.py"
    ]
    
    required_dirs = [
        "llm_qa_system",
        "data",
        "logs"
    ]
    
    tests = []
    
    # Check files
    for file_path in required_files:
        if Path(file_path).exists():
            tests.append((file_path, "✅", "green"))
        else:
            tests.append((file_path, "❌", "red"))
    
    # Check directories
    for dir_path in required_dirs:
        if Path(dir_path).exists() and Path(dir_path).is_dir():
            tests.append((f"{dir_path}/", "✅", "green"))
        else:
            tests.append((f"{dir_path}/", "❌", "red"))
    
    # Display results
    table = Table(title="File Structure")
    table.add_column("Path", style="cyan")
    table.add_column("Status", style="white")
    
    for path, status, color in tests:
        table.add_row(path, f"[{color}]{status}[/{color}]")
    
    console.print(table)
    
    return all("✅" in test[1] for test in tests)


def test_basic_functionality():
    """Test basic system functionality"""
    console.print("\n[bold blue]⚙️  Testing basic functionality...[/bold blue]")
    
    try:
        from llm_qa_system import CodebaseIndexer
        
        # Test indexer
        indexer = CodebaseIndexer(".")
        console.print("[green]✅ Codebase indexer initialized[/green]")
        
        # Test file scanning (just count, don't index)
        file_count = 0
        for file_path in indexer.scan_codebase():
            file_count += 1
            if file_count >= 5:  # Just test first 5 files
                break
        
        console.print(f"[green]✅ Found {file_count}+ files to index[/green]")
        
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Basic functionality test failed: {e}[/red]")
        return False


def main():
    """Run all tests"""
    console.print(Panel(
        "[bold green]🧪 LLM QA System - Installation Test[/bold green]\n"
        "This script verifies that the system is properly installed and configured.",
        title="Installation Test",
        border_style="blue"
    ))
    
    # Run all tests
    tests = [
        ("File Structure", test_file_structure),
        ("Python Imports", test_imports),
        ("Ollama Connection", test_ollama_connection),
        ("Semantic Search", test_semantic_search),
        ("Basic Functionality", test_basic_functionality)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        console.print(f"\n{'='*50}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            console.print(f"[red]❌ {test_name} test crashed: {e}[/red]")
            results.append((test_name, False))
    
    # Summary
    console.print(f"\n{'='*50}")
    console.print("[bold blue]📊 Test Summary:[/bold blue]")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    summary_table = Table(title="Test Results")
    summary_table.add_column("Test", style="cyan")
    summary_table.add_column("Result", style="white")
    
    for test_name, result in results:
        status = "[green]✅ PASS[/green]" if result else "[red]❌ FAIL[/red]"
        summary_table.add_row(test_name, status)
    
    console.print(summary_table)
    
    # Final verdict
    if passed == total:
        console.print(Panel(
            f"[bold green]🎉 All tests passed! ({passed}/{total})[/bold green]\n\n"
            "[bold blue]Your system is ready to use:[/bold blue]\n"
            "• Run: [green]python main.py index[/green] to index your codebase\n"
            "• Run: [green]python main.py interactive[/green] for Q&A\n"
            "• Run: [green]python examples/demo.py[/green] for a full demo",
            title="✅ Installation Successful",
            border_style="green"
        ))
    else:
        console.print(Panel(
            f"[bold red]❌ {total - passed} test(s) failed ({passed}/{total} passed)[/bold red]\n\n"
            "[bold blue]Common fixes:[/bold blue]\n"
            "• Install dependencies: [yellow]pip install -r requirements.txt[/yellow]\n"
            "• Start Ollama: [yellow]ollama serve[/yellow]\n"
            "• Pull a model: [yellow]ollama pull llama3.2[/yellow]\n"
            "• Run setup: [yellow]python setup.py[/yellow]",
            title="❌ Installation Issues",
            border_style="red"
        ))
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
