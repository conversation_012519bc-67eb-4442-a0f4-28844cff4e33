"""
Ollama API Client for local LLM integration
"""

import json
import logging
import time
from typing import Dict, List, Optional, Any, Generator
import requests
import aiohttp
import asyncio
from config import OLLAMA_CONFIG

logger = logging.getLogger(__name__)


class OllamaClient:
    """Client for communicating with local Ollama service"""
    
    def __init__(self, base_url: str = None, model: str = None):
        """
        Initialize Ollama client
        
        Args:
            base_url: Ollama server URL (default from config)
            model: Default model to use (default from config)
        """
        self.base_url = base_url or OLLAMA_CONFIG["base_url"]
        self.default_model = model or OLLAMA_CONFIG["default_model"]
        self.timeout = OLLAMA_CONFIG["timeout"]
        self.max_retries = OLLAMA_CONFIG["max_retries"]
        
    def _make_request(self, endpoint: str, data: Dict[str, Any], stream: bool = False) -> requests.Response:
        """Make HTTP request to Ollama API with retry logic"""
        url = f"{self.base_url}/{endpoint}"
        
        for attempt in range(self.max_retries):
            try:
                response = requests.post(
                    url,
                    json=data,
                    timeout=self.timeout,
                    stream=stream
                )
                response.raise_for_status()
                return response
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"Request attempt {attempt + 1} failed: {e}")
                if attempt == self.max_retries - 1:
                    raise
                time.sleep(2 ** attempt)  # Exponential backoff
                
    def check_connection(self) -> bool:
        """Check if Ollama service is available"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except requests.exceptions.RequestException:
            return False
            
    def list_models(self) -> List[Dict[str, Any]]:
        """Get list of available models"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=self.timeout)
            response.raise_for_status()
            return response.json().get("models", [])
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to list models: {e}")
            return []
            
    def pull_model(self, model_name: str) -> bool:
        """Pull a model if not available locally"""
        try:
            data = {"name": model_name}
            response = self._make_request("api/pull", data, stream=True)
            
            # Process streaming response
            for line in response.iter_lines():
                if line:
                    status = json.loads(line)
                    if "status" in status:
                        logger.info(f"Pull status: {status['status']}")
                        
            return True
        except Exception as e:
            logger.error(f"Failed to pull model {model_name}: {e}")
            return False
            
    def generate(self, prompt: str, model: str = None, **kwargs) -> str:
        """
        Generate text completion
        
        Args:
            prompt: Input prompt
            model: Model to use (default: self.default_model)
            **kwargs: Additional generation parameters
            
        Returns:
            Generated text response
        """
        model = model or self.default_model
        
        data = {
            "model": model,
            "prompt": prompt,
            "stream": False,
            **kwargs
        }
        
        try:
            response = self._make_request("api/generate", data)
            result = response.json()
            return result.get("response", "")
            
        except Exception as e:
            logger.error(f"Generation failed: {e}")
            raise
            
    def chat(self, messages: List[Dict[str, str]], model: str = None, **kwargs) -> str:
        """
        Chat completion with conversation history
        
        Args:
            messages: List of message dicts with 'role' and 'content'
            model: Model to use (default: self.default_model)
            **kwargs: Additional chat parameters
            
        Returns:
            Assistant's response
        """
        model = model or self.default_model
        
        data = {
            "model": model,
            "messages": messages,
            "stream": False,
            **kwargs
        }
        
        try:
            response = self._make_request("api/chat", data)
            result = response.json()
            return result.get("message", {}).get("content", "")
            
        except Exception as e:
            logger.error(f"Chat failed: {e}")
            raise
            
    def stream_generate(self, prompt: str, model: str = None, **kwargs) -> Generator[str, None, None]:
        """
        Stream text generation
        
        Args:
            prompt: Input prompt
            model: Model to use
            **kwargs: Additional parameters
            
        Yields:
            Chunks of generated text
        """
        model = model or self.default_model
        
        data = {
            "model": model,
            "prompt": prompt,
            "stream": True,
            **kwargs
        }
        
        try:
            response = self._make_request("api/generate", data, stream=True)
            
            for line in response.iter_lines():
                if line:
                    chunk = json.loads(line)
                    if "response" in chunk:
                        yield chunk["response"]
                        
        except Exception as e:
            logger.error(f"Streaming generation failed: {e}")
            raise
            
    async def async_generate(self, prompt: str, model: str = None, **kwargs) -> str:
        """Async version of generate method"""
        model = model or self.default_model
        
        data = {
            "model": model,
            "prompt": prompt,
            "stream": False,
            **kwargs
        }
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(
                    f"{self.base_url}/api/generate",
                    json=data,
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                ) as response:
                    response.raise_for_status()
                    result = await response.json()
                    return result.get("response", "")
                    
            except Exception as e:
                logger.error(f"Async generation failed: {e}")
                raise
                
    def ensure_model_available(self, model_name: str = None) -> bool:
        """Ensure a model is available, pull if necessary"""
        model_name = model_name or self.default_model
        
        # Check if model exists
        models = self.list_models()
        model_names = [m.get("name", "") for m in models]
        
        if model_name in model_names:
            logger.info(f"Model {model_name} is available")
            return True
            
        logger.info(f"Model {model_name} not found, attempting to pull...")
        return self.pull_model(model_name)
