#Q1
#Question 1: (2 marks)
#Define s as a text
#Write expressions for finding all words in s that contain the sequence of letters pt
#The result should be in the form of a list of words: ['word1', 'word2', ...].

# import nltk
#
# # Define 's' as a text
# s = input("Enter the text: ")
#
# # Tokenize the text into words
# words = nltk.word_tokenize(s)
#
# # Find words containing the sequence 'pt'
# result = [word for word in words if 'pt' in word]
#
# # Print the result
# print(result)

#Q2
#Define a function percent(word, text) that calculates how often a given word occurs
# in a text and expresses the result as a percentage.
# import nltk
#
#
# def percent(word, text):
#     # Tokenize the text into words
#     words = nltk.word_tokenize(text)
#
#     # Count the occurrences of the given word
#     word_count = sum(1 for w in words if w.lower() == word.lower())
#
#     # Calculate the percentage
#     percentage = (word_count / len(words)) * 100
#
#     return percentage
#
#
# # Example usage
# text = input("Enter the text: ")
# word = input("Enter the word: ")
# result = percent(word, text)
#
# print(f"The word '{word}' occurs {result:.2f}% of the time in the given text.")




#####Q.3
#Write a Python NLTK program to split the text sentence/paragraph into a list of words. Sample Output:
#Original string:
#Joe waited for the train. The train was late. Mary and Samantha took the bus. I looked for Mary and Samantha at the bus station.
#Sentence-tokenized copy in a list:
#['Joe waited for the train.', 'The train was late.', 'Mary and Samantha took the bus.', 'I
#looked for Mary and Samantha at the bus station.']
#Read the list:
#Joe waited for the train.
#The train was late.
#Mary and Samantha took the bus.
#I looked for Mary and Samantha at the bus station.


# import nltk
#
#
# def split_text(text):
#     # Tokenize the text into sentences
#     sentences = nltk.sent_tokenize(text)
#
#     # Return the sentence-tokenized copy in a list
#     return sentences
#
#
# # Example usage
# text = "The new <NAME_EMAIL>, <EMAIL>. If you find any disruptions,<NAME_EMAIL> or <EMAIL>"
#
# # Split the text into sentences
# sentence_list = split_text(text)
#
# # Print the original string
# print(sentence_list)


# import nltk
# from nltk.tokenize import wordpunct_tokenize
#
# text = "The new <NAME_EMAIL>, <EMAIL>. If you find any disruptions,<NAME_EMAIL> or <EMAIL>"
# tokenized_text = wordpunct_tokenize(text)
#
# print(tokenized_text)
# #
# #
# # # Print the sentence-tokenized copy in a list
# # print("Sentence-tokenized copy in a list:")
# # print(sentence_list)
# #
# # # Print the list
# # print("Read the list:")
# # for sentence in sentence_list:
# #     print(sentence)


#Q4
#Extract the Verb Phrases from the given text
#Input:
#text = ("I may bake a cake for my birthday. The talk will introduce reader about Use of baking")
#Desired Output:
#may bake
#will introduce

# import nltk
# from nltk import pos_tag, ne_chunk
# from nltk.tokenize import word_tokenize
# from nltk.chunk import tree2conlltags
#
# text = "I may bake a cake for my birthday. The talk will introduce reader about Use of baking"
# tokens = word_tokenize(text)
#
# tagged_words = pos_tag(tokens)
# grammar = "VP: {<MD><VB>}"
# vp_parser = nltk.RegexpParser(grammar)
# tree = vp_parser.parse(tagged_words)
#
# verb_phrases = []
# for subtree in tree.subtrees():
#     if subtree.label() == 'VP':
#         verb_phrase = ' '.join(word for word, tag in subtree.leaves())
#         verb_phrases.append(verb_phrase)
# for verb_phrase in verb_phrases:
#     print(verb_phrase)
#


