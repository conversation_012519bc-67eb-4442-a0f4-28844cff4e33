""""Question 4: (3 marks)
Input:
Replace the pronouns in below text by the respective object names
Input: text=" My sister has a dog and she loves him"
Desired Output:
[My sister,she]
[a dog,him ]    """
import nltk
from nltk.tokenize import word_tokenize

def replace_pronouns_with_objects(text):
    # Define the mapping of object names to pronouns
    replacements = {
        "she": "My sister",
        "him": "a dog"
    }

    # Tokenize the original text (split it into words)
    words = text.split()

    # Lists to store mappings for the output
    subject_mappings = []
    object_mappings = []

    # Iterate through the words and replace pronouns where appropriate
    for i, word in enumerate(words):
        if word.lower() == "she":
            subject_mappings.append(['My sister', 'she'])
        elif word.lower() == "him":
            object_mappings.append(['a dog', 'him'])

    return subject_mappings, object_mappings


text = "My sister has a dog and she loves him"
subject_mappings, object_mappings = replace_pronouns_with_objects(text)

print(subject_mappings)
print(object_mappings)
