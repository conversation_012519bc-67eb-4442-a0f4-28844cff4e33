""""Question 1: (2 marks)
Write regular expressions to match the following classes of strings:
1. An arithmetic expression using integers, addition, and multiplication, such as 5*6+9
2. A single determiner (assume that a, an, and the ** are the only determiners). """

#Q1-2
import nltk
nltk.download('averaged_perceptron_tagger')

# Prompt the user to enter a sentence
sentence = input("Please enter a sentence: ")

# Tokenize the input sentence
tokens = nltk.word_tokenize(sentence)

# Tag each token with its part of speech
pos_tags = nltk.pos_tag(tokens)

# Extract determiners from the tagged tokens
determiners = [word for word, tag in pos_tags if tag in ('DT',)]

# Print the list of determiners
print("Determiners found:", determiners)

#test: Please enter a sentence: The quick brown fox jumps over a lazy dog
#output: Determiners found: ['The', 'a']