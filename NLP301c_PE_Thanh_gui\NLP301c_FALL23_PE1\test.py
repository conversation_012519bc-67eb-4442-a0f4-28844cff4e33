# #Cau 1:
# import re
#
# # Define the regular expression pattern
# pattern = r'^\d+(\s*[\+\*]\s*\d+)*$'
# regex = re.compile(pattern)
#
# # Read input from the keyboard
# user_input = input("Enter an arithmetic expression: ")
#
# # Check if the input matches the pattern
# if regex.match(user_input):
#     print("The input is a valid arithmetic expression.")
# else:
#     print("The input is NOT a valid arithmetic expression.")
#
# #test: Enter an arithmetic expression: 5*6+9
# #output: The input is a valid arithmetic expression.

# Cau 2:
# from nltk.tokenize import word_tokenize
#
# s = ' this is. \n a sample \t. sentence.'
#
# # Step 1: Tokenize the string to split it into words and punctuation
# tokens = word_tokenize(s)
#
# # Step 2: Filter out unwanted punctuation and join tokens back into a single string with a single space between each word
# words_only = [word for word in tokens if word.isalnum()]
#
# # Step 3: Normalize the whitespace between words
# normalized_string = ' '.join(words_only)
#
# # Step 4: Add a period at the end of the sentence, if not already present
# if not normalized_string.endswith('.'):
#     normalized_string += '.'
#
# print("Normalized string:", normalized_string)

#Cau 3:
# from collections import Counter
# import re
#
# def shorten(text, n):
#     # Tokenize the text, preserving the case and ignoring punctuation
#     words = re.findall(r'\b\w+\b', text)
#
#     # Count the frequency of each word
#     word_counts = Counter(words)
#
#     # Identify the n most frequently occurring words
#     most_common_words = set([word for word, count in word_counts.most_common(n)])
#
#     # Reconstruct the text without the n most frequently occurring words
#     filtered_words = [word for word in words if word not in most_common_words]
#
#     # Join the remaining words into a single string
#     shortened_text = ' '.join(filtered_words)
#
#     return shortened_text
#
# # Example usage
# text = 'Write a function shorten(text, n) to process a text, omitting the n most frequently occurring words of the text. How readable is it?'
# output = shorten(text, 4)
# print(output)

# Cau 4:

# text = "Walter was feeling anxious. He was diagnosed today. He probably is the best person I know."
#
# stop_words_and_delims = ['was', 'is', 'the', '.', ',', '-', '!', '?']
# for r in stop_words_and_delims:
#     text = text.replace(r, 'DELIM')
#
# words = [t.strip() for t in text.split('DELIM')]
# words_filtered = list(filter(lambda a: a not in [''], words))
#
# print(words_filtered)