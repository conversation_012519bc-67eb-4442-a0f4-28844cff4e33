#Q1
#Define s as a text
#Write expressions for finding all words in s that all lowercase letters
#The result should be in the form of a list of words: ['word1', 'word2', ...].

# import nltk
# nltk.download('punkt')  # Download necessary resources
#
# from nltk.tokenize import word_tokenize
#
# # Define your text
# s = "This is a sample text with some Words in it."
#
# # Tokenize the text into words
# words = word_tokenize(s)
# lowercase_words = [word for word in words if word.islower()]
#
# print(lowercase_words)
# #


#Q2
#Find all the four-letter words in the Chat Corpus (text5).
# With the help of a frequency distribution (FreqDist),
# show these words in decreasing order of frequency.

#First, import the necessary modules from NLTK:
# import nltk
# from nltk.corpus import webtext
# from nltk import FreqDist
# #Load the Chat Corpus (text5) using the webtext corpus from NLTK:
# chat_corpus = nltk.corpus.webtext.words("firefox.txt")
# #Use a list comprehension to filter out all the four-letter words from the corpus:
# four_letter_words = [word for word in chat_corpus if len(word) == 4]
# #Create a frequency distribution object from the filtered four-letter words:
# fdist = FreqDist(four_letter_words)
# #Finally, display the four-letter words in decreasing order of frequency using the most_common() method of the fdist object:
# for word, frequency in fdist.most_common():
#     print(f"{word}: {frequency}")
#


#Q3
#Write a Python NLTK program to create a list of words from a given string.
#Sample Output:
#Original string:
#Joe waited for the train. The train was late. Mary and Samantha took the bus. I looked for Mary and Samantha at the bus station.
#List of words:
#['Joe', 'waited', 'for', 'the', 'train', '', 'The', 'train', 'was', 'late', '', 'Mary', 'and', 'Samantha', 'took', 'the', 'bus', '', 'T', 'looked', 'for', 'Mary', 'and', 'Samantha', 'at', 'the', 'bus', 'station', '.7


# import nltk
#
#
# def create_word_list(string):
#     # Tokenize the string into words
#     words = nltk.word_tokenize(string)
#
#     # Return the list of words
#     return words
#
#
# # Define the input string
# input_string = "Joe waited for the train. The train was late. Mary and Samantha took the bus. I looked for Mary and Samantha at the bus station."
#
# # Call the function to create the word list
# word_list = create_word_list(input_string)
#
# # Print the original string
# print("Original string:")
# print(input_string)
#
# # Print the list of words
# print("List of words:")
# print(word_list)


#Q4
#Merge the first name and last name as single token in the given sentence Input:
#text="Robert Langdon is a famous character in various books and movies" Desired Output:
#Robert Langdon
#is
#a
#famous
#character
# in
# various
# books
# and

#Import the necessary libraries:
# import nltk
# from nltk.tokenize import word_tokenize
#
# #Tokenize the given sentence into individual words:
# text = "Robert Langdon is a famous character in various books and movies"
# tokens = word_tokenize(text)
# #Merge the first name and last name into a single token:
#
# merged_tokens = []
# i = 0
# while i < len(tokens):
#     if i + 1 < len(tokens) and tokens[i + 1].istitle():  # Check if the next token starts with a capital letter
#         merged_tokens.append(tokens[i] + " " + tokens[i + 1])
#         i += 2  # Skip the next token since it has already been merged
#     else:
#         merged_tokens.append(tokens[i])
#         i += 1
# #Print the desired output:
# for token in merged_tokens:
#     print(token)
#
#
#
#
#
