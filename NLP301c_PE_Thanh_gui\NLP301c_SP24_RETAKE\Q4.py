""""Question 4: (3 marks)
Write code to initialize a two-dimensional array of sets called word nonvowels and process
a list of words, adding each word to word _nonvowels[|][n] where l is the length of the word
and n is the number of non-vowels it contains. Print l and v
Example:
Input: word list = 'Write code to initialize an array and process a list of words' """

import nltk
from nltk.tokenize import word_tokenize
import string

# Download the required NLTK resources if not already downloaded
nltk.download('punkt')


# Function to count non-vowel characters in a word
def count_non_vowels(word):
    vowels = 'aeiou'
    return sum(1 for char in word if char.lower() not in vowels and char.isalpha())


# Initialize a two-dimensional array of sets
def initialize_word_nonvowels(max_length):
    # Initialize a 2D array where each element is a set
    return [[set() for _ in range(max_length + 1)] for _ in range(max_length + 1)]


# Process the list of words
def process_words(word_list):
    words = word_tokenize(word_list)
    words = [word for word in words if word.isalpha()]  # Filter out punctuation
    max_length = max(len(word) for word in words)  # Find maximum length of words
    word_nonvowels = initialize_word_nonvowels(max_length)

    for word in words:
        l = len(word)
        n = count_non_vowels(word)
        word_nonvowels[l][n].add(word)
        print(f"Word: '{word}' | Length: {l} | Non-vowels: {n}")

    return word_nonvowels


# Example usage
word_list = 'Write code to initialize an array and process a list of words'
word_nonvowels = process_words(word_list)

# Print the content of word_nonvowels for demonstration
for length in range(len(word_nonvowels)):
    for nonvowels in range(len(word_nonvowels[length])):
        if word_nonvowels[length][nonvowels]:
            print(f"Length {length}, Non-vowels {nonvowels}: {word_nonvowels[length][nonvowels]}")
