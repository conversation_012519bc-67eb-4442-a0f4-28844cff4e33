""""Question 1: (2 marks)
Write code to convert nationality adjectives like Canadian and Australian to their
corresponding nouns Canada and Australia
Example:
Input:
['Argentinian', 'Australian', ' Canadian']
Output:
('Argentina', 'Australia', 'Canada']"""


# ... existing imports or other necessary context ...

# ... existing imports or other necessary context ...

def convert_noun(nationality_adjectives):
    nationality_dict = {
        'Canadian': 'Canada',
        'Australian': 'Australia',
        'Argentinian': 'Argentina',
    }
    
    converted_nouns = tuple(nationality_dict.get(adj, adj) for adj in nationality_adjectives)
    return converted_nouns

# Example usage:
nationalities = ['Argentinian', 'Australian', 'Canadian']
nouns = convert_noun(nationalities)
print(nouns)  # Output: ('Argentina', 'Australia', 'Canada')