""""Write a Python NLTK program to tokenize words, sentence wise.
Sample Output:
Original string.
<PERSON> waited for the train. The train was late. <PERSON> and <PERSON> took the bus. I looked for
<PERSON> and <PERSON> at the bus station.
Tokenize words sentence wise:
Read the list:
['<PERSON>', 'waited', 'for', 'the', 'train', '']
['The', 'train', 'was', 'late', '']
['<PERSON>', 'and", '<PERSON>', took', "the', "bus', '
['T', 'looked", 'for', '<PERSON>', 'and", '<PERSON>', 'at', 'the', 'bus', 'station', !']."""
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize

# Sample text
text = ("<PERSON> waited for the train. The train was late. <PERSON> and <PERSON> took the bus. "
        "I looked for <PERSON> and <PERSON> at the bus station.")

# Tokenize text into sentences
sentences = sent_tokenize(text)

# Tokenize each sentence into words
tokenized_sentences = [word_tokenize(sentence) for sentence in sentences]

# Print the tokenized words, sentence-wise
print("Original string.")
print(text)
print("Tokenize words sentence wise:")
for sentence in tokenized_sentences:
    print(sentence)
