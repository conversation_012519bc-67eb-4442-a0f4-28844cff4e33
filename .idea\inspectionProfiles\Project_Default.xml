<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="13">
            <item index="0" class="java.lang.String" itemvalue="transformers" />
            <item index="1" class="java.lang.String" itemvalue="basicsr" />
            <item index="2" class="java.lang.String" itemvalue="gradio" />
            <item index="3" class="java.lang.String" itemvalue="diffusers" />
            <item index="4" class="java.lang.String" itemvalue="xformers" />
            <item index="5" class="java.lang.String" itemvalue="pytorch_lightning" />
            <item index="6" class="java.lang.String" itemvalue="torch" />
            <item index="7" class="java.lang.String" itemvalue="einops" />
            <item index="8" class="java.lang.String" itemvalue="omegaconf" />
            <item index="9" class="java.lang.String" itemvalue="PyQt5-Qt5" />
            <item index="10" class="java.lang.String" itemvalue="handright" />
            <item index="11" class="java.lang.String" itemvalue="PyQt5" />
            <item index="12" class="java.lang.String" itemvalue="PyQt5-stubs" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>