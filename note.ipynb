{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["\n", "\n", "---\n", "\n", "### **1. <PERSON><PERSON> lý cơ bản**\n", "| **Hàm**               | **<PERSON>ức năng**                                                               | **Ví dụ**                                                       |\n", "|------------------------|-----------------------------------------------------------------------------|-----------------------------------------------------------------|\n", "| `len()`               | <PERSON><PERSON>y độ dài chuỗi.                                                          | `len(\"hello\")` → `5`                                            |\n", "| `str()`               | Chuyển đổi dữ liệu khác thành chuỗi.                                        | `str(123)` → `\"123\"`                                            |\n", "| `lower()`             | Chuyển chuỗi thành chữ thường.                                             | `\"HELLO\".lower()` → `\"hello\"`                                   |\n", "| `upper()`             | Chuyển chuỗi thành chữ hoa.                                                | `\"hello\".upper()` → `\"HELLO\"`                                   |\n", "| `capitalize()`        | Viết hoa chữ cái đầu tiên của chuỗi.                                        | `\"python\".capitalize()` → `\"Python\"`                           |\n", "| `title()`             | Vi<PERSON><PERSON> hoa chữ cái đầu của mỗi từ trong chuỗi.                               | `\"hello world\".title()` → `\"Hello World\"`                      |\n", "| `strip()`             | <PERSON><PERSON><PERSON> k<PERSON>ng trắng ở đầu và cuối chuỗi.                                      | `\"  hello  \".strip()` → `\"hello\"`                              |\n", "| `lstrip()`/`rstrip()` | <PERSON><PERSON><PERSON> k<PERSON>ng trắng ở đầu (`lstrip`) hoặc cuối chuỗi (`rstrip`).              | `\"  hello  \".lstrip()` → `\"hello  \"`                           |\n", "\n", "---\n", "\n", "### **2. <PERSON><PERSON><PERSON> k<PERSON>ếm và thay thế**\n", "| **Hàm**           | **<PERSON>ức năng**                                                                 | **Ví dụ**                                                       |\n", "|--------------------|-------------------------------------------------------------------------------|-----------------------------------------------------------------|\n", "| `find()`          | Tìm vị trí đầu tiên của một chuỗi con.                                         | `\"hello world\".find(\"world\")` → `6`                            |\n", "| `rfind()`         | Tìm vị trí cuối cùng của một chuỗi con.                                       | `\"hello world world\".rfind(\"world\")` → `12`                    |\n", "| `index()`         | T<PERSON><PERSON><PERSON> tự `find()`, nh<PERSON>ng báo lỗi nếu không tìm thấy.                          | `\"hello\".index(\"l\")` → `2`                                      |\n", "| `replace()`       | Thay thế chuỗi con bằng chuỗi khác.                                           | `\"hello world\".replace(\"world\", \"Python\")` → `\"hello Python\"`  |\n", "| `count()`         | <PERSON><PERSON><PERSON> số lần xuất hiện của một chuỗi con.                                       | `\"hello world\".count(\"o\")` → `2`                               |\n", "| `startswith()`    | <PERSON><PERSON><PERSON> tra chuỗi có bắt đầu bằng một chuỗi con không.                           | `\"hello\".startswith(\"he\")` → `True`                            |\n", "| `endswith()`      | <PERSON><PERSON><PERSON> tra chuỗi có kết thúc bằng một chuỗi con không.                          | `\"hello\".endswith(\"lo\")` → `True`                              |\n", "\n", "---\n", "\n", "### **3. <PERSON><PERSON> t<PERSON>ch và nối chuỗi**\n", "| **Hàm**         | **<PERSON>ức năng**                                                                 | **Ví dụ**                                                       |\n", "|------------------|-------------------------------------------------------------------------------|-----------------------------------------------------------------|\n", "| `split()`       | Tách chuỗi thành danh sách dựa trên ký tự (mặc định là khoảng trắng).          | `\"a,b,c\".split(\",\")` → `['a', 'b', 'c']`                       |\n", "| `rsplit()`      | Tách chuỗi từ cuối đến đầu.                                                   | `\"a,b,c\".rsplit(\",\", 1)` → `['a,b', 'c']`                      |\n", "| `splitlines()`  | Tách chuỗi thành danh sách dựa trên ký tự xuống dòng (`\\n`).                   | `\"line1\\nline2\".splitlines()` → `['line1', 'line2']`           |\n", "| `join()`        | <PERSON><PERSON><PERSON> c<PERSON>c phần tử trong danh sách thành chuỗi với ký tự ngăn cách.              | `\",\".join(['a', 'b', 'c'])` → `\"a,b,c\"`                        |\n", "\n", "---\n", "\n", "### **4. <PERSON><PERSON><PERSON> chỉnh chuỗi**\n", "| **Hàm**       | **<PERSON>ức năng**                                                                 | **Ví dụ**                                                       |\n", "|----------------|-------------------------------------------------------------------------------|-----------------------------------------------------------------|\n", "| `center()`    | <PERSON><PERSON>n gi<PERSON>a chuỗi với chiều dài và ký tự đệm.                                     | `\"hello\".center(10, \"*\")` → `\"**hello**\"`                      |\n", "| `ljust()`     | <PERSON>ăn trái chuỗi với chiều dài và ký tự đệm.                                     | `\"hello\".ljust(10, \"*\")` → `\"hello*****\"`                      |\n", "| `rjust()`     | <PERSON><PERSON><PERSON> ph<PERSON>i chuỗi với chiều dài và ký tự đệm.                                     | `\"hello\".rjust(10, \"*\")` → `\"*****hello\"`                      |\n", "| `zfill()`     | <PERSON><PERSON><PERSON><PERSON> s<PERSON> `0` vào đầu chuỗi đến khi đạt chiều dài mong muốn.                     | `\"42\".zfill(5)` → `\"00042\"`                                    |\n", "\n", "---\n", "\n", "### **5. <PERSON><PERSON><PERSON> tra thu<PERSON><PERSON> t<PERSON> chuỗi**\n", "| **Hàm**             | **<PERSON>ức năng**                                                       | **Ví dụ**                                                       |\n", "|----------------------|---------------------------------------------------------------------|-----------------------------------------------------------------|\n", "| `isalpha()`         | <PERSON><PERSON><PERSON> tra chuỗi chỉ chứa chữ cái.                                     | `\"abc\".isalpha()` → `True`                                      |\n", "| `isdigit()`         | Ki<PERSON><PERSON> tra chuỗi chỉ chứa số.                                         | `\"123\".isdigit()` → `True`                                      |\n", "| `isalnum()`         | <PERSON><PERSON><PERSON> tra chuỗi chỉ chứa chữ cái hoặc số.                             | `\"abc123\".isalnum()` → `True`                                   |\n", "| `islower()`         | <PERSON><PERSON><PERSON> tra tất cả ký tự trong chuỗi là chữ thường.                    | `\"abc\".islower()` → `True`                                      |\n", "| `isupper()`         | <PERSON><PERSON><PERSON> tra tất cả ký tự trong chuỗi là chữ hoa.                       | `\"ABC\".isupper()` → `True`                                      |\n", "| `isspace()`         | <PERSON><PERSON><PERSON> tra chuỗi chỉ chứa khoảng trắng.                               | `\"   \".isspace()` → `True`                                      |\n", "| `istitle()`         | <PERSON><PERSON><PERSON> tra chuỗi có phải dạng tiêu đề (chữ cái đầu mỗi từ viết hoa).  | `\"Hello World\".istitle()` → `True`                              |\n", "\n", "---\n", "\n", "### **6. <PERSON><PERSON><PERSON><PERSON> đổi chuỗi**\n", "| **Hàm**           | **<PERSON>ức năng**                                                       | **Ví dụ**                                                       |\n", "|--------------------|---------------------------------------------------------------------|-----------------------------------------------------------------|\n", "| `swapcase()`      | Đ<PERSON>i chữ hoa thành chữ thường và ngược lại.                          | `\"Hello\".swapcase()` → `\"hELLO\"`                               |\n", "| `casefold()`      | Chuyển chuỗi thành dạng viết thường để so sánh không phân biệt hoa thường. | `\"Straße\".casefold()` → `\"strasse\"`                     |\n", "| `translate()`     | Thay thế ký tự dựa trên bảng dịch.                                   | `\"hello\".translate(str.maketrans(\"h\", \"H\"))` → `\"Hello\"`       |\n", "\n", "---\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "\n", "---\n", "\n", "### **1. `split()`**\n", "- **Công dụng**: <PERSON><PERSON><PERSON> chuỗi dựa trên một biểu thức ch<PERSON>h quy.\n", "- **<PERSON><PERSON> pháp**:\n", "  ```python\n", "  re.split(pattern, string, maxsplit=0, flags=0)\n", "  ```\n", "- **Tham số chính**:\n", "  - `pattern`: <PERSON><PERSON><PERSON><PERSON> thức ch<PERSON>h quy dùng để tách.\n", "  - `string`: Chuỗi cần tách.\n", "  - `maxsplit` (t<PERSON><PERSON>): <PERSON><PERSON> lần tách tối đa (mặc định là không giới hạn).\n", "\n", "- **Ví dụ**:\n", "  ```python\n", "  import re\n", "  text = \"word1, word2; word3.word4\"\n", "  result = re.split(r'[,\\.;\\s]+', text)\n", "  print(result)  # ['word1', 'word2', 'word3', 'word4']\n", "  ```\n", "\n", "---\n", "\n", "### **2. `findall()`**\n", "- **Công dụng**: <PERSON><PERSON><PERSON> về tất cả các kết quả khớp (là danh sách) trong chuỗi.\n", "- **<PERSON><PERSON> pháp**:\n", "  ```python\n", "  re.findall(pattern, string, flags=0)\n", "  ```\n", "- **Ví dụ**:\n", "  ```python\n", "  import re\n", "  text = \"Python is great. Python is easy to learn.\"\n", "  result = re.findall(r'Python', text)\n", "  print(result)  # ['Python', 'Python']\n", "  ```\n", "\n", "---\n", "\n", "### **3. `search()`**\n", "- **Công dụng**: <PERSON><PERSON><PERSON> kiế<PERSON> **lần xuất hiện đầu tiên** của mẫu regex trong chuỗi.\n", "- **<PERSON><PERSON> pháp**:\n", "  ```python\n", "  re.search(pattern, string, flags=0)\n", "  ```\n", "- **Tr<PERSON> về**: <PERSON><PERSON><PERSON> đ<PERSON>i tượng `Match` hoặc `None` nếu không tìm thấy.\n", "\n", "- **Ví dụ**:\n", "  ```python\n", "  import re\n", "  text = \"The rain in Spain\"\n", "  match = re.search(r'rain', text)\n", "  if match:\n", "      print(f\"Found '{match.group()}' at position {match.start()}-{match.end()}\")\n", "  ```\n", "\n", "---\n", "\n", "### **4. `match()`**\n", "- **Công dụng**: <PERSON><PERSON><PERSON> tra xem chuỗi có khớp với biểu thức chính quy bắt đầu từ vị trí đầu tiên không.\n", "- **<PERSON><PERSON> pháp**:\n", "  ```python\n", "  re.match(pattern, string, flags=0)\n", "  ```\n", "- **Ví dụ**:\n", "  ```python\n", "  import re\n", "  text = \"Python is fun\"\n", "  match = re.match(r'Python', text)\n", "  if match:\n", "      print(f\"Matched: {match.group()}\")\n", "  ```\n", "\n", "---\n", "\n", "### **5. `sub()`**\n", "- **Công dụng**: <PERSON><PERSON> thế các chuỗi khớp với mẫu regex bằng một chuỗi khác.\n", "- **<PERSON><PERSON> pháp**:\n", "  ```python\n", "  re.sub(pattern, repl, string, count=0, flags=0)\n", "  ```\n", "- **Ví dụ**:\n", "  ```python\n", "  import re\n", "  text = \"I love Python. Python is amazing.\"\n", "  result = re.sub(r'Python', 'Regex', text)\n", "  print(result)  # \"I love Regex. Regex is amazing.\"\n", "  ```\n", "\n", "---\n", "\n", "### **6. `finditer()`**\n", "- **Công dụng**: <PERSON><PERSON><PERSON> tất cả các kết quả khớp và trả về một **iterator** chứa các đối tượng `Match`.\n", "- **<PERSON><PERSON> pháp**:\n", "  ```python\n", "  re.finditer(pattern, string, flags=0)\n", "  ```\n", "- **Ví dụ**:\n", "  ```python\n", "  import re\n", "  text = \"Python Python Python\"\n", "  matches = re.finditer(r'Python', text)\n", "  for match in matches:\n", "      print(f\"Found '{match.group()}' at position {match.start()}-{match.end()}\")\n", "  ```\n", "\n", "---\n", "\n", "### **7. `start()` và `end()`**\n", "- **Công dụng**: <PERSON><PERSON><PERSON> định vị trí bắt đầu (`start()`) và kết thúc (`end()`) của mẫu khớp trong chuỗi.\n", "- **Ví dụ**:\n", "  ```python\n", "  import re\n", "  text = \"Hello world\"\n", "  match = re.search(r'world', text)\n", "  if match:\n", "      print(f\"Start: {match.start()}, End: {match.end()}\")  # Start: 6, End: 11\n", "  ```\n", "\n", "---\n", "\n", "### **8. `group()`**\n", "- **Công dụng**: <PERSON><PERSON>y chuỗi con khớp với regex trong đối tượng `Match`.\n", "- **Ví dụ**:\n", "  ```python\n", "  import re\n", "  text = \"My <NAME_EMAIL>\"\n", "  match = re.search(r'[\\w\\.-]+@[\\w\\.-]+', text)\n", "  if match:\n", "      print(match.group())  # <EMAIL>\n", "  ```\n", "\n", "---\n", "\n", "### **9. `compile()`**\n", "- **Công dụng**: <PERSON><PERSON><PERSON><PERSON> dịch biểu thức ch<PERSON>h quy để tái sử dụng nhiều lần.\n", "- **<PERSON><PERSON> pháp**:\n", "  ```python\n", "  regex = re.compile(pattern, flags=0)\n", "  ```\n", "- **Ví dụ**:\n", "  ```python\n", "  import re\n", "  pattern = re.compile(r'Python')\n", "  text = \"Python is awesome. Python is fun.\"\n", "  matches = pattern.findall(text)\n", "  print(matches)  # ['Python', 'Python']\n", "  ```\n", "\n", "---\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Trong Python, `re.findall()` là một hàm mạnh mẽ thuộc module **`re`** (Regular Expressions), được sử dụng để tìm tất cả các chuỗi con khớp với một mẫu regex trong một chuỗi văn bản. Dưới đây là toàn bộ các **ký tự đặc biệt (metacharacters)** trong biểu thức chính quy mà bạn có thể sử dụng với `re.findall()`:\n", "\n", "---\n", "\n", "### **1. <PERSON><PERSON> tự đại diện và khớp**\n", "| **Ký tự** | **Ý nghĩa** | **Ví dụ** | **Kết quả** |\n", "|-----------|-------------|-----------|-------------|\n", "| `.`       | <PERSON><PERSON><PERSON> cho **bất kỳ ký tự nào** ngoại trừ xuống dòng (`\\n`) | `re.findall(r'a.b', 'acb adb abb a_b')` | `['acb', 'adb', 'a_b']` |\n", "| `\\d`      | Khớp với **chữ số** (0–9) | `re.findall(r'\\d', 'abc123def')` | `['1', '2', '3']` |\n", "| `\\D`      | Khớp với **bất kỳ ký tự nào không phải chữ số** | `re.findall(r'\\D', 'abc123def')` | `['a', 'b', 'c', 'd', 'e', 'f']` |\n", "| `\\w`      | Khớp với **ký tự chữ cái, số, hoặc `_`** | `re.findall(r'\\w+', 'Hello_World! 123')` | `['Hello_World', '123']` |\n", "| `\\W`      | Khớp với **bất kỳ ký tự nào không phải chữ cái, số hoặc `_`** | `re.findall(r'\\W+', 'Hello_World! 123')` | `[' ', '! ', ' ']` |\n", "| `\\s`      | Khớp với **khoảng trắng** (space, tab, newline) | `re.findall(r'\\s', 'Hello World\\n123')` | `[' ', '\\n']` |\n", "| `\\S`      | Khớp với **bất kỳ ký tự nào không phải khoảng trắng** | `re.findall(r'\\S+', 'Hello World\\n123')` | `['Hello', 'World', '123']` |\n", "\n", "---\n", "\n", "### **2. <PERSON><PERSON> (Anchors)**\n", "| **Ký tự** | **Ý nghĩa** | **Ví dụ** | **Kết quả** |\n", "|-----------|-------------|-----------|-------------|\n", "| `^`       | Khớp với **đầu chuỗi** | `re.findall(r'^Hello', 'Hello World')` | `['Hello']` |\n", "| `$`       | Khớp với **cuối chuỗi** | `re.findall(r'World$', 'Hello World')` | `['World']` |\n", "| `\\b`      | Khớp với **biên giới từ** | `re.findall(r'\\bWorld\\b', 'Hello World!')` | `['World']` |\n", "| `\\B`      | Khớp với **không phải biên giới từ** | `re.findall(r'lo\\B', 'Hello World')` | `['lo']` |\n", "\n", "---\n", "\n", "### **3. <PERSON><PERSON> t<PERSON> định l<PERSON> (Quantifiers)**\n", "| **Ký tự** | **Ý nghĩa** | **Ví dụ** | **Kết quả** |\n", "|-----------|-------------|-----------|-------------|\n", "| `*`       | Khớp **0 hoặc nhiều lần** | `re.findall(r'a*', 'aaabaaa')` | `['aaa', '', 'aaa', '']` |\n", "| `+`       | Khớp **1 hoặc nhiều lần** | `re.findall(r'a+', 'aaabaaa')` | `['aaa', 'aaa']` |\n", "| `?`       | Khớp **0 hoặc 1 lần** | `re.findall(r'ab?', 'abbbabc')` | `['ab', 'ab']` |\n", "| `{n}`     | Khớp chính xác **n lần** | `re.findall(r'a{3}', 'aaabaaa')` | `['aaa']` |\n", "| `{n,}`    | Khớp **ít nhất n lần** | `re.findall(r'a{2,}', 'aaabaaa')` | `['aaa', 'aaa']` |\n", "| `{n,m}`   | Khớp từ **n đến m lần** | `re.findall(r'a{2,3}', 'aaabaaa')` | `['aaa', 'aaa']` |\n", "\n", "---\n", "\n", "### **4. <PERSON><PERSON><PERSON><PERSON> và lựa chọn (Grouping and Alternation)**\n", "| **Ký tự** | **Ý nghĩa** | **Ví dụ** | **Kết quả** |\"\n", "|-----------|-------------|-----------|-------------|\n", "| `()`      | Nhóm mẫu lại với nhau | `re.findall(r'(ab)+', 'ababababc')` | `['ab', 'ab']` |\n", "| `|`       | Khớp với **hoặc** | `re.findall(r'cat|dog', 'cat and dog')` | `['cat', 'dog']` |\n", "\n", "---\n", "\n", "### **5. <PERSON><PERSON> (Escape Characters)**\n", "| **Ký tự** | **Ý nghĩa** | **Ví dụ** | **Kết quả** |\n", "|-----------|-------------|-----------|-------------|\n", "| `\\.`      | Khớp với **dấu chấm thực** | `re.findall(r'\\.', 'example.com')` | `['.']` |\n", "| `\\\\`      | Khớp với **dấu gạch chéo ngược thực** | `re.findall(r'\\\\', r'c:\\\\path')` | `['\\\\']` |\n", "\n", "---\n", "\n", "### **6. <PERSON><PERSON> kh<PERSON> (Character Classes)**\n", "| **Ký tự** | **Ý nghĩa** | **Ví dụ** | **Kết quả** |\n", "|-----------|-------------|-----------|-------------|\n", "| `[abc]`   | Khớp với **a, b, hoặc c** | `re.findall(r'[aeiou]', 'hello world')` | `['e', 'o', 'o']` |\n", "| `[^abc]`  | Khớp với **bất kỳ ký tự nào không phải a, b, hoặc c** | `re.findall(r'[^aeiou]', 'hello world')` | `['h', 'l', 'l', ' ', 'w', 'r', 'l', 'd']` |\n", "| `[a-z]`   | Khớp với **các ký tự từ a đến z** | `re.findall(r'[a-z]', 'Hello123')` | `['e', 'l', 'l', 'o']` |\n", "| `[A-Z]`   | Khớp với **các ký tự từ A đến Z** | `re.findall(r'[A-Z]', 'Hello123')` | `['H']` |\n", "| `[0-9]`   | Khớp với **các số từ 0 đến 9** | `re.findall(r'[0-9]', 'Hello123')` | `['1', '2', '3']` |\n", "\n", "---\n", "\n", "### **7. <PERSON><PERSON><PERSON> mẫu đặc biệt**\n", "| **Ký tự**         | **Ý nghĩa**                  | **Ví dụ**                          | **Kết quả**                 |\n", "|--------------------|------------------------------|-------------------------------------|-----------------------------|\n", "| `(?i)`            | Bỏ qua phân biệt chữ hoa/thường | `re.findall(r'(?i)hello', 'Hello')` | `['Hello']`                |\n", "| `(?<=x)`          | Khớp với chuỗi ngay **sau** `x` | `re.findall(r'(?<=\\$)\\d+', '$10 $20')` | `['10', '20']`         |\n", "| `(?=x)`           | Khớp với chuỗi ngay **trước** `x` | `re.findall(r'\\d+(?=%)', '50% 100%')` | `['50', '100']`       |\n", "| `(?!x)`           | Không khớp với chuỗi ngay **sau** `x` | `re.findall(r'\\d+(?!%)', '50% 100 kg')` | `['100']`          |\n", "\n", "---\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Dưới đây là các cú pháp khởi tạo và các chức năng liên quan đến **dictionary** trong Python:\n", "\n", "---\n", "\n", "### **1. Khởi tạo dictionary**\n", "| **Cách khởi tạo**           | **Mô tả**                                                                                 | **Ví dụ**                                                       |\n", "|------------------------------|-------------------------------------------------------------------------------------------|-----------------------------------------------------------------|\n", "| Sử dụng `{}`                | Khởi tạo dictionary rỗng hoặc có giá trị.                                                 | `my_dict = {\"a\": 1, \"b\": 2}`                                    |\n", "| Sử dụng `dict()`            | Khởi tạo dictionary bằng hàm `dict()`.                                                    | `my_dict = dict(a=1, b=2)`                                      |\n", "| Từ danh sách tuple          | Chuyển đổi danh sách tuple thành dictionary.                                              | `my_dict = dict([(\"a\", 1), (\"b\", 2)])`                         |\n", "| Sử dụng comprehension       | Tạo dictionary từ vòng lặp.                                                               | `my_dict = {x: x**2 for x in range(5)}`                        |\n", "\n", "---\n", "\n", "### **2. <PERSON><PERSON><PERSON> thao tác cơ bản với dictionary**\n", "| **Cú pháp/Hàm**       | **Chức năng**                                                                   | **Ví dụ**                                                       |\n", "|------------------------|---------------------------------------------------------------------------------|-----------------------------------------------------------------|\n", "| `my_dict[key]`        | <PERSON><PERSON><PERSON> cập giá trị dựa trên key.                                                   | `my_dict[\"a\"]` → `1`                                           |\n", "| `my_dict[key] = value`| Gán giá trị cho key.                                                            | `my_dict[\"c\"] = 3` → `{'a': 1, 'b': 2, 'c': 3}`               |\n", "| `del my_dict[key]`    | Xóa một key-value trong dictionary.                                             | `del my_dict[\"a\"]`                                              |\n", "| `key in my_dict`      | Kiểm tra key có tồn tại trong dictionary không.                                 | `'a' in my_dict` → `True`                                      |\n", "| `len(my_dict)`        | Trả về số lượng key-value trong dictionary.                                     | `len(my_dict)` → `2`                                           |\n", "| `my_dict.clear()`     | Xóa toàn bộ các phần tử trong dictionary.                                       | `my_dict.clear()` → `{}`                                       |\n", "\n", "---\n", "\n", "### **3. <PERSON><PERSON><PERSON> thức xử lý dictionary**\n", "| **Ph<PERSON><PERSON><PERSON> thức**          | **Chức năng**                                                                 | **Ví dụ**                                                       |\n", "|---------------------------|-------------------------------------------------------------------------------|-----------------------------------------------------------------|\n", "| `get(key, default)`      | <PERSON><PERSON>y giá trị của key, trả về giá trị mặc định nếu key không tồn tại.            | `my_dict.get(\"a\", 0)` → `1`                                    |\n", "| `pop(key, default)`      | L<PERSON>y giá trị của key và xóa nó khỏi dictionary.                                | `my_dict.pop(\"a\")` → `{'b': 2}`                                |\n", "| `popitem()`              | X<PERSON><PERSON> và trả về một cặp key-value bất kỳ.                                       | `my_dict.popitem()` → `('b', 2)`                               |\n", "| `setdefault(key, value)` | <PERSON><PERSON><PERSON> về giá trị của key, nếu không tồn tại sẽ thêm key với giá trị mặc định.    | `my_dict.setdefault(\"c\", 3)` → `{'a': 1, 'b': 2, 'c': 3}`      |\n", "| `update(other_dict)`     | Thêm hoặc cập nhật dictionary từ một dictionary hoặc iterable khác.           | `my_dict.update({\"d\": 4})` → `{'a': 1, 'b': 2, 'd': 4}`        |\n", "\n", "---\n", "\n", "### **4. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> phần tử trong dictionary**\n", "| **Hàm/<PERSON><PERSON> pháp**                  | **Chức năng**                                                    | **Ví dụ**                                                       |\n", "|-----------------------------------|------------------------------------------------------------------|-----------------------------------------------------------------|\n", "| `keys()`                         | T<PERSON><PERSON> về danh sách các key trong dictionary.                      | `list(my_dict.keys())` → `['a', 'b']`                          |\n", "| `values()`                       | Tr<PERSON> về danh sách các value trong dictionary.                    | `list(my_dict.values())` → `[1, 2]`                            |\n", "| `items()`                        | <PERSON><PERSON><PERSON> về danh sách các tuple (key, value).                        | `list(my_dict.items())` → `[('a', 1), ('b', 2)]`               |\n", "| `for key in my_dict`             | Lặp qua các key trong dictionary.                               | `for key in my_dict: print(key)` → `a\\nb`                      |\n", "| `for key, value in my_dict.items()`| Lặp qua cả key và value.                                         | `for k, v in my_dict.items(): print(k, v)` → `a 1\\nb 2`        |\n", "\n", "---\n", "\n", "### **5. <PERSON><PERSON><PERSON><PERSON> dụng đặc biệt**\n", "| **<PERSON><PERSON>ch sử dụng**         | **Mô tả**                                                                 | **Ví dụ**                                                       |\n", "|--------------------------|---------------------------------------------------------------------------|-----------------------------------------------------------------|\n", "| Dictionary Comprehension | Tạo dictionary từ một biểu thức hoặc vòng lặp.                          | `{x: x**2 for x in range(3)}` → `{0: 0, 1: 1, 2: 4}`           |\n", "| Đảo ng<PERSON>c dictionary     | Đổi vị trí key và value.                                                 | `{v: k for k, v in my_dict.items()}`                           |\n", "| Đếm tần suất xuất hiện   | Sử dụng dictionary để đếm tần suất các phần tử trong danh sách.          | `freq = {x: lst.count(x) for x in set(lst)}`                   |\n", "\n", "---\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<PERSON><PERSON><PERSON><PERSON> đây là những kiến thức cơ bản và các chức năng liên quan đến **tuple** trong Python:\n", "\n", "---\n", "\n", "### **1. Khởi tạo tuple**\n", "| **Cách khởi tạo**            | **Mô tả**                                                                                  | **Ví dụ**                         |\n", "|-------------------------------|--------------------------------------------------------------------------------------------|-----------------------------------|\n", "| Sử dụng dấu ngoặc `()`        | Khởi tạo tuple thông thường.                                                               | `my_tuple = (1, 2, 3)`           |\n", "| Tuple rỗng                   | Tạo một tuple rỗng.                                                                        | `empty_tuple = ()`               |\n", "| Tu<PERSON> chứa một phần tử        | <PERSON><PERSON><PERSON> có dấu phẩy sau phần tử để phân biệt với kiểu dữ liệu khác.                           | `single_element = (1,)`          |\n", "| Sử dụng `tuple()`            | Tạo tuple từ iterable (list, string, set, v.v.).                                           | `my_tuple = tuple([1, 2, 3])`    |\n", "| Tu<PERSON> không có ngoặc          | <PERSON> cho phép bỏ ngoặc khi khai báo các giá trị phân tách bằng dấu phẩy.                 | `my_tuple = 1, 2, 3`             |\n", "\n", "---\n", "\n", "### **2. <PERSON><PERSON><PERSON> chất của tuple**\n", "1. **<PERSON><PERSON><PERSON>ng thể thay đổi (Immutable):** <PERSON><PERSON><PERSON> khi được khởi tạo, gi<PERSON> trị của tuple không thể thay đổi.\n", "2. **<PERSON><PERSON> thể chứa các kiểu dữ liệu khác nhau:** <PERSON><PERSON> có thể chứa số, chuỗi, danh s<PERSON>, hoặc thậm chí tuple khác.\n", "\n", "---\n", "\n", "### **3. <PERSON><PERSON><PERSON> thao tác c<PERSON> bản với tuple**\n", "| **<PERSON><PERSON> pháp/<PERSON><PERSON><PERSON><PERSON> thức**       | **<PERSON><PERSON><PERSON> năng**                                                            | **Ví dụ**                               |\n", "|--------------------------------|--------------------------------------------------------------------------|-----------------------------------------|\n", "| `my_tuple[index]`             | <PERSON><PERSON><PERSON> cập phần tử tại vị trí `index`.                                     | `(1, 2, 3)[0]` → `1`                   |\n", "| `my_tuple[start:end:step]`    | <PERSON><PERSON><PERSON> một phần tử con (slicing).                                           | `(1, 2, 3, 4)[1:3]` → `(2, 3)`         |\n", "| `len(my_tuple)`               | T<PERSON><PERSON> về số lượng phần tử trong tuple.                                     | `len((1, 2, 3))` → `3`                 |\n", "| `my_tuple.index(value)`       | Tìm vị trí đầu tiên của giá trị `value`.                                  | `(1, 2, 3).index(2)` → `1`             |\n", "| `my_tuple.count(value)`       | <PERSON><PERSON><PERSON> số lần xuất hiện của giá trị `value`.                                | `(1, 2, 2, 3).count(2)` → `2`          |\n", "| `value in my_tuple`           | Ki<PERSON><PERSON> tra xem `value` có trong tuple không.                               | `2 in (1, 2, 3)` → `True`              |\n", "| `value not in my_tuple`       | Ki<PERSON>m tra xem `value` không có trong tuple.                               | `4 not in (1, 2, 3)` → `True`          |\n", "\n", "---\n", "\n", "### **4. <PERSON><PERSON><PERSON> thao tác đặc biệt**\n", "| **T<PERSON> tác**                  | **Chức năng**                                                           | **Ví dụ**                               |\n", "|--------------------------------|--------------------------------------------------------------------------|-----------------------------------------|\n", "| <PERSON><PERSON> tuple (unpacking)         | Gán giá trị trong tuple vào các bi<PERSON>n.                                   | `a, b, c = (1, 2, 3)` → `a=1, b=2, c=3`|\n", "| <PERSON><PERSON>i tuple                     | <PERSON><PERSON><PERSON> hợp hai tuple.                                                      | `(1, 2) + (3, 4)` → `(1, 2, 3, 4)`     |\n", "| Nhân tuple                    | Lặp lại tuple nhiều lần.                                                | `(1, 2) * 3` → `(1, 2, 1, 2, 1, 2)`    |\n", "| <PERSON><PERSON>ng ghép tuple               | <PERSON><PERSON> có thể chứa các tuple khác.                                       | `nested = (1, (2, 3), (4, (5, 6)))`    |\n", "\n", "---\n", "\n", "### **5. <PERSON><PERSON><PERSON><PERSON> dụng thực tế**\n", "- **<PERSON><PERSON> thường dùng để lưu trữ dữ liệu không đổi** như tọa độ, cặp giá trị (`key, value`) hoặc cấu trúc bất biến.\n", "- **<PERSON><PERSON> làm khóa trong dictionary**: <PERSON><PERSON> tuple không thay đổi nên chúng có thể được sử dụng làm khóa trong dictionary.\n", "\n", "---\n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}