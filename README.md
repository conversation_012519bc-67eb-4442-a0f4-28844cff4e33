# LLM Question-Answering System for Codebase Analysis

A comprehensive question-answering system that integrates local LLM (Ollama) with semantic search to provide intelligent analysis of codebases. The system can answer questions about code, generate Q&A pairs for training, and provide detailed explanations of code functionality.

## 🚀 Features

- **Local LLM Integration**: Uses Ollama for privacy-focused, local language model processing
- **Semantic Search**: ChromaDB-powered vector search for relevant code retrieval
- **Intelligent Q&A**: Combines semantic search with LLM reasoning for accurate answers
- **Code Understanding**: Analyzes Python, JavaScript, Markdown, and other file types
- **Q&A Generation**: Automatically creates question-answer pairs from codebase content
- **Interactive Interface**: Command-line interface with rich formatting and interactive mode
- **Comprehensive Indexing**: Extracts functions, classes, and meaningful code chunks

## 📋 Prerequisites

1. **Python 3.8+**
2. **Ollama** installed and running locally
   ```bash
   # Install Ollama (macOS/Linux)
   curl -fsSL https://ollama.ai/install.sh | sh
   
   # Start Ollama service
   ollama serve
   
   # Pull a model (e.g., llama3.2)
   ollama pull llama3.2
   ```

## 🛠️ Installation

1. **<PERSON>lone or download the project**
   ```bash
   cd your-codebase-directory
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify Ollama connection**
   ```bash
   curl http://localhost:11434/api/tags
   ```

## 🎯 Quick Start

### 1. Index Your Codebase
```bash
# Index current directory
python main.py index

# Index specific directory
python main.py index /path/to/your/codebase

# Force re-indexing
python main.py index --force
```

### 2. Ask Questions
```bash
# Ask a single question
python main.py ask "How does the authentication work?"

# Ask with specific codebase path
python main.py ask "What machine learning algorithms are used?" --path /path/to/ml/project
```

### 3. Interactive Mode
```bash
# Start interactive Q&A session
python main.py interactive

# Interactive mode with specific path
python main.py interactive --path /path/to/codebase
```

### 4. Generate Q&A Pairs
```bash
# Generate 10 Q&A pairs
python main.py generate-qa

# Generate 25 pairs with custom output
python main.py generate-qa --num 25 --output my_qa_pairs.json
```

### 5. View Statistics
```bash
# Show indexing statistics
python main.py stats
```

## 💡 Usage Examples

### Example Questions You Can Ask

**Functionality Questions:**
- "What does the main function do?"
- "How is user authentication implemented?"
- "What machine learning models are used?"
- "How does the data processing pipeline work?"

**Implementation Questions:**
- "Show me all the classes in the project"
- "What external libraries are being used?"
- "How is error handling implemented?"
- "What design patterns are used?"

**Code Analysis:**
- "Are there any potential security issues?"
- "What functions handle file I/O?"
- "How is the database connection managed?"
- "What are the main entry points of the application?"

### Interactive Mode Commands

In interactive mode, you can use these special commands:
- `help` - Show available commands
- `stats` - Display codebase statistics
- `clear` - Clear chat history
- `quit` / `exit` / `q` - Exit interactive mode

## 🏗️ System Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Codebase      │    │   Semantic       │    │   Ollama LLM    │
│   Indexer       │───▶│   Search         │───▶│   Client        │
│                 │    │   (ChromaDB)     │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Code Chunks   │    │   Vector         │    │   Generated     │
│   Extraction    │    │   Embeddings     │    │   Answers       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Components

1. **CodebaseIndexer**: Scans and extracts meaningful chunks from code files
2. **SemanticSearchEngine**: Creates embeddings and performs vector similarity search
3. **OllamaClient**: Handles communication with local Ollama LLM service
4. **QuestionAnsweringEngine**: Combines search results with LLM processing
5. **QAGenerator**: Creates training/testing Q&A pairs from codebase content

## ⚙️ Configuration

The system can be configured by modifying `config.py`:

```python
# Ollama settings
OLLAMA_CONFIG = {
    "base_url": "http://localhost:11434",
    "default_model": "llama3.2",
    "timeout": 60,
    "max_retries": 3
}

# Search settings
SEARCH_CONFIG = {
    "embedding_model": "all-MiniLM-L6-v2",
    "max_results": 5,
    "similarity_threshold": 0.7,
    "chroma_db_path": "data/chroma_db"
}

# Indexing settings
INDEXING_CONFIG = {
    "supported_extensions": [".py", ".md", ".txt", ".rst", ".ipynb"],
    "ignore_dirs": ["__pycache__", ".git", "node_modules"],
    "max_file_size_mb": 10
}
```

## 🔧 Advanced Usage

### Custom Search Filters

```python
from llm_qa_system import QuestionAnsweringEngine

# Search only in Python files
result = qa_engine.answer_question(
    "How is data validation implemented?",
    search_filters={"language": "python"}
)

# Search only in specific file types
result = qa_engine.answer_question(
    "What functions are defined?",
    search_filters={"chunk_type": "function"}
)
```

### Programmatic Usage

```python
from llm_qa_system import (
    OllamaClient, SemanticSearchEngine, 
    QuestionAnsweringEngine, CodebaseIndexer
)

# Initialize components
ollama_client = OllamaClient()
search_engine = SemanticSearchEngine()
qa_engine = QuestionAnsweringEngine(ollama_client, search_engine)

# Index codebase
indexer = CodebaseIndexer("./my_project")
chunks = indexer.index_codebase()
search_engine.add_chunks(chunks)

# Ask questions
result = qa_engine.answer_question("How does authentication work?")
print(result["answer"])
```

## 🐛 Troubleshooting

### Common Issues

1. **Ollama Connection Failed**
   ```bash
   # Check if Ollama is running
   curl http://localhost:11434/api/tags
   
   # Start Ollama if not running
   ollama serve
   ```

2. **Model Not Found**
   ```bash
   # Pull the required model
   ollama pull llama3.2
   
   # List available models
   ollama list
   ```

3. **No Code Chunks Found**
   - Ensure your codebase contains supported file types (.py, .md, .txt, etc.)
   - Check that files are not in ignored directories
   - Verify file permissions

4. **Poor Search Results**
   - Try rephrasing your question
   - Use more specific terms
   - Check if the relevant code is actually indexed

### Performance Tips

- **Large Codebases**: Consider indexing in smaller batches
- **Memory Usage**: Adjust `chunk_size` in config for memory optimization
- **Search Speed**: Reduce `max_results` for faster searches
- **Model Performance**: Use larger Ollama models for better answers (e.g., `llama3.1:70b`)

## 📊 Output Examples

### Q&A Pair Generation Output
```json
[
  {
    "question": "What does the file_count function do?",
    "answer": "The file_count function analyzes a text file and counts three metrics: words, characters, and sentences. It reads the file content, uses regex to find words, counts non-whitespace characters, and splits by newlines to count sentences.",
    "source": {
      "file_path": "PE_SU24_1/Q1.py",
      "chunk_type": "function",
      "language": "python",
      "start_line": 10,
      "end_line": 23
    }
  }
]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- **Ollama** for local LLM capabilities
- **ChromaDB** for vector storage and search
- **Sentence Transformers** for embeddings
- **Rich** for beautiful terminal interfaces
- **Typer** for CLI framework
