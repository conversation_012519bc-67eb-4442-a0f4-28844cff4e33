# Cau 4
#Write a Python program that takes a paragraph of text as input and outputs the number of times each word appears in the paragraph.

import spacy

paragraph = "the quick brown fox jumps over the lazy dog the quick brown fox jumps over the lazy dog"
nlp=spacy.load("en_core_web_sm")
doc=nlp(paragraph)

words=[str(token).strip() for token in doc if token.is_punct==False]
freq_dict={}

for word in words:
  if word not in freq_dict:
    freq_dict[word]=1
  else:
    freq_dict[word]+=1

print(freq_dict)
