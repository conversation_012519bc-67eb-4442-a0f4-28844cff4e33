# Cau 1
# Write a Python program that inputs a text file and outputs the number of words, characters, and sentences in the file.

# Input: a text file named "sample.txt" with the following content


import re


def file_count(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        text = file.read()
    num_characters = len([_ for _ in text if _.strip()])
    words = re.findall(r'\b\w+\b', text)
    num_words = len(words)

    sentences = text.split('\n')
    num_sentences = len([s for s in sentences if s.strip()])  

    print(f'Number of words: {num_words}')
    print(f'Number of characters: {num_characters}')
    print(f'Number of sentences: {num_sentences}')


file_count(input_file)
