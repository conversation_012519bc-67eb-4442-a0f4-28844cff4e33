""""Question 4: (3 marks)
Write a function that takes a list of words (containing duplicates) and returns a list of words
(with no duplicates) sorted by ascending frequency.
Input:
['table', 'table', 'table', table', table', 'table', table', table, table', table', 'chair, 'chair, 'chair,
"chair, 'chair, 'chair, 'chair, 'chair, 'chair')
Output:
['chair, table] """

import nltk
from nltk.probability import FreqDist

# Make sure you have the necessary NLTK resources
nltk.download('punkt')


def sort_by_frequency(words):
    # Create a FreqDist object to count word frequencies
    freq_dist = FreqDist(words)

    # Sort the words based on frequency (ascending order)
    sorted_words = sorted(freq_dist, key=freq_dist.get)

    return sorted_words


# Example usage
input_words = ['table', 'table', 'table', 'table', 'table', 'table', 'table', 'table', 'table', 'table', 'chair',
               'chair', 'chair', 'chair', 'chair', 'chair', 'chair', 'chair', 'chair']
output_words = sort_by_frequency(input_words)
print(output_words)
