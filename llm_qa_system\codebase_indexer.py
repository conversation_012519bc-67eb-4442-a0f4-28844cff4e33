"""
Codebase indexer for extracting and processing code content
"""

import os
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Generator
import re
from dataclasses import dataclass, asdict
from config import INDEXING_CONFIG

logger = logging.getLogger(__name__)


@dataclass
class CodeChunk:
    """Represents a chunk of code with metadata"""
    content: str
    file_path: str
    start_line: int
    end_line: int
    chunk_type: str  # 'function', 'class', 'module', 'comment', 'other'
    language: str
    size: int
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        self.size = len(self.content)


class CodebaseIndexer:
    """Indexes codebase files and extracts meaningful chunks"""
    
    def __init__(self, root_path: str):
        """
        Initialize codebase indexer
        
        Args:
            root_path: Root directory of the codebase to index
        """
        self.root_path = Path(root_path)
        self.supported_extensions = INDEXING_CONFIG["supported_extensions"]
        self.ignore_dirs = INDEXING_CONFIG["ignore_dirs"]
        self.ignore_files = INDEXING_CONFIG["ignore_files"]
        self.max_file_size_mb = INDEXING_CONFIG["max_file_size_mb"]
        
    def _should_ignore_path(self, path: Path) -> bool:
        """Check if path should be ignored"""
        # Check if any parent directory is in ignore list
        for part in path.parts:
            if part in self.ignore_dirs:
                return True
                
        # Check if filename matches ignore patterns
        for pattern in self.ignore_files:
            if path.match(pattern):
                return True
                
        return False
        
    def _get_file_language(self, file_path: Path) -> str:
        """Determine programming language from file extension"""
        extension_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.h': 'c',
            '.hpp': 'cpp',
            '.cs': 'csharp',
            '.php': 'php',
            '.rb': 'ruby',
            '.go': 'go',
            '.rs': 'rust',
            '.md': 'markdown',
            '.txt': 'text',
            '.rst': 'restructuredtext',
            '.ipynb': 'jupyter'
        }
        return extension_map.get(file_path.suffix.lower(), 'unknown')
        
    def _extract_python_chunks(self, content: str, file_path: str) -> List[CodeChunk]:
        """Extract meaningful chunks from Python code"""
        chunks = []
        lines = content.split('\n')
        
        # Extract classes and functions
        current_chunk = []
        current_start = 0
        current_type = 'other'
        indent_level = 0
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            
            # Detect class or function definitions
            if stripped.startswith('class ') or stripped.startswith('def '):
                # Save previous chunk if exists
                if current_chunk:
                    chunk_content = '\n'.join(current_chunk)
                    if chunk_content.strip():
                        chunks.append(CodeChunk(
                            content=chunk_content,
                            file_path=file_path,
                            start_line=current_start + 1,
                            end_line=i,
                            chunk_type=current_type,
                            language='python'
                        ))
                
                # Start new chunk
                current_chunk = [line]
                current_start = i
                current_type = 'class' if stripped.startswith('class ') else 'function'
                indent_level = len(line) - len(line.lstrip())
                
            elif current_chunk:
                # Continue current chunk if indented or empty line
                line_indent = len(line) - len(line.lstrip()) if line.strip() else indent_level + 1
                if line_indent > indent_level or not line.strip():
                    current_chunk.append(line)
                else:
                    # End current chunk
                    chunk_content = '\n'.join(current_chunk)
                    if chunk_content.strip():
                        chunks.append(CodeChunk(
                            content=chunk_content,
                            file_path=file_path,
                            start_line=current_start + 1,
                            end_line=i,
                            chunk_type=current_type,
                            language='python'
                        ))
                    
                    # Start new chunk
                    current_chunk = [line] if line.strip() else []
                    current_start = i
                    current_type = 'other'
                    indent_level = 0
            else:
                # Start new chunk for non-empty lines
                if line.strip():
                    current_chunk = [line]
                    current_start = i
                    current_type = 'other'
                    indent_level = 0
        
        # Add final chunk
        if current_chunk:
            chunk_content = '\n'.join(current_chunk)
            if chunk_content.strip():
                chunks.append(CodeChunk(
                    content=chunk_content,
                    file_path=file_path,
                    start_line=current_start + 1,
                    end_line=len(lines),
                    chunk_type=current_type,
                    language='python'
                ))
        
        return chunks
        
    def _extract_generic_chunks(self, content: str, file_path: str, language: str, chunk_size: int = 1000) -> List[CodeChunk]:
        """Extract chunks from non-Python files using simple text splitting"""
        chunks = []
        lines = content.split('\n')
        
        current_chunk = []
        current_start = 0
        current_size = 0
        
        for i, line in enumerate(lines):
            current_chunk.append(line)
            current_size += len(line) + 1  # +1 for newline
            
            if current_size >= chunk_size or i == len(lines) - 1:
                chunk_content = '\n'.join(current_chunk)
                if chunk_content.strip():
                    chunks.append(CodeChunk(
                        content=chunk_content,
                        file_path=file_path,
                        start_line=current_start + 1,
                        end_line=i + 1,
                        chunk_type='text',
                        language=language
                    ))
                
                current_chunk = []
                current_start = i + 1
                current_size = 0
        
        return chunks
        
    def _process_jupyter_notebook(self, file_path: Path) -> List[CodeChunk]:
        """Process Jupyter notebook files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                notebook = json.load(f)
                
            chunks = []
            for i, cell in enumerate(notebook.get('cells', [])):
                cell_type = cell.get('cell_type', 'unknown')
                source = cell.get('source', [])
                
                if isinstance(source, list):
                    content = ''.join(source)
                else:
                    content = str(source)
                    
                if content.strip():
                    chunks.append(CodeChunk(
                        content=content,
                        file_path=str(file_path),
                        start_line=i + 1,
                        end_line=i + 1,
                        chunk_type=f'jupyter_{cell_type}',
                        language='jupyter',
                        metadata={'cell_index': i, 'cell_type': cell_type}
                    ))
                    
            return chunks
            
        except Exception as e:
            logger.error(f"Failed to process notebook {file_path}: {e}")
            return []
            
    def extract_file_chunks(self, file_path: Path) -> List[CodeChunk]:
        """Extract chunks from a single file"""
        try:
            # Check file size
            file_size_mb = file_path.stat().st_size / (1024 * 1024)
            if file_size_mb > self.max_file_size_mb:
                logger.warning(f"Skipping large file {file_path} ({file_size_mb:.1f}MB)")
                return []
                
            # Handle Jupyter notebooks separately
            if file_path.suffix == '.ipynb':
                return self._process_jupyter_notebook(file_path)
                
            # Read file content
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
            if not content.strip():
                return []
                
            language = self._get_file_language(file_path)
            
            # Use specialized extraction for Python files
            if language == 'python':
                return self._extract_python_chunks(content, str(file_path))
            else:
                return self._extract_generic_chunks(content, str(file_path), language)
                
        except Exception as e:
            logger.error(f"Failed to process file {file_path}: {e}")
            return []
            
    def scan_codebase(self) -> Generator[Path, None, None]:
        """Scan codebase and yield file paths to process"""
        for root, dirs, files in os.walk(self.root_path):
            # Filter out ignored directories
            dirs[:] = [d for d in dirs if d not in self.ignore_dirs]
            
            for file in files:
                file_path = Path(root) / file
                
                # Check if file should be processed
                if (file_path.suffix in self.supported_extensions and 
                    not self._should_ignore_path(file_path)):
                    yield file_path
                    
    def index_codebase(self) -> List[CodeChunk]:
        """Index entire codebase and return all chunks"""
        logger.info(f"Starting codebase indexing from {self.root_path}")
        
        all_chunks = []
        file_count = 0
        
        for file_path in self.scan_codebase():
            logger.debug(f"Processing {file_path}")
            chunks = self.extract_file_chunks(file_path)
            all_chunks.extend(chunks)
            file_count += 1
            
        logger.info(f"Indexed {file_count} files, extracted {len(all_chunks)} chunks")
        return all_chunks
        
    def save_index(self, chunks: List[CodeChunk], output_path: str):
        """Save index to JSON file"""
        data = [asdict(chunk) for chunk in chunks]
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
            
        logger.info(f"Saved index with {len(chunks)} chunks to {output_path}")
        
    def load_index(self, input_path: str) -> List[CodeChunk]:
        """Load index from JSON file"""
        with open(input_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        chunks = [CodeChunk(**item) for item in data]
        logger.info(f"Loaded index with {len(chunks)} chunks from {input_path}")
        return chunks
