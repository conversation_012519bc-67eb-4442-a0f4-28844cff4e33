"""
LLM Question-Answering System for Codebase Analysis

A comprehensive system that integrates local LLM (Ollama) with semantic search
to provide intelligent question-answering capabilities over codebases.
"""

__version__ = "1.0.0"
__author__ = "NLP301c QA System"

from .ollama_client import OllamaClient
from .semantic_search import SemanticSearchEngine
from .qa_engine import QuestionAnsweringEngine
from .qa_generator import QAGenerator
from .codebase_indexer import CodebaseIndexer

__all__ = [
    "OllamaClient",
    "SemanticSearchEngine", 
    "QuestionAnsweringEngine",
    "QAGenerator",
    "CodebaseIndexer"
]
