import nltk
from nltk import pos_tag, ne_chunk
from nltk.tokenize import word_tokenize
from nltk.chunk import tree2conlltags

#Input the text
text = "I may bake a cake for my birthday. The talk will introduce reader about Use of baking"
tokens = word_tokenize(text)

tagged_words = pos_tag(tokens)
grammar = "VP: {<MD><VB>}"
vp_parser = nltk.RegexpParser(grammar)
tree = vp_parser.parse(tagged_words)

verb_phrases = []
for subtree in tree.subtrees():
    if subtree.label() == 'VP':
        verb_phrase = ' '.join(word for word, tag in subtree.leaves())
        verb_phrases.append(verb_phrase)
for verb_phrase in verb_phrases:
    print(verb_phrase)

