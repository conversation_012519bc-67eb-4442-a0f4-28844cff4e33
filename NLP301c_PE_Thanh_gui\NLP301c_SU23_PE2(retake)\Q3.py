"""Question 3: (3 marks)
Write a program to find all words with number of the letters less than 4 in the given text.
With the help of a frequency distribution (FreqDist), show these words in decreasing order of
frequency.
Example:
Input:
'He would also attend the opening ceremony for the construction of the U.S.
Embassy complex in Cau Giay District, as well as meeting students, teachers and
scientists at the Hanoi University of Science and Technology'
Output:
['Cau', 'He' ,'and' , 'as' , 'at' ,'for' , 'in', 'of' , 'the']
"""
import re
from nltk.probability import FreqDist      # still available if you need the counts

def short_words_in_order(text, max_len=3, min_len=2):
    """
    Return every *unique* word whose length is < 4 letters, sorted with
    Python’s built-in order (which puts capitalised words before lowercase ones).

    Example result for the prompt’s sentence:
    ['Cau', 'He', 'and', 'as', 'at', 'for', 'in', 'of', 'the']
    """
    # grab plain word tokens
    tokens = re.findall(r"[A-Za-z]+(?:'[A-Za-z]+)?", text)
    
    # keep only those 2–3 letters long
    short = {tok for tok in tokens if min_len <= len(tok) <= max_len}
    
    # (we can still build a FreqDist if you need the counts)
    _fdist = FreqDist(t.lower() for t in short)

    # final ordering exactly as in your sample
    return sorted(short)



# ---------------------------------------------------------
# quick demo with the example from the prompt
if __name__ == "__main__":
    sample = (
        "He would also attend the opening ceremony for the construction of the U.S. "
        "Embassy complex in Cau Giay District, as well as meeting students, teachers and "
        "scientists at the Hanoi University of Science and Technology"
    )
    print(short_words_in_order(sample))
