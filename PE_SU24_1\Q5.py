from nltk import FreqDist
from nltk.corpus import stopwords
import nltk
text1 = "This is a sample document. This document contains several sentences. We can extract bigrams from it"


def find_50_most_frequent_bigrams(text):
    bigram = list(nltk.bigrams(text))
    print(bigram)
    fdist = FreqDist(b for b in bigram if b[0].isalpha() and b[1].isalpha()
                                      and b[0] not in stopwords.words('english') 
                                      and b[1] not in stopwords.words('english')) 
    return fdist.most_common(50)

print(find_50_most_frequent_bigrams(text1))