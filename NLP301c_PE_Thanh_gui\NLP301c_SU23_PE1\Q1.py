import nltk

# Define  a text
text = "He would also attend the opening ceremony for the construction of the U.S.Embassy complex in Cau Giay District, as well as meeting students, teachers and scientists at the Hanoi University of Science and Technology"

# Tokenize the text into words
words = nltk.word_tokenize(text)

# Find words containing the sequence 'st'
result = [word for word in words if 'st' in word]

# Print the result
print(result)
