""""Write code to print out an index for a lexicon, allowing someone to look up words according
o their meanings (or their pronunciations; whatever properties are contained in the lexical entries).using nltk in natural language processing"""
import nltk
from nltk.corpus import wordnet as wn

# Example lexical entries (for demonstration purposes, using WordNet)
lexicon = [
    {
        "word": "dog",
        "meanings": wn.synsets('dog'),
        "pronunciation": "dɔg"
    },
    {
        "word": "cat",
        "meanings": wn.synsets('cat'),
        "pronunciation": "kæt"
    },
    {
        "word": "apple",
        "meanings": wn.synsets('apple'),
        "pronunciation": "ˈæpəl"
    }
]

# Function to print the index for the lexicon
def print_lexicon_index(lexicon):
    print("Lexicon Index")
    print("-------------")
    for entry in lexicon:
        word = entry["word"]
        meanings = ", ".join([syn.definition() for syn in entry["meanings"]])
        pronunciation = entry["pronunciation"]
        print(f"Word: {word}")
        print(f"Meanings: {meanings}")
        print(f"Pronunciation: {pronunciation}")
        print("-------------")

# Print the lexicon index
print_lexicon_index(lexicon)
