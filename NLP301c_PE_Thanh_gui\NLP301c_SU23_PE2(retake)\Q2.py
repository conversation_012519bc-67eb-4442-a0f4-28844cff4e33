""" Question 2: (2 marks)
Write a Python NLTK program to split all punctuation into separate tokens.
Sample Output:
Original string:
Reset your password if you just can't remember your old one.
Split all punctuation into separate tokens:
['Reset', 'your', 'password', 'if', 'you', 'just', 'ca', "n't", 'remember', 'your', 'old', 'one', '.']' """

from nltk.tokenize import word_tokenize
text = "Reset your password if you just can't remember your old one."
tokens = word_tokenize(text)
print(tokens)
