"""Question 1: (2 marks)
Define a function percent(word, text) that calculates how often a given word occurs in a text
and expresses the result as a percentage."""

def percent(word, text):
    word = word.lower()
    text = text.lower()
    words = text.split()
    total = len(words)
    if total == 0:
        return 0.0
    count = words.count(word)
    return (count / total) * 100

# Example usage
result = percent("apple", "apple banana apple orange apple")
print(result)  # Output: 60.0