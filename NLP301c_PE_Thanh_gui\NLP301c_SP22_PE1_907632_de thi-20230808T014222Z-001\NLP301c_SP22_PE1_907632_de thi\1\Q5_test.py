#Q1
#Define a function percent(word, text)
#that calculates how often a given word occurs in a
# #text and expresses the result as a percentage.
#
from nltk.tokenize import word_tokenize


def percent(word, text):
    # Tokenize the text into words
    words = word_tokenize(text)

    # Calculate the frequency of the word
    word_frequency = words.count(word)

    # Calculate the percentage
    total_words = len(words)
    percentage = (word_frequency / total_words) * 100

    return percentage


# Example usage
text = "NLTK (Natural Language Toolkit) is a Python library that provides tools for processing human language. NLTK helps developers build projects in areas such as sentiment analysis, machine translation, and information extraction."

word = "NLTK"
result = percent(word, text)
print(f"The word '{word}' occurs in the text with a frequency of {result:.2f}%.")



#Q2
#Write a function that takes a text and a vocabulary
# as its arguments and returns the set of words that
# appear in the text but not in the vocabulary. Both arguments can be
# represented as lists of strings.

# def words_not_in_vocab(text, vocabulary):
#     text_set = set(text)
#     vocab_set = set(vocabulary)
#
#     words_not_in_vocab = text_set - vocab_set
#
#     return words_not_in_vocab
#
# text = ["apple", "banana", "orange", "grape", "kiwi"]
# vocabulary = ["apple", "banana", "cherry", "grape"]
#
# result = words_not_in_vocab(text, vocabulary)
# print(result)




#Q3
#Write a Python NLTK program to split all punctuation into separate tokens.
#Sample Output:
#Original string:
#Reset your password if you just can't remember your old one.
#Split all punctuation into separate tokens:
#['Reset', 'your', 'password', 'if', 'you', 'just', 'can', """", 't', 'remember', 'your', 'old', 'one', '']


# import nltk
#
# nltk.download('punkt')
#
# from nltk.tokenize import word_tokenize
#
#
# def split_punctuation(text):
#     tokens = word_tokenize(text)
#     split_tokens = []
#
#     for token in tokens:
#         if token.isalnum():
#             split_tokens.append(token)
#         else:
#             split_tokens.extend(list(token))
#
#     return split_tokens
#
#
# # Sample Input
# original_string = "Reset your password if you just can't remember your old one."
#
# # Tokenize and split punctuation
# split_tokens = split_punctuation(original_string)
#
# # Print results
# print("Original string:")
# print(original_string)
# print("\nSplit all punctuation into separate tokens:")
# print(split_tokens)




#Q4.
#Find the similarity between any two text documents
#Input:
#text1="John lives in Canada"
#text2="James lives in America, though he's not from there"
#Desired Output:
#Similarity between text1 and text2 is 0.792817083631068


# import nltk
# from nltk.corpus import stopwords
# from nltk.tokenize import word_tokenize
# from nltk.stem import PorterStemmer
# from sklearn.feature_extraction.text import TfidfVectorizer
# from sklearn.metrics.pairwise import cosine_similarity
#
# nltk.download('punkt')
# nltk.download('stopwords')
#
#
# def preprocess_text(text):
#     stop_words = set(stopwords.words('english'))
#     stemmer = PorterStemmer()
#
#     words = word_tokenize(text)
#     filtered_words = [stemmer.stem(word) for word in words if word.lower() not in stop_words]
#
#     return ' '.join(filtered_words)
#
#
# def calculate_similarity(text1, text2):
#     preprocessed_text1 = preprocess_text(text1)
#     preprocessed_text2 = preprocess_text(text2)
#
#     corpus = [preprocessed_text1, preprocessed_text2]
#
#     vectorizer = TfidfVectorizer()
#     tfidf_matrix = vectorizer.fit_transform(corpus)
#
#     cosine_sim = cosine_similarity(tfidf_matrix[0], tfidf_matrix[1])
#
#     return cosine_sim[0][0]
#
#
# # Input texts
# text1 = "John lives in Canada"
# text2 = "James lives in America, though he's not from there"
#
# # Calculate similarity
# similarity = calculate_similarity(text1, text2)
#
# # Print results
# print(f"Similarity between text1 and text2 is {similarity}")