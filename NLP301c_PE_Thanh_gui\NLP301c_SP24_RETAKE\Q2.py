""""Question 2: (2 marks)
Write a program to extract and print all non-noun words present in the below text.
Hint: using spacy package
Example:
Input:
"<PERSON> works at Microsoft. She lives in Manchester and likes to play the flute."
Output:
works
at
She
lives
in
and
likes
to
olay
the"""

import spacy

# Load the small English model including POS tags.
nlp = spacy.load("en_core_web_sm")

def extract_and_print_non_nouns(text):
    # Process the text using SpaCy to get tokens and their parts of speech (POS).
    doc = nlp(text)
    
    for token in doc:
        # Check if the part-of-speech tag is not NOUN.
        if token.pos_ != "NOUN":
            print(f"{token.text}")

if __name__ == "__main__":
    sample_text = ("<PERSON> works at Microsoft. She lives in Manchester and likes to play the flute.")
    extract_and_print_non_nouns(sample_text)