{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### 1. Import nltk and download the ‘stopwords’ and ‘punkt’ packages\n", "Difficulty Level : L1\n", "\n", "Q. Import nltk and necessary packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import nltk\n", "nltk.download('punkt')\n", "nltk.download('stop')\n", "nltk.download('stopwords')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2. Import spacy and load the language model\n", "Difficulty Level : L1\n", "\n", "Q. Import spacy library and load ‘en_core_web_sm’ model for english language. Load ‘xx_ent_wiki_sm’ for multi language support."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import spacy\n", "nlp = spacy.load(\"en_core_web_sm\")\n", "nlp"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3. How to tokenize a given text?\n", "Difficulty Level : L1\n", "\n", "Q. Print the tokens of the given text document"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"Last week, the University of Cambridge shared its own research that shows if everyone wears a mask outside home,dreaded ‘second wave’ of the pandemic can be avoided.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "Last\n", "week\n", ",\n", "the\n", "University\n", "of\n", "Cambridge\n", "shared\n", "...(truncated)...\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Method 1\n", "# Tokeniation with nltk\n", "import nltk\n", "tokens=nltk.word_tokenize(text)\n", "for token in tokens:\n", "  print(token)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Method 2\n", "# Tokenization with spaCy\n", "import spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "doc=nlp(text)\n", "for token in doc:\n", "  print(token.text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 4. How to get the sentences of a text document?\n", "Difficulty Level : L1\n", "\n", "Q. Print the sentences of the given text document"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"\"\"The outbreak of coronavirus disease 2019 (COVID-19) has created a global health crisis that has had a deep impact on the way we perceive our world and our everyday lives. Not only the rate of contagion and patterns of transmission threatens our sense of agency, but the safety measures put in place to contain the spread of the virus also require social distancing by refraining from doing what is inherently human, which is to find solace in the company of others. Within this context of physical threat, social and physical distancing, as well as public alarm, what has been (and can be) the role of the different mass media channels in our lives on individual, social and societal levels? Mass media have long been recognized as powerful forces shaping how we experience the world and ourselves. This recognition is accompanied by a growing volume of research, that closely follows the footsteps of technological transformations (e.g. radio, movies, television, the internet, mobiles) and the zeitgeist (e.g. cold war, 9/11, climate change) in an attempt to map mass media major impacts on how we perceive ourselves, both as individuals and citizens. Are media (broadcast and digital) still able to convey a sense of unity reaching large audiences, or are messages lost in the noisy crowd of mass self-communication? \"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "The outbreak of coronavirus disease 2019 (COVID-19) has created a global health crisis that has had a deep impact on the way we perceive our world and our everyday lives.\n", "Not only the rate of contagion and patterns of transmission threatens our sense of agency, but the safety measures put in place to contain the spread of the virus also require social distancing by refraining from doing what is inherently human, which is to find solace in the company of others.\n", "Within this context of physical threat, social and physical distancing, as well as public alarm, what has been (and can be)\n", "...(truncated)...\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Tokenizing the text into sentences with spaCy\n", "import spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "doc=nlp(text)\n", "for sentence in doc.sents:\n", "  print(sentence)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extracting sentences with nltk\n", "nltk.sent_tokenize(text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 5. How to tokenize a text using the `transformers` package?\n", "Difficulty Level : L1\n", "\n", "<PERSON>. Tokenize the given text in encoded form using the tokenizer of Huggingface’s transformer package."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"I love spring season. I go hiking with my friends\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "[101, 1045, 2293, 3500, 2161, 1012, 1045, 2175, 13039, 2007, 2026, 2814, 102]\n", "[CLS] i love spring season. i go hiking with my friends [SEP]\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import tokenizer from transfromers\n", "from transformers import AutoTokenizer\n", "import warnings\n", "warnings.filterwarnings(\"ignore\", category=DeprecationWarning) \n", "\n", "# Initialize the tokenizer\n", "tokenizer=AutoTokenizer.from_pretrained('bert-base-uncased')\n", "\n", "# Encoding with the tokenizer\n", "inputs=tokenizer.encode(text)\n", "print(inputs)\n", "print(tokenizer.decode(inputs))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 6. How to tokenize text with stopwords as delimiters?\n", "Difficulty Level : L2\n", "\n", "<PERSON>. Tokenize the given text with stop words (“is”,”the”,”was”) as delimiters. Tokenizing this way identifies meaningful phrases. Sometimes, useful for topic modeling"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text = \"<PERSON> was feeling anxious. He was diagnosed today. He probably is the best person I know.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Expected Output :\n", "```\n", "['<PERSON>',\n", " 'feeling anxious',\n", " 'He',\n", " 'diagnosed today',\n", " 'He probably',\n", " 'best person I know']\n", "```\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Solution\n", "text = \"<PERSON> was feeling anxious. He was diagnosed today. He probably is the best person I know.\"\n", "\n", "stop_words_and_delims = ['was', 'is', 'the', '.', ',', '-', '!', '?']\n", "for r in stop_words_and_delims:\n", "    text = text.replace(r, 'DELIM')\n", "\n", "words = [t.strip() for t in text.split('DELIM')]\n", "words_filtered = list(filter(lambda a: a not in [''], words))\n", "words_filtered"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### 7. How to remove stop words in a text ?\n", "Difficulty Level : L1\n", "\n", "<PERSON>. Remove all the stopwords ( ‘a’ , ‘the’, ‘was’…) from the text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"\"\"the outbreak of coronavirus disease 2019 (COVID-19) has created a global health crisis that has had a deep impact on the way we perceive our world and our everyday lives. Not only the rate of contagion and patterns of transmission threatens our sense of agency, but the safety measures put in place to contain the spread of the virus also require social distancing by refraining from doing what is inherently human, which is to find solace in the company of others. Within this context of physical threat, social and physical distancing, as well as public alarm, what has been (and can be) the role of the different mass media channels in our lives on individual, social and societal levels? Mass media have long been recognized as powerful forces shaping how we experience the world and ourselves. This recognition is accompanied by a growing volume of research, that closely follows the footsteps of technological transformations (e.g. radio, movies, television, the internet, mobiles) and the zeitgeist (e.g. cold war, 9/11, climate change) in an attempt to map mass media major impacts on how we perceive ourselves, both as individuals and citizens. Are media (broadcast and digital) still able to convey a sense of unity reaching large audiences, or are messages lost in the noisy crowd of mass self-communication?\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "'outbreak coronavirus disease 2019 ( COVID-19 ) created global health crisis deep impact way perceive world everyday lives . rate contagion patterns transmission threatens sense agency , safety measures place contain spread virus require social distancing refraining inherently human , find solace company . context physical threat , social physical distancing , public alarm , ( ) role different mass media channels lives individual , social societal levels ? Mass media long recognized powerful forces shaping experience world . recognition accompanied growing volume research , closely follows footsteps technological transformations ( e.g. radio , movies , television , internet , mobiles ) zeitgeist ( e.g. cold war , 9/11 , climate change ) attempt map mass media major impacts perceive , individuals citizens . media ( broadcast digital ) able convey sense unity reaching large audiences , messages lost noisy crowd mass self - communication ?'\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Method 1\n", "# Removing stopwords in nltk\n", "\n", "import nltk\n", "from nltk.corpus import stopwords\n", "my_stopwords=set(stopwords.words('english'))\n", "new_tokens=[]\n", "\n", "# Tokenization using word_tokenize()\n", "all_tokens=nltk.word_tokenize(text)\n", "\n", "for token in all_tokens:\n", "  if token not in my_stopwords:\n", "    new_tokens.append(token)\n", "\n", "\n", "\" \".join(new_tokens)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Method 2\n", "# Removing stopwords in spaCy\n", "\n", "import spacy\n", "nlp = spacy.load(\"en_core_web_sm\")\n", "doc=nlp(text)\n", "new_tokens=[]\n", "\n", "# Using is_stop attribute of each token to check if it's a stopword\n", "for token in doc:\n", "  if token.is_stop==False:\n", "    new_tokens.append(token.text)\n", "\n", "\" \".join(new_tokens)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 8. How to add custom stop words in spaCy ?\n", "Difficulty Level : L1\n", "\n", "<PERSON><PERSON> Add the custom stopwords “NIL” and “JUNK” in spaCy and remove the stopwords in below text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\" <PERSON> was a JUNK great guy NIL Adam was evil NIL Martha JUNK was more of a fool \""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Expected Output :\n", "```\n", "'Jonas great guy Adam evil Martha fool'\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import spacy\n", "nlp = spacy.load(\"en_core_web_sm\")\n", "\n", "# list of custom stop words\n", "customize_stop_words = ['NIL','JUNK']\n", "\n", "# Adding these stop words\n", "for w in customize_stop_words:\n", "    nlp.vocab[w].is_stop = True\n", "doc = nlp(text.strip())\n", "tokens = [token.text for token in doc if not token.is_stop]\n", "\n", "\" \".join(tokens)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 9. How to remove punctuations ?\n", "Difficulty Level : L1\n", "\n", "<PERSON>. Remove all the punctuations in the given text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"The match has concluded !!! India has won the match . Will we fin the finals too ? !\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "'The match has concluded India has won the match Will we fin the finals too'\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Method 1\n", "# Removing punctuations in spaCy\n", "import spacy\n", "nlp = spacy.load(\"en_core_web_sm\")\n", "\n", "doc=nlp(text)\n", "new_tokens=[]\n", "# Check if a token is a punctuation through is_punct attribute\n", "for token in doc:\n", "  if token.is_punct==False:\n", "    new_tokens.append(token.text)\n", "\n", "\" \".join(new_tokens)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Method 2\n", "# Removing punctuation in nltk with RegexpTokenizer\n", "import nltk\n", "tokenizer=nltk.RegexpTokenizer(r\"\\w+\")\n", "\n", "tokens=tokenizer.tokenize(text)\n", "\" \".join(tokens)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 10. How to perform stemming\n", "Difficulty Level : L2\n", "\n", "Q. Perform stemming/ convert each token to it’s root form in the given text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text= \"Dancing is an art. Students should be taught dance as a subject in schools . I danced in many of my school function. Some people are always hesitating to dance.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "'danc is an art . student should be taught danc as a subject in school . I danc in mani of my school function . some peopl are alway hesit to danc .'\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Stemming with nltk's <PERSON><PERSON><PERSON><PERSON>\n", "import nltk\n", "from nltk.stem import PorterStemmer\n", "\n", "stemmer=PorterStemmer()\n", "stemmed_tokens=[]\n", "for token in nltk.word_tokenize(text):\n", "  stemmed_tokens.append(stemmer.stem(token))\n", "\n", "\" \".join(stemmed_tokens)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 11. How to lemmatize a given text ?\n", "Difficulty Level : L2\n", "\n", "Q. Perform lemmatzation on the given text\n", "\n", "\n", "Hint: Lemmatization Approaches"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text= \"Dancing is an art. Students should be taught dance as a subject in schools . I danced in many of my school function. Some people are always hesitating to dance.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "'dancing be an art . student should be teach dance as a subject in school . -PRON- dance in many of -PRON- school function . some people be always hesitate to dance .'\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Lemmatization using spacy's lemma_ attribute of token\n", "import spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "doc=nlp(text)\n", "\n", "lemmatized=[token.lemma_ for token in doc]\n", "\" \".join(lemmatized)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 12. How to extract usernames from emails ?\n", "Difficulty Level : L2\n", "\n", "Q. Extract the usernames from the email addresses present in the text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text= \"The new <NAME_EMAIL> , <EMAIL>. If you find any disruptions, <NAME_EMAIL> or <EMAIL> \""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "['potter709', 'elixir101', 'granger111', 'severus77']\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Using regular expression to extract usernames\n", "import re  \n", "\n", "# \\S matches any non-whitespace character \n", "# @ for as in the Email \n", "# + for Repeats a character one or more times \n", "usernames= re.findall('(\\S+)@', text)     \n", "print(usernames) "]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 13. How to find the most common words in the text excluding stopwords\n", "Difficulty Level : L2\n", "\n", "Q. Extract the top 10 most common words in the given text excluding stopwords."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"\"\"Junkfood - Food that do no good to our body. And there's no need of them in our body but still we willingly eat them because they are great in taste and easy to cook or ready to eat. Junk foods have no or very less nutritional value and irrespective of the way they are marketed, they are not healthy to consume.The only reason of their gaining popularity and increased trend of consumption is \n", "that they are ready to eat or easy to cook foods. People, of all age groups are moving towards Junkfood as it is hassle free and often ready to grab and eat. Cold drinks, chips, noodles, pizza, burgers, French fries etc. are few examples from the great variety of junk food available in the market.\n", " Junkfood is the most dangerous food ever but it is pleasure in eating and it gives a great taste in mouth examples of Junkfood are kurkure and chips.. cold rings are also source of junk food... they shud nt be ate in high amounts as it results fatal to our body... it cn be eated in a limited extend ... in research its found tht ths junk foods r very dangerous fr our health\n", "Junkfood is very harmful that is slowly eating away the health of the present generation. The term itself denotes how dangerous it is for our bodies. Most importantly, it tastes so good that people consume it on a daily basis. However, not much awareness is spread about the harmful effects of Junkfood .\n", "The problem is more serious than you think. Various studies show that Junkfood impacts our health negatively. They contain higher levels of calories, fats, and sugar. On the contrary, they have very low amounts of healthy nutrients and lack dietary fibers. Parents must discourage their children from consuming junk food because of the ill effects it has on one’s health.\n", "Junkfood is the easiest way to gain unhealthy weight. The amount of fats and sugar in the food makes you gain weight rapidly. However, this is not a healthy weight. It is more of fats and cholesterol which will have a harmful impact on your health. Junk food is also one of the main reasons for the increase in obesity nowadays.\n", "This food only looks and tastes good, other than that, it has no positive points. The amount of calorie your body requires to stay fit is not fulfilled by this food. For instance, foods like French fries, burgers, candy, and cookies, all have high amounts of sugar and fats. Therefore, this can result in long-term illnesses like diabetes and high blood pressure. This may also result in kidney failure.\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "text= {Junkfood: 10,\n", " food: 8,\n", " good: 5,\n", " harmful : 3\n", " body: 1,\n", " need: 1,\n", "\n", " ...(truncated)\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Creating spacy doc of the text\n", "\n", "import spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "doc=nlp(text)\n", "\n", "# Removal of stop words and punctuations\n", "words=[str(token).strip() for token in doc if token.is_stop==False and token.is_punct==False]\n", "\n", "freq_dict={}\n", "\n", "# Calculating frequency count\n", "for word in words:\n", "  if word not in freq_dict:\n", "    freq_dict[word]=1\n", "  else:\n", "    freq_dict[word]+=1\n", "\n", "print(freq_dict)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 14. How to do spell correction in a given text ?\n", "Difficulty Level : L2\n", "\n", "<PERSON><PERSON> Correct the spelling errors in the following text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"He is a gret person. He beleives in bod\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "text=\"He is a great person. He believes in god\"\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import textblob\n", "from textblob import TextBlob\n", "import warnings\n", "warnings.filterwarnings(\"ignore\", category=DeprecationWarning) \n", "\n", "# Using textb<PERSON>b's correct() function\n", "text=TextBlob(text)\n", "print(text.correct())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 15. How to tokenize tweets ?\n", "Difficulty Level : L2\n", "\n", "Q. Clean the following tweet and tokenize them"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\" Having lots of fun #goa #vaction #summervacation. Fancy dinner @Beachbay restro :) \""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "['Having',\n", " 'lots',\n", " 'of',\n", " 'fun',\n", " 'goa',\n", " 'vaction',\n", " 'summervacation',\n", " 'Fancy',\n", " 'dinner',\n", " 'Beachbay',\n", " 'restro']\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "# Cleaning the tweets\n", "text=re.sub(r'[^\\w]', ' ', text)\n", "\n", "# Using nltk's TweetTokenizer\n", "from nltk.tokenize import TweetTokenizer\n", "tokenizer=TweetTokenizer()\n", "tokenizer.tokenize(text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 16. How to extract all the nouns in a text?\n", "Difficulty Level : L2\n", "\n", "Q. Extract and print all the nouns present in the below text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"<PERSON> works at Microsoft. She lives in manchester and likes to play the flute\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "<PERSON>\n", "Microsoft\n", "manchester\n", "flute\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Coverting the text into a spacy Doc\n", "import spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "doc=nlp(text)\n", "\n", "# Using spacy's pos_ attribute to check for part of speech tags\n", "for token in doc:\n", "  if token.pos_=='NOUN' or token.pos_=='PROPN':\n", "    print(token.text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 17. How to extract all the pronouns in a text?\n", "Difficulty Level : L2\n", "\n", "Q. Extract and print all the pronouns in the text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"<PERSON> is happy finally. He had landed his dream job finally. He told his mom. She was elated \""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", " He\n", " He\n", " She\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Using spacy's pos_ attribute to check for part of speech tags\n", "import spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "doc=nlp(text)\n", "\n", "for token in doc:\n", "  if token.pos_=='PRON':\n", "    print(token.text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 18. How to find similarity between two words?\n", "Difficulty Level : L2\n", "\n", "Find the similarity between any two words."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["word1=\"amazing\"\n", "word2=\"terrible\"\n", "word3=\"excellent\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "similarity between amazing and terrible is 0.46189071343764604\n", "similarity between amazing and excellent is 0.6388207086737778\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Convert words into spacy tokens\n", "import spacy\n", "nlp=spacy.load('en_core_web_lg')\n", "\n", "token1=nlp(word1)\n", "token2=nlp(word2)\n", "token3=nlp(word3)\n", "\n", "# Use similarity() function of tokens\n", "print('similarity between', word1,'and' ,word2, 'is' ,token1.similarity(token2))\n", "print('similarity between', word1,'and' ,word3, 'is' ,token1.similarity(token3))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 19. How to find similarity between two documents?\n", "Difficulty Level : L2\n", "\n", "Q. Find the similarity between any two text documents"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text1=\"<PERSON> lives in Canada\"\n", "text2=\"<PERSON> lives in America, though he's not from there\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "0.792817083631068\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Method 1\n", "# Finding similarity using spacy library\n", "import spacy\n", "import warnings\n", "warnings.filterwarnings(\"ignore\", category=UserWarning) \n", "\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "\n", "doc1=nlp(text1)\n", "doc2=nlp(text2)\n", "print(doc1.similarity(doc2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Method 2\n", "from nltk.corpus import wordnet\n", "\n", "list1 = text1.split(\" \")\n", "list2 = text2.split(\" \")\n", "\n", "lst = []\n", "for word1 in list1:\n", "    for word2 in list2:\n", "        wordFromList1 = wordnet.synsets(word1)\n", "        wordFromList2 = wordnet.synsets(word2)\n", "        if wordFromList1 and wordFromList2: #Thanks to @alexis' note\n", "            s = wordFromList1[0].wup_similarity(wordFromList2[0])\n", "            lst.append(s)\n", "            \n", "s = 0\n", "for i in lst:\n", "    s += i\n", "\n", "print(1- s/len(lst))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 20. How to find the cosine similarity of two documents?\n", "Difficulty Level : L3\n", "\n", "Q. Find the cosine similarity between two given documents"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text1='Taj Mahal is a tourist place in India'\n", "text2='Great Wall of China is a tourist place in china'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "[[1.         0.45584231]\n", " [0.45584231 1.        ]]\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Using Vectorizer of sklearn to get vector representation\n", "\n", "from sklearn.feature_extraction.text import CountVectorizer\n", "import pandas as pd\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "\n", "documents=[text1,text2]\n", "\n", "vectorizer=CountVectorizer()\n", "matrix=vectorizer.fit_transform(documents)\n", "\n", "# Obtaining the document-word matrix\n", "doc_term_matrix=matrix.todense()\n", "doc_term_matrix\n", "\n", "# Computing cosine similarity\n", "df=pd.DataFrame(doc_term_matrix)\n", "\n", "print(cosine_similarity(df,df))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 21. How to find soft cosine similarity of documents ?\n", "Difficulty Level : L3\n", "\n", "<PERSON><PERSON> Compute the soft cosine similarity of the given documents\n", "\n", "\n", "Hint: Soft Cosine Similarity"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doc_soup = \"Soup is a primarily liquid food, generally served warm or hot (but may be cool or cold), that is made by combining ingredients of meat or vegetables with stock, juice, water, or another liquid. \"\n", "doc_noodles = \"Noodles are a staple food in many cultures. They are made from unleavened dough which is stretched, extruded, or rolled flat and cut into one of a variety of shapes.\"\n", "doc_dosa = \"Dosa is a type of pancake from the Indian subcontinent, made from a fermented batter. It is somewhat similar to a crepe in appearance. Its main ingredients are rice and black gram.\"\n", "doc_trump = \"Mr. <PERSON> became president after winning the political election. Though he lost the support of some republican friends, <PERSON> is friends with President <PERSON>\"\n", "doc_election = \"President <PERSON> says <PERSON> had no political interference is the election outcome. He says it was a witchhunt by political parties. He claimed President <PERSON> is a friend who had nothing to do with the election\"\n", "doc_putin = \"Post elections, <PERSON> became President of Russia. President <PERSON> had served as the Prime Minister earlier in his political career\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "0.5842470477718544\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings(\"ignore\", category=DeprecationWarning) \n", "\n", "# Import and download stopwords from NLTK.\n", "from nltk.corpus import stopwords\n", "from nltk import download\n", "download('stopwords')  # Download stopwords list.\n", "stop_words = stopwords.words('english')\n", "\n", "# Preprocess the sentences\n", "def preprocess(sentence):\n", "    return [w for w in sentence.lower().split() if w not in stop_words]\n", "\n", "doc_soup = preprocess(doc_soup)\n", "doc_noodles = preprocess(doc_noodles)\n", "doc_dosa = preprocess(doc_dosa)\n", "doc_trump = preprocess(doc_trump)\n", "doc_election = preprocess(doc_election)\n", "doc_putin = preprocess(doc_putin)\n", "\n", "# Build a dictionary and an TF-IDF model, convert the sentences to the bag-of-words format\n", "from gensim.corpora import Dictionary\n", "documents = [doc_soup, doc_noodles, doc_dosa, doc_trump, doc_election, doc_putin]\n", "dictionary = Dictionary(documents)\n", "\n", "doc_soup = dictionary.doc2bow(doc_soup)\n", "doc_noodles = dictionary.doc2bow(doc_noodles)\n", "doc_dosa = dictionary.doc2bow(doc_dosa)\n", "doc_trump = dictionary.doc2bow(doc_trump)\n", "doc_election = dictionary.doc2bow(doc_election)\n", "doc_putin = dictionary.doc2bow(doc_putin)\n", "\n", "from gensim.models import TfidfModel\n", "documents = [doc_soup, doc_noodles, doc_dosa, doc_trump, doc_election, doc_putin]\n", "tfidf = TfidfModel(documents)\n", "\n", "doc_soup = tfidf[doc_soup]\n", "doc_noodles = tfidf[doc_noodles]\n", "doc_dosa = tfidf[doc_dosa]\n", "doc_trump = tfidf[doc_trump]\n", "doc_election = tfidf[doc_election]\n", "doc_putin = tfidf[doc_putin]\n", "\n", "# Download the FastText model\n", "import gensim.downloader as api\n", "model = api.load('fasttext-wiki-news-subwords-300')\n", "# model = api.load('word2vec-google-news-300')\n", "\n", "# Prepare the similarity matrix\n", "from gensim.similarities import SparseTermSimilarityMatrix, WordEmbeddingSimilarityIndex\n", "termsim_index = WordEmbeddingSimilarityIndex(model)\n", "termsim_matrix = SparseTermSimilarityMatrix(termsim_index, dictionary, tfidf)\n", "\n", "# Compute SCM using the inner_product method\n", "similarity = termsim_matrix.inner_product(doc_soup, doc_noodles, normalized=(True, True))\n", "print('similarity = %f' % similarity)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare the soft cosines for all documents against each other\n", "import numpy as np\n", "import pandas as pd\n", "\n", "sentences = [doc_soup, doc_noodles, doc_dosa, doc_trump, doc_election, doc_putin]\n", "\n", "def create_soft_cossim_matrix(sentences):\n", "    len_array = np.arange(len(sentences))\n", "    xx, yy = np.meshgrid(len_array, len_array)\n", "    cossim_mat = pd.DataFrame([[round(termsim_matrix.inner_product(sentences[i],sentences[j], normalized=(True, True)), 2) for i, j in zip(x,y)] for y, x in zip(xx, yy)])\n", "    return cossim_mat\n", "\n", "create_soft_cossim_matrix(sentences)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 22. How to find similar words using pre-trained Word2Vec?\n", "Difficulty Level : L2\n", "\n", "Q. Find all similiar words to “amazing” using Google news Word2Vec."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "[('incredible', 0.90),\n", "('awesome', 0.82),\n", "('unbelievable', 0.82),\n", "('fantastic', 0.77),\n", "('phenomenal', 0.76),\n", "('astounding', 0.73),\n", "('wonderful', 0.72),\n", "('unbelieveable', 0.71),\n", "('remarkable', 0.70),\n", "('marvelous', 0.70)]\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings(\"ignore\", category=DeprecationWarning) \n", "\n", "# Import gensim api\n", "import gensim.downloader as api\n", "\n", "# Load the pretrained google news word2vec model\n", "word2vec_model300 = api.load('word2vec-google-news-300')\n", "\n", "# Using most_similar() function\n", "word2vec_model300.most_similar('amazing')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 23. How to compute Word mover distance?\n", "Difficulty Level : L3\n", "\n", "<PERSON><PERSON>mpute the word mover distance between given two texts"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sentence_orange = 'Oranges are my favorite fruit'\n", "sent=\"apples are not my favorite\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "5.378\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings(\"ignore\", category=DeprecationWarning) \n", "\n", "# Import and download stopwords from NLTK.\n", "from nltk.corpus import stopwords\n", "from nltk import download\n", "download('stopwords')  # Download stopwords list.\n", "stop_words = stopwords.words('english')\n", "\n", "def preprocess(sentence):\n", "    return [w for w in sentence.lower().split() if w not in stop_words]\n", "\n", "sentence_orange = preprocess(sentence_orange)\n", "sent = preprocess(sent)\n", "\n", "# Importing g<PERSON><PERSON>'s model\n", "import gensim.downloader as api\n", "model = api.load('word2vec-google-news-300')\n", "\n", "# Computing the word mover distance\n", "distance = model.wmdistance(sent, sentence_orange)\n", "print(distance)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 24. How to replace all the pronouns in a text with their respective object names\n", "Difficulty Level : L2\n", "\n", "<PERSON>. Replace the pronouns in below text by the respective object names"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\" My sister has a dog and she loves him\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "[My sister, she]\n", "[a dog, him ]\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # NOT WORKING\n", "# # Import neural coref library\n", "# # neuralcoref only works with spacy v2, specifically: spacy==2.1.0, neuralcoref==4.0\n", "# import spacy\n", "# import neuralcoref\n", "\n", "# # Add it to the pipeline\n", "# nlp = spacy.load('en')\n", "# neuralcoref.add_to_pipe(nlp)\n", "\n", "# # Printing the coreferences\n", "# doc1 = nlp('My sister has a dog. She loves him.')\n", "# print(doc1._.coref_clusters)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import spacy\n", "\n", "text = \"My sister has a dog and she loves him\" \n", "\n", "nlp = spacy.load('en_core_web_sm')\n", "doc = nlp(text)\n", "\n", "pronouns = []\n", "objects = []\n", "for token in doc:\n", "    if token.pos_ == \"PRON\" and token.head.pos_ != \"PRON\":\n", "        pronouns.append(token.text)\n", "        objects.append(token.head.text)\n", "    elif token.pos_ == \"PRON\" and token.head.pos_ == \"PRON\":  \n", "        pronouns.append(token.text)\n", "        objects.append(token.head.head.text)\n", "\n", "print(pronouns)\n", "print(objects)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 25. How to extract topic keywords using LSA?\n", "Difficulty Level : L3\n", "\n", "Q. Extract the topic keywords from the given texts using LSA(Latent Semantic Analysis )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["texts= [\"\"\"It's all about travel. I travel a lot.  those who do not travel read only a page.” – said <PERSON>. He was a great travel person. Travelling can teach you more than any university course. You learn about the culture of the country you visit. If you talk to locals, you will likely learn about their thinking, habits, traditions and history as well.If you travel, you will not only learn about foreign cultures, but about your own as well. You will notice the cultural differences, and will find out what makes your culture unique. After retrurning from a long journey, you will see your country with new eyes.\"\"\",\n", "        \"\"\" You can learn a lot about yourself through travelling. You can observe how you feel beeing far from your country. You will find out how you feel about your homeland.You should travel You will realise how you really feel about foreign people. You will find out how much you know/do not know about the world. You will be able to observe how you react in completely new situations. You will test your language, orientational and social skills. You will not be the same person after returning home.During travelling you will meet people that are very different from you. If you travel enough, you will learn to accept and appreciate these differences. Traveling makes you more open and accepting.\"\"\",\n", "        \"\"\"Some of my most cherished memories are from the times when I was travelling. If you travel, you can experience things that you could never experience at home. You may see beautiful places and landscapes that do not exist where you live. You may meet people that will change your life, and your thingking. You may try activities that you have never tried before.Travelling will inevitably make you more independent and confident. You will realise that you can cope with a lot of unexpected situations. You will realise that you can survive without all that help that is always available for you at home. You will likely find out that you are much stronger and braver than you have expected.\"\"\",\n", "        \"\"\"If you travel, you may learn a lot of useful things. These things can be anything from a new recepie, to a new, more effective solution to an ordinary problem or a new way of creating something.Even if you go to a country where they speak the same language as you, you may still learn some new words and expressions that are only used there. If you go to a country where they speak a different language, you will learn even more.\"\"\",\n", "        \"\"\"After arriving home from a long journey, a lot of travellers experience that they are much more motivated than they were before they left. During your trip you may learn things that you will want to try at home as well. You may want to test your new skills and knowledge. Your experiences will give you a lot of energy.During travelling you may experience the craziest, most exciting things, that will eventually become great stories that you can tell others. When you grow old and look back at your life and all your travel experiences, you will realise how much you have done in your life and your life was not in vain. It can provide you with happiness and satisfaction for the rest of your life.\"\"\",\n", "        \"\"\"The benefits of travel are not just a one-time thing: travel changes you physically and psychologically. Having little time or money isn't a valid excuse. You can travel for cheap very easily. If you have a full-time job and a family, you can still travel on the weekends or holidays, even with a baby. travel  more is likely to have a tremendous impact on your mental well-being, especially if you're no used to going out of your comfort zone. Trust me: travel more and your doctor will be happy. Be sure to get in touch with your physician, they might recommend some medication to accompany you in your travels, especially if you're heading to regions of the globe with potentially dangerous diseases.\"\"\",\n", "        \"\"\"Sure, you probably feel comfortable where you are, but that is just a fraction of the world! If you are a student, take advantage of programs such as Erasmus to get to know more people, experience and understand their culture. Dare traveling to regions you have a skeptical opinion about. I bet that you will change your mind and realize that everything is not so bad abroad.\"\"\",\n", "        \"\"\" So, travel makes you cherish life. Let's travel more . Share your travel diaries with us too\"\"\"\n", "        ]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "Topic 0: \n", "learn new life travelling country feel  \n", "Topic 1: \n", "life cherish diaries let share experience  \n", "Topic 2: \n", "feel know time people just regions  \n", "Topic 3: \n", "time especially cherish diaries let share  \n", "..(truncated)..\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Importing the Tf-idf vectorizer from sklearn\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "\n", "# Defining the vectorizer\n", "vectorizer = TfidfVectorizer(stop_words='english', max_features= 1000,  max_df = 0.5, smooth_idf=True)\n", "\n", "# Transforming the tokens into the matrix form through .fit_transform()\n", "matrix= vectorizer.fit_transform(texts)\n", "\n", "# SVD represent documents and terms in vectors\n", "from sklearn.decomposition import TruncatedSVD\n", "SVD_model = TruncatedSVD(n_components=10, algorithm='randomized', n_iter=100, random_state=122)\n", "SVD_model.fit(matrix)\n", "\n", "# Getting the terms \n", "terms = vectorizer.get_feature_names_out()\n", "\n", "# Iterating through each topic\n", "for i, comp in enumerate(SVD_model.components_):\n", "    terms_comp = zip(terms, comp)\n", "    # sorting the 7 most important terms\n", "    sorted_terms = sorted(terms_comp, key= lambda x:x[1], reverse=True)[:7]\n", "    print(\"Topic \"+str(i)+\": \")\n", "    # printing the terms of a topic\n", "    for t in sorted_terms:\n", "        print(t[0],end=' ')\n", "    print(' ')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}