{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["*** Introductory Examples for the NLTK Book ***\n", "Loading text1, ..., text9 and sent1, ..., sent9\n", "Type the name of the text or sentence to view it.\n", "Type: 'texts()' or 'sents()' to list the materials.\n", "text1: <PERSON><PERSON> by <PERSON> 1851\n", "text2: Sense and Sensibility by <PERSON> 1811\n", "text3: The Book of Genesis\n", "text4: Inaugural Address Corpus\n", "text5: <PERSON><PERSON>\n", "text6: <PERSON> and the Holy Grail\n", "text7: Wall Street Journal\n", "text8: Personals Corpus\n", "text9: The Man Who Was Thursday by <PERSON> <PERSON> <PERSON> <PERSON> 1908\n"]}], "source": ["import nltk\n", "from nltk.book import *\n", "from nltk.corpus import gutenberg\n", "from nltk.corpus import brown\n", "from nltk.corpus import webtext\n", "from nltk.corpus import state_union\n", "from nltk.corpus import wordnet as wn\n", "from nltk.corpus import stopwords\n", "import matplotlib.pyplot as plt\n", "import random\n", "from nltk.corpus import udhr"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**1. Create a variable ** `phrase` ** containing a list of words. Review the operations described in the previous chapter, including addition, multiplication, indexing, slicing, and sorting.**"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['hello', 'natural', 'language', 'processing', 'python', 'hello', 'natural', 'language', 'processing', 'python']\n", "['hello', 'natural', 'language', 'processing', 'python', 'hello', 'natural', 'language', 'processing', 'python', 'hello', 'natural', 'language', 'processing', 'python']\n", "hello\n", "['natural', 'language', 'processing', 'python']\n", "['hello', 'language', 'natural', 'processing', 'python']\n"]}], "source": ["phrase = ['hello', 'natural', 'language', 'processing']\n", "phrase.append('python')\n", "print(phrase + phrase)\n", "print(phrase * 3)\n", "print(phrase[0])\n", "print(phrase[1:])\n", "print(sorted(phrase))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**2. Use the corpus module to explore ** `austen-persuasion.txt`**. How many word tokens does this book have? How many word types?**"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["84121\n", "5739\n"]}], "source": ["persuasion = gutenberg.words('austen-persuasion.txt')\n", "print(len([word for word in persuasion if word.isalpha()]))\n", "print(len(set(word.lower() for word in persuasion if word.isalpha())))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**3. Use the Brown corpus reader ** `nltk.corpus.brown.words()` ** or the Web text corpus reader ** `nltk.corpus.webtext.words()` ** to access some sample text in two different genres.**"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['It', 'is', 'not', 'news', 'that', '<PERSON>', ...]\n", "['It', 'was', 'among', 'these', 'that', 'Hinkle', ...]\n", "['<PERSON><PERSON>', 'Manager', ':', '\"', 'Don', \"'\", 't', ...]\n"]}], "source": ["print(brown.words(categories='reviews'))\n", "print(brown.words(categories='humor'))\n", "print(webtext.words(fileids='firefox.txt'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**4. Read in the texts of the ** *State of the Union* ** addresses, using the ** `state_union` ** corpus reader. Count occurrences of ** `men`**,** `women`**, and** `people` ** in each document. What has happened to the usage of these words over time?**"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<AxesSubplot: xlabel='Samples', ylabel='Counts'>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["cfd = nltk.ConditionalFreqDist(\n", "          (target, fileid[:4])\n", "          for fileid in state_union.fileids()\n", "          for w in state_union.words(fileid)\n", "          for target in ['men', 'women', 'people']\n", "          if w.lower().startswith(target))\n", "cfd.plot()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**5. Investigate the holonym-meronym relations for some nouns. Remember that there are three kinds of holonym-meronym relation, so you need to use: ** `member_meronyms()`, `part_meronyms()`, `substance_meronyms()`, `member_holonyms()`, `part_holonyms()`**, and ** `substance_holonyms()` **.**"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[<PERSON><PERSON><PERSON>('busbar.n.01'), <PERSON><PERSON><PERSON>('cathode-ray_tube.n.01'), <PERSON><PERSON><PERSON>('central_processing_unit.n.01'), <PERSON><PERSON><PERSON>('chip.n.07'), <PERSON><PERSON><PERSON>('computer_accessory.n.01'), <PERSON><PERSON><PERSON>('computer_circuit.n.01'), <PERSON><PERSON><PERSON>('data_converter.n.01'), <PERSON><PERSON><PERSON>('disk_cache.n.01'), <PERSON><PERSON><PERSON>('diskette.n.01'), <PERSON><PERSON><PERSON>('hardware.n.03'), <PERSON><PERSON><PERSON>('keyboard.n.01'), <PERSON><PERSON><PERSON>('memory.n.04'), <PERSON><PERSON><PERSON>('monitor.n.04'), <PERSON><PERSON><PERSON>('peripheral.n.01')]\n", "[Synset('platform.n.03')]\n", "\n", "[Synset('person.n.01')]\n", "[S<PERSON><PERSON>('world.n.08')]\n", "\n", "[Synset('cellulose.n.01')]\n"]}], "source": ["computer = wn.synset('computer.n.01')\n", "print(computer.part_meronyms())\n", "print(computer.part_holonyms())\n", "print()\n", "\n", "people = wn.synset('people.n.01')\n", "print(people.member_meronyms())\n", "print(people.member_holonyms())\n", "print()\n", "\n", "paper = wn.synset('paper.n.01')\n", "print(paper.substance_meronyms())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**6. In the discussion of comparative wordlists, we created an object called ** `translate` ** which you could look up using words in both German and Spanish in order to get corresponding words in English. What problem might arise with this approach? Can you suggest a way to avoid this problem?**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If there're some words both appear in German and Spanish, then the dictionary would have ambiguity.\n", "Add 'de'/'es' before the words and use that as keys in the dictionary."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**7. According to <PERSON><PERSON> and <PERSON>'s ** *Elements of Style* **, the word ** *however* **, used at the start of a sentence, means \"in whatever way\" or \"to whatever extent\", and not \"nevertheless\". They give this example of correct usage: ** *However you advise him, he will probably do as he thinks best.* **(http://www.bartleby.com/141/strunk3.html) Use the concordance tool to study actual usage of this word in the various texts we have been considering. See also the ** *LanguageLog* ** posting \"Fossilized prejudices about 'however'\" at http://itre.cis.upenn.edu/~myl/languagelog/archives/001913.html**"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Displaying 25 of 89 matches:\n", "onceited , silly father . She had , however , one very intimate friend , a sens\n", "early custom . But these measures , however good in themselves , were insuffici\n", "ellynch Hall was to be let . This , however , was a profound secret , not to be\n", "t immediate neighbourhood , which , however , had not suited him ; that acciden\n", "e dues of a tenant . It succeeded , however ; and though Sir <PERSON> must ever l\n", "h , the former curate of Monkford , however suspicious appearances may be , but\n", "good character and appearance ; and however <PERSON> might have asked yet f\n", "siness no evil . She was assisted , however , by that perfect indifference and \n", "h the others . Something occurred , however , to give her a different duty . Ma\n", " , but can never alter plain ones . However , at any rate , as I have a great d\n", "l what is due to you as my sister . However , we may as well go and sit with th\n", "o means of her going . She wished , however to see the Crofts , and was glad to\n", "ithout any approach to coarseness , however , or any want of good humour . Anne\n", "ll be in question . She could not , however , reach such a degree of certainty \n", "al to <PERSON> ' s nerves . She found , however , that it was one to which she must\n", "re gone , she hoped , to be happy , however oddly constructed such happiness mi\n", "once more in the same room . Soon , however , she began to reason with herself \n", " ! It would be but a new creation , however , and I never think much of your ne\n", "rove of Uppercross .\" Her husband , however , would not agree with her here ; f\n", "re presently .\" Captain <PERSON> , however , came from his window , apparently\n", "d <PERSON> stir . In another moment , however , she found herself in the state of\n", " at once . After a short struggle , however , <PERSON> seemed to quit the\n", "rything being to be done together , however undesired and inconvenient . She tr\n", " , nobody answered her . Winthrop , however , or its environs -- for young men \n", "fore they were beyond her hearing , however , <PERSON> spoke again . \" Mary is go\n"]}], "source": ["nltk.Text(persuasion).concordance('However')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**8. Define a conditional frequency distribution over the Names corpus that allows you to see which initial letters are more frequent for males vs. females (cf. 4.4).**"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<AxesSubplot: xlabel='Samples', ylabel='Counts'>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["names = nltk.corpus.names\n", "cfd = nltk.ConditionalFreqDist(\n", "          (fileid, name[0])\n", "          for fileid in names.fileids()\n", "          for name in names.words(fileid))\n", "cfd.plot()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From the figure, we can find that 'w' are more frequent for males."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**9. Pick a pair of texts and study the differences between them, in terms of vocabulary, vocabulary richness, genre, etc. Can you find pairs of words which have quite different meanings across the two texts, such as ** *monstrous* ** in ** *<PERSON><PERSON>* ** and in ** *Sense and Sensibility* **?**"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Vocabulary of news:   14394\n", "Vocabulary of romance: 8452\n", "---------------------------\n", "Vocabulary richness of news:\t 0.14314696580941583\n", "Vocabulary richness of romance:\t 0.12070492131044529\n", "----------------------------------------------------\n", "'Address' in news:\n", "administration legislature date state welfare administrators wife\n", "daughter back he texas speaker face first battle congress just down\n", "bill lawyer\n", "\n", "'Address' in romance:\n", "mass boredom sounds door smell bay dogs heat dead wife bill front back\n", "first place thought head smoothness taste passion\n"]}], "source": ["news_text = brown.words(categories='news')\n", "romance_text = brown.words(categories='romance')\n", "print(\"Vocabulary of news:  \", len(set(news_text)))\n", "print(\"Vocabulary of romance:\", len(set(romance_text)))\n", "print(\"---------------------------\")\n", "print(\"Vocabulary richness of news:\\t\", len(set(news_text)) / len(news_text))\n", "print(\"Vocabulary richness of romance:\\t\", len(set(romance_text)) / len(romance_text))\n", "print(\"----------------------------------------------------\")\n", "print(\"'Address' in news:\")\n", "nltk.Text(news_text).similar('address')\n", "print()\n", "print(\"'Address' in romance:\")\n", "nltk.Text(romance_text).similar('address')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**10. Read the BBC News article: ** *UK's <PERSON> 'left behind'* ** http://news.bbc.co.uk/1/hi/education/6173441.stm. The article gives the following statistic about teen language: \"the top 20 words used, including yeah, no, but and like, account for around a third of all words.\" How many word types account for a third of all word tokens, for a variety of text sources? What do you conclude about this statistic? Read more about this on ** *LanguageLog* **, at http://itre.cis.upenn.edu/~myl/languagelog/archives/003993.html.**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Word types: 20  \n", "Word tokens: `len(text)`  \n", "The proportion is close to zero.  \n", "The teen language is simplified nowadays."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**11. Investigate the table of modal distributions and look for other patterns. Try to explain them in terms of your own impressionistic understanding of the different genres. Can you find other closed classes of words that exhibit significant differences across different genres?**"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                  can could   may might  must  will \n", "           news    94    87    93    38    53   389 \n", "       religion    84    59    79    12    54    72 \n", "        hobbies   276    59   143    22    84   269 \n", "science_fiction    16    49     4    12     8    17 \n", "        romance    79   195    11    51    46    49 \n", "          humor    17    33     8     8     9    13 \n"]}], "source": ["# the code is a bit different from that in the book\n", "# because I convert the words to lower case\n", "modals = ['can', 'could', 'may', 'might', 'must', 'will']\n", "cfd = nltk.ConditionalFreqDist(\n", "            (genre, word.lower())                                               # to count the Uppercased words as well, or the statistics would be inconsistent\n", "            for genre in brown.categories()\n", "            for word in brown.words(categories=genre))\n", "genres = ['news', 'religion', 'hobbies', 'science_fiction', 'romance', 'humor']         # genres = brown.categories()\n", "cfd.tabulate(conditions=genres, samples=modals)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In news, many events would lead to predicable consequences, so the use of 'will' is most frequent.  \n", "In religion, the use of modals are of little difference.  \n", "In hobbies, we can often benefit from our hobbies and get something. Thus, the use of 'can', 'may' and 'will' is high.  \n", "In science_fiction and humor, the use of modals is rare. There contexts generally don't need modals.  \n", "In romance, the use of 'could' is frequent maybe because it's tactful.  \n", "Well, these are just my personal idea. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["**12. The CMU Pronouncing Dictionary contains multiple pronunciations for certain words. How many distinct words does it contain? What fraction of words in this dictionary have more than one possible pronunciation?**"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Distinct words: 123455\n", "Fractions of words with more than one possible pronunciation: 0.07485318537118789\n"]}], "source": ["# entries = nltk.corpus.cmudict.entries()          to count distinct, use dict() rather than entries()\n", "prondict = nltk.corpus.cmudict.dict()\n", "print('Distinct words:', len(prondict))\n", "\n", "# count words in the dictionary that have more than one possible pronunciation\n", "# iterate over the dict and find those whose values' length is greater than 1\n", "wordPron = 0\n", "for key in prondict:\n", "    if len(prondict[key]) > 1:\n", "        wordPron += 1\n", "print('Fractions of words with more than one possible pronunciation:', wordPron / len(prondict))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**13. What percentage of noun synsets have no hyponyms? You can get all noun synsets using** `wn.all_synsets('n')`."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.7967119283931072\n"]}], "source": ["noun_synsets = len(list(wn.all_synsets('n')))               # the number of noun synsets\n", "cnt = 0                                                     # counter for noun synsets with no hyponyms\n", "for synset in wn.all_synsets('n'):\n", "    if (synset.hyponyms() == []):\n", "        cnt += 1\n", "print(cnt / noun_synsets)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**14. Define a function ** `supergloss(s)` ** that takes a synset ** `s` ** as its argument and returns a string consisting of the concatenation of the definition of ** `s`**, and the definitions of all the hypernyms and hyponyms of ** `s`**.**"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"collapsed": true}, "outputs": [], "source": ["def supergloss(s):\n", "    defis = ''\n", "    defis = defis + s.name() + ': ' + s.definition() + '\\n'\n", "    for synset in s.hypernyms():\n", "        defis = defis + synset.name() + ': ' + synset.definition() + '\\n'\n", "    for synset in s.hyponyms():\n", "        defis = defis + synset.name() + ': ' + synset.definition() + '\\n'\n", "    return defis"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["computer.n.01: a machine for performing calculations automatically\n", "machine.n.01: any mechanical or electrical device that transmits or modifies energy to perform or assist in the performance of human tasks\n", "analog_computer.n.01: a computer that represents information by variable quantities (e.g., positions or voltages)\n", "digital_computer.n.01: a computer that represents information by numerical (binary) digits\n", "home_computer.n.01: a computer intended for use in the home\n", "node.n.08: (computer science) any computer that is hooked up to a computer network\n", "number_cruncher.n.02: a computer capable of performing a large number of mathematical operations per second\n", "pari-mutuel_machine.n.01: computer that registers bets and divides the total amount bet among those who won\n", "predictor.n.03: a computer for controlling antiaircraft fire that computes the position of an aircraft at the instant of a shell's arrival\n", "server.n.03: (computer science) a computer that provides client stations with access to files and printers as shared resources to a computer network\n", "turing_machine.n.01: a hypothetical computer with an infinitely long memory tape\n", "web_site.n.01: a computer connected to the internet that maintains a series of web pages on the World Wide Web\n", "\n"]}], "source": ["print(supergloss(computer))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**15. Write a program to find all words that occur at least three times in the Brown Corpus.**"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["wordSet = []                              # create an empty list\n", "# frequency distribution for words in Brown Corpus\n", "fdist = FreqDist(w.lower() for w in brown.words() if w.isalpha())\n", "# iterate over the samples\n", "for sample in fdist:\n", "    if fdist[sample] >=3:\n", "        wordSet.append(sample)            # add to the list the the frequency is greater than or equal to 3\n", "# print(wordSet)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**16. Write a program to generate a table of lexical diversity scores (i.e. token/type ratios), as we saw in 1.1. Include the full set of Brown Corpus genres (** `nltk.corpus.brown.categories()` **). Which genre has the lowest diversity (greatest number of tokens per type)? Is this what you would have expected?**"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["adventure 0.1279743878169075\n", "belles_lettres 0.10642071451679992\n", "editorial 0.16054152327770924\n", "fiction 0.1358194136199042\n", "government 0.11667641228232811\n", "hobbies 0.14493897625842492\n", "humor 0.23125144042406084\n", "learned 0.09268890745953554\n", "lore 0.13148804612915801\n", "mystery 0.12212912592488936\n", "news 0.14314696580941583\n", "religion 0.1617553745018909\n", "reviews 0.21192020440251572\n", "romance 0.12070492131044529\n", "science_fiction 0.22342778161713892\n"]}], "source": ["for category in brown.categories():\n", "    tokens = len(brown.words(categories=category))\n", "    types = len(set(brown.words(categories=category)))\n", "    diversity = types / tokens                               \n", "    # the computation of diversity in the second version of the book seems to be different to that in the first version\n", "    # I just use the second version's calculation formula\n", "    print(category, diversity)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The genre `learned`(Mosteller: *Probability with Statistical Applications*) has the lowest diversity."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**17. Write a function that finds the 50 most frequently occurring words of a text that are not stopwords.**"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["[('whale', 1226),\n", " ('one', 921),\n", " ('like', 647),\n", " ('upon', 566),\n", " ('man', 527),\n", " ('ship', 518),\n", " ('ahab', 511),\n", " ('ye', 472),\n", " ('sea', 455),\n", " ('old', 450),\n", " ('would', 432),\n", " ('though', 384),\n", " ('head', 345),\n", " ('yet', 345),\n", " ('boat', 336),\n", " ('time', 334),\n", " ('long', 333),\n", " ('captain', 329),\n", " ('still', 312),\n", " ('great', 306),\n", " ('said', 304),\n", " ('two', 298),\n", " ('must', 283),\n", " ('seemed', 283),\n", " ('white', 281),\n", " ('last', 277),\n", " ('see', 272),\n", " ('thou', 271),\n", " ('way', 271),\n", " ('whales', 268),\n", " ('stubb', 257),\n", " ('queequeg', 252),\n", " ('little', 249),\n", " ('round', 247),\n", " ('three', 245),\n", " ('say', 244),\n", " ('men', 244),\n", " ('sperm', 244),\n", " ('may', 240),\n", " ('first', 235),\n", " ('every', 232),\n", " ('well', 230),\n", " ('us', 228),\n", " ('much', 223),\n", " ('could', 216),\n", " ('good', 216),\n", " ('hand', 214),\n", " ('side', 208),\n", " ('ever', 206),\n", " ('never', 206)]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["def find_50_most_frequent_words(text):\n", "    fdist = FreqDist(w.lower() for w in text if w.isalpha() and w.lower() not in stopwords.words('english'))\n", "    return fdist.most_common(50)\n", "\n", "find_50_most_frequent_words(text1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**18. Write a program to print the 50 most frequent bigrams (pairs of adjacent words) of a text, omitting bigrams that contain stopwords.**"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["[(('Sperm', 'Whale'), 118),\n", " (('<PERSON><PERSON>', '<PERSON>'), 83),\n", " (('old', 'man'), 80),\n", " (('<PERSON>', 'Whale'), 74),\n", " (('I', 'say'), 71),\n", " (('Captain', 'Ah<PERSON>'), 61),\n", " (('sperm', 'whale'), 55),\n", " (('said', 'I'), 52),\n", " (('I', 'thought'), 44),\n", " (('I', 'know'), 43),\n", " (('Right', 'Whale'), 38),\n", " (('I', 'could'), 36),\n", " (('But', 'I'), 36),\n", " (('I', 'think'), 34),\n", " (('ye', 'see'), 34),\n", " (('thought', 'I'), 32),\n", " (('I', 'would'), 32),\n", " (('Captain', '<PERSON><PERSON><PERSON>'), 32),\n", " (('cried', 'Ahab'), 32),\n", " (('white', 'whale'), 31),\n", " (('I', 'must'), 29),\n", " (('one', 'hand'), 28),\n", " (('I', 'saw'), 27),\n", " (('I', 'see'), 24),\n", " (('I', 'shall'), 24),\n", " (('cried', 'Stubb'), 23),\n", " (('one', 'side'), 22),\n", " (('every', 'one'), 21),\n", " (('let', 'us'), 20),\n", " (('never', 'mind'), 20),\n", " (('I', 'suppose'), 20),\n", " (('OF', 'THE'), 18),\n", " (('years', 'ago'), 18),\n", " (('I', 'never'), 18),\n", " (('I', 'mean'), 18),\n", " (('<PERSON>', '<PERSON>'), 18),\n", " (('At', 'last'), 18),\n", " (('said', 'Stubb'), 18),\n", " (('I', 'tell'), 17),\n", " (('cried', '<PERSON>bu<PERSON>'), 17),\n", " (('something', 'like'), 16),\n", " (('<PERSON>', 'Horn'), 16),\n", " (('lower', 'jaw'), 16),\n", " (('Look', 'ye'), 16),\n", " (('well', 'known'), 15),\n", " (('But', 'though'), 15),\n", " (('I', 'guess'), 15),\n", " (('would', 'seem'), 15),\n", " (('ivory', 'leg'), 15),\n", " (('old', 'Ahab'), 15)]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["def find_50_most_frequent_bigrams(text):\n", "    bigram = list(nltk.bigrams(text))\n", "    fdist = FreqDist(b for b in bigram if b[0].isalpha() and b[1].isalpha()\n", "                                      and b[0] not in stopwords.words('english') \n", "                                      and b[1] not in stopwords.words('english')) \n", "    return fdist.most_common(50)\n", "\n", "find_50_most_frequent_bigrams(text1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**19. Write a program to create a table of word frequencies by genre, like the one given in 1 for modals. Choose your own words and try to find words whose presence (or absence) is typical of a genre. Discuss your findings.**"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                  love   like  peace   hate    war  fight battle \n", "      adventure      9    136      5      8     18     10      3 \n", " belles_lettres     68    169     29      4     84     13     22 \n", "      editorial     13     49     30      0     54     10      3 \n", "        fiction     16    147      3      9     24      7      6 \n", "     government      1     21     11      0      7      0      0 \n", "        hobbies      6     66      3      0     12      1      2 \n", "          humor      4     34      1      0      1      0      2 \n", "        learned     13     83      8      2     16      7      4 \n", "           lore     19     86     11      2     23     15     13 \n", "        mystery      7    136      0      2      2      4      1 \n", "           news      3     46      4      1     20     14     15 \n", "       religion     13     18     19      3     14      3      1 \n", "        reviews      7     36      2      2     17      5      3 \n", "        romance     32    185      7      9     11      7      3 \n", "science_fiction      3     25      0      0      2      1      0 \n"]}], "source": ["cfd = nltk.ConditionalFreqDist(\n", "          (genre, word)\n", "          for genre in brown.categories()\n", "          for word in brown.words(categories=genre))\n", "genres = brown.categories()\n", "my_words = ['love', 'like', 'peace', 'hate', 'war', 'fight', 'battle']\n", "cfd.tabulate(conditions=genres, samples=my_words)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**20. Write a function ** `word_freq()` ** that takes a word and the name of a section of the Brown Corpus as arguments, and computes the frequency of the word in that section of the corpus.**"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["def word_freq(section):\n", "    fdist = FreqDist(w.lower() for w in brown.words(categories=section))\n", "    return fdist\n", "\n", "# word_freq('news')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**21. Write a program to guess the number of syllables contained in a text, making use of the CMU Pronouncing Dictionary.**"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["739425"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["def number_of_syllables(text):\n", "    prondict = nltk.corpus.cmudict.dict()\n", "    # syllables = []                                       # an empty list for other functions\n", "    number = 0\n", "    for w in text:\n", "        if w.lower() in prondict.keys():                   # to avoid KeyError\n", "            number += len(prondict[w.lower()][0])          # though a word may have multiple prouns, we chose the first\n", "    return number\n", "\n", "# len(number_of_syllables(testText))\n", "number_of_syllables(text1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**22. Define a function ** `hedge(text)` ** which processes a text and produces a new version with the word ** 'like' ** between every third word.**"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Text: <PERSON><PERSON> like <PERSON> by like <PERSON> like 1851>"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["def hedge(text):\n", "    new_version = list(text)                                 # convert the type from nltk.Text to list\n", "                                                             # to take advantage of insert() method\n", "    for i in range(2, len(text) + len(text) // 3, 3):        # loop over every third word\n", "                                                             # remember to add the length \n", "        new_version.insert(i, 'like')                        # and this is a simple version that \n", "                                                             # regards punctuations as words\n", "    return nltk.Text(new_version)                            # restore to nltk.Text\n", "\n", "hedge(text1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**23. <PERSON><PERSON><PERSON>'s Law: Let ** *f(w)* ** be the frequency of a word ** *w* ** in free text. Suppose that all the words of a text are ranked according to their frequency, with the most frequent word first. <PERSON><PERSON><PERSON>'s law states that the frequency of a word type is inversely proportional to its rank (i.e. ** *f × r = k* **, for some constant ** *k* **). For example, the 50th most common word type should occur three times as frequently as the 150th most common word type.**  \n", "a. **Write a function to process a large text and plot word frequency against word rank using pylab.plot. Do you confirm <PERSON><PERSON><PERSON>'s law? (Hint: it helps to use a logarithmic scale). What is going on at the extreme ends of the plotted line?**  \n", "b. **Generate random text, e.g., using ** `random.choice(\"abcdefg \")` **, taking care to include the space character. You will need to ** `import random` ** first. Use the string concatenation operator to accumulate characters into a (very) long string. Then tokenize this string, and generate the Zipf plot as before, and compare the two plots. What do you make of Zipf's Law in the light of this?**"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def zipf_law(text):\n", "    fdist = FreqDist([w.lower() for w in text if w.isalpha()])\n", "    fdist = fdist.most_common()                                 # sort the frequency distribution\n", "                                                                # note that it converts the type from dict to list\n", "    rank = []\n", "    freq = []\n", "    n = 1                                                       # the variable records the rank\n", "    \n", "    for i in range(len(fdist)):\n", "        freq.append(fdist[i][1])                                # fdist[i][0] is the word \n", "                                                                # and fdist[i][1] is the corresponding frequency\n", "        rank.append(n)\n", "        n += 1\n", "    \n", "    # I use matplotlib.pyplot istead, since it seems that pylab is discouraged nowadays\n", "    plt.plot(rank, freq, 'bs')\n", "    plt.xscale('log')                                           # set the x axis to log scale\n", "    # the above two statements are equivalent to: plt.semilogx(rank, freq, 'bs')\n", "\n", "    plt.title(\"<PERSON><PERSON><PERSON>'s law\")\n", "    plt.xlabel('word rank')\n", "    plt.ylabel('word frequency')\n", "    plt.show()\n", "\n", "zipf_law(brown.words())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The frequency of 1st ranked word is approximately 2 times of the frequency of 2nd ranked word and 7 times of the frequency of 7st ranked word. (Well, the frequency of 3rd to 6th words is a bit high)  \n", "Generally the Zipf's law applies."]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"image/png": "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**********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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["randomText = ''\n", "for i in range(10000000):\n", "    randomText = randomText + random.choice(\"abcdefg \")\n", "randomWord = randomText.split()\n", "zipf_law(randomWord)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Since the text is generated randomly, the <PERSON><PERSON><PERSON>'s law does not apply. <PERSON><PERSON><PERSON>'s law is a empirical law based on human language."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**24. Modify the text generation program in 2.2 further, to do the following tasks:**  \n", "a. **Store the n most likely words in a list ** `words` ** then randomly choose a word from the list using**  `random.choice()`**. (You will need to ** `import random` ** first.)**  \n", "b. **Select a particular genre, such as a section of the Brown Corpus, or a genesis translation, one of the Gutenberg texts, or one of the Web texts. Train the model on this corpus and get it to generate random text. You may have to experiment with different start words. How intelligible is the text? Discuss the strengths and weaknesses of this method of generating random text.**  \n", "c. **Now train your system using two distinct genres and experiment with generating text in the hybrid genre. Discuss your observations.**"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["come then that men thou he there about cried might their old like would last almost thou through been So your one - sea would boat should In way said now what made such thought do sea us . might even him these : which no ( you your through though while ' and men must two yet but they of \" most The an For or Captain many with yet - can last most water us day after , deck , boat him seen more There ,\" again one my the their been boat can great might back those us . great little we said through <PERSON>ub<PERSON> ? while than long whales ' or still there among cried eyes her Captain by an would . when though ! little So thing many had did ?\" thing two It even back What must So so man almost we ?\" Ahab be of our - round !\" were he about now white ; which - after made one man little And it had , made with t not life a ( most own now than said own we round whale with into day are men could any Ahab t never away "]}], "source": ["def generate_random_text_on_n_most_likely_words(text, n):\n", "    fdist = FreqDist(text)\n", "    fdist = fdist.most_common(n)\n", "    for i in range(n):\n", "        print(random.choice(fdist)[0], end=' ')\n", "        \n", "generate_random_text_on_n_most_likely_words(text1, 200)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Jr. as or men as Mrs. new million we between an well school set or man Mr. He would also There A million her time today system only not president like , between get against against most get off new much only There U.S. Mrs. Sunday night back without Mr. under This good president people Dallas season being with per about during make said get when their its by out be about take take between off tax -- it before those being week jury both to all New been men time years , get 1 under <PERSON> before into while which he In but was They members our be who night over government program take took at school two will by those one year man both take They administration But some him meeting her members President do make could into no could without did which such way up other yesterday who Mantle The cent American yesterday by that it here good United before during Jr. Texas are Monday was , since we may members much The than no Washington we 1 would do with take what home all In -- York administration It New him years Kennedy there through now "]}], "source": ["generate_random_text_on_n_most_likely_words(brown.words(categories='news'), 200)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["people just say by came And one They for make home first going old before little if knew before then it still out on But You over you young make just or A them their four But just here never those . must should were too men it may make most who knew went put night knew say after were can who about man can come around looked this the little asked four It told I'm must in most day left might last ( other an there man didn't all might under must has like many In my '' could for John such me thought work some into next time way then took into which make home because down felt years against And before asked my how which over one It since might of put many who -- their my : the men being what went ! should new against American school . much are say some than didn't American people while those would They state might go which work A year away work too has went they over so ; an being felt ) In both never do all John he have home said four were state out where "]}], "source": ["generate_random_text_on_n_most_likely_words(brown.words(categories=['news','romance']), 200)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**25. Define a function ** `find_language()` ** that takes a string as its argument, and returns a list of languages that have that string as a word. Use the ** `udhr` ** corpus and limit your searches to files in the Latin-1 encoding.**"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"collapsed": true}, "outputs": [], "source": ["def find_language(s):\n", "    langs = []\n", "    for lang in udhr.fileids():\n", "        if lang.endswith('Latin1') and s in udhr.words(lang):\n", "            langs.append(lang)\n", "    return langs"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["['English-Latin1', 'NigerianPidginEnglish-Latin1']"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["find_language('world')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**26. What is the branching factor of the noun hypernym hierarchy? I.e. for every noun synset that has hyponyms — or children in the hypernym hierarchy — how many do they have on average? You can get all noun synsets using ** `wn.all_synsets('n')` **.**"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4.543820763194153\n"]}], "source": ["# noun_synsets = len(list(wn.all_synsets('n')))               # the number of noun synsets\n", "cnt = 0\n", "hypos = 0\n", "for synset in wn.all_synsets('n'):\n", "    if synset.hyponyms() != []:\n", "        hypos += len(synset.hyponyms())\n", "        cnt += 1\n", "print(hypos / cnt)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**27. The polysemy of a word is the number of senses it has. Using WordNet, we can determine that the noun ** *dog* ** has 7 senses with: ** `len(wn.synsets('dog', 'n'))` **. Compute the average polysemy of nouns, verbs, adjectives and adverbs according to WordNet.**"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["# Well, I tried to store the number of senses in dict() \n", "# but after many trials I still failed... or say, stuck in a nested for loop."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**28. Use one of the predefined similarity measures to score the similarity of each of the following pairs of words. Rank the pairs in order of decreasing similarity. How close is your ranking to the order given here, an order that was established experimentally by (<PERSON> & <PERSON>, 1998): car-automobile, gem-jewel, journey-voyage, boy-lad, coast-shore, asylum-madhouse, magician-wizard, midday-noon, furnace-stove, food-fruit, bird-cock, bird-crane, tool-implement, brother-monk, lad-brother, crane-implement, journey-car, monk-oracle, cemetery-woodland, food-rooster, coast-hill, forest-graveyard, shore-woodland, monk-slave, coast-forest, lad-wizard, chord-smile, glass-magician, rooster-voyage, noon-string.**"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["def similarities(w1, w2):\n", "    print('Path similarity:', w1.path_similarity(w2))\n", "    print('<PERSON><PERSON>-<PERSON><PERSON><PERSON> similarity:', w1.lch_similarity(w2))\n", "    print('<PERSON><PERSON><PERSON> similarity:', w1.wup_similarity(w2))"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["car = wn.synset('car.n.01')\n", "automobile = wn.synset('automobile.n.01')\n", "gem = wn.synset('gem.n.01')\n", "jewel = wn.synset('jewel.n.01')\n", "journey = wn.synset('journey.n.01')\n", "voyage = wn.synset('voyage.n.01')\n", "boy = wn.synset('boy.n.01')\n", "lad = wn.synset('lad.n.01')\n", "coast = wn.synset('coast.n.01')\n", "shore = wn.synset('shore.n.01')\n", "asylum = wn.synset('asylum.n.01')\n", "madhouse = wn.synset('madhouse.n.01')\n", "magician = wn.synset('magician.n.01')\n", "wizard = wn.synset('wizard.n.01')\n", "midday = wn.synset('midday.n.01')\n", "noon = wn.synset('noon.n.01')\n", "furnace = wn.synset('furnace.n.01')\n", "stove = wn.synset('stove.n.01')\n", "food = wn.synset('food.n.01')\n", "fruit = wn.synset('fruit.n.01')\n", "bird = wn.synset('bird.n.01')\n", "cock = wn.synset('cock.n.01')\n", "crane = wn.synset('crane.n.01')\n", "tool = wn.synset('tool.n.01')\n", "implement = wn.synset('implement.n.01')\n", "brother = wn.synset('brother.n.01')\n", "monk = wn.synset('monk.n.01')\n", "oracle = wn.synset('oracle.n.01')\n", "cemetery = wn.synset('cemetery.n.01')\n", "woodland = wn.synset('woodland.n.01')\n", "rooster = wn.synset('rooster.n.01')\n", "hill = wn.synset('hill.n.01')\n", "forest = wn.synset('forest.n.01')\n", "graveyard = wn.synset('graveyard.n.01')\n", "slave = wn.synset('slave.n.01')\n", "chord = wn.synset('chord.n.01')\n", "smile = wn.synset('smile.n.01')\n", "glass = wn.synset('glass.n.01')\n", "string = wn.synset('string.n.01')"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Path similarity: 0.3333333333333333\n", "Leacock-Chodorow similarity: 2.538973871058276\n", "Wu-Palmer similarity: 0.6666666666666666\n"]}], "source": ["similarities(boy, lad)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Well, it seems that I can't get access to Miller & Charles, 1998. And the computation of Leacock-Cho<PERSON><PERSON> similarity takes a long time. So here I don't compute the similarities of all the given pairs."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 2}