text = "This is a sample text. It's a simple example."

import re

def text_count(text):
    num_characters = len(text)
    words = re.findall(r'\b\w+\b', text)
    num_words = len(words) - 1
    num_characters1 = len([_ for _ in text if _.strip()])


    sentences = text.split('\n')
    num_sentences = len([s for s in sentences if s.strip()]) + 1
    average_word_length = num_characters1 / num_words

    print(f'Number of words: {num_words}')
    print(f'Number of characters: {num_characters}')
    print(f'Number of sentences: {num_sentences}')
    print(f'Average word length: {average_word_length}')

text_count(text)
