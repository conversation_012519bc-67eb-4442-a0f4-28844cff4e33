""""Question 3: (3 marks)
Write a function shorten(text, n) ** to process a text, omitting the ** n** most frequently
occurring words of the text.
Input:
text = 'Write a function shorten(text, n) to process a text, omitting the n most frequently
occurring words of the text. How readable is it?'
shorten (text, 4)
Output:
'Write function shorten to process omitting most frequently occurring words of How
readable is it' """

from collections import Counter
import re

def shorten(text, n):
    # Tokenize the text, preserving the case and ignoring punctuation
    words = re.findall(r'\b\w+\b', text)

    # Count the frequency of each word
    word_counts = Counter(words)

    # Identify the n most frequently occurring words
    most_common_words = set([word for word, count in word_counts.most_common(n)])

    # Reconstruct the text without the n most frequently occurring words
    filtered_words = [word for word in words if word not in most_common_words]

    # Join the remaining words into a single string
    shortened_text = ' '.join(filtered_words)

    return shortened_text

# Example usage
text = 'Write a function shorten(text, n) to process a text, omitting the n most frequently occurring words of the text. How readable is it?'
output = shorten(text, 4)
print(output)
