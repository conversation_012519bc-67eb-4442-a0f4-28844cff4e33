""""Question 1: (2 marks)
Define flowers to be the list of words ['camellia', 'pendulum', 'petunias', begonia', 'dahlia',
hostas', 'pelorism', 'paperwhite'). Now write code to perform the following tasks:
a. Print all words ending with lia
b. Print all words longer than eight characters """

# Import nltk
import nltk

# Define the list of flowers
flowers = ['camellia', 'pendulum', 'petunias', 'begonia', 'dahlia', 'hostas', 'pelorism', 'paperwhite']

# Task a: Print all words ending with 'lia'
words_ending_with_lia = [word for word in flowers if word.endswith('lia')]
print("Words ending with 'lia':", words_ending_with_lia)

# Task b: Print all words longer than eight characters
words_longer_than_eight = [word for word in flowers if len(word) > 8]
print("Words longer than eight characters:", words_longer_than_eight)
