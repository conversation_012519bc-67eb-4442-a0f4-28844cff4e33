""""Question 1: (2 marks)
Write regular expressions to match the following classes of strings:
1. An arithmetic expression using integers, addition, and multiplication, such as 5*6+9
2. A single determiner (assume that a, an, and the ** are the only determiners). """

#Q1-1
import re

# Define the regular expression pattern
pattern = r'^\d+(\s*[\+\*]\s*\d+)*$'
regex = re.compile(pattern)

# Read input from the keyboard
user_input = input("Enter an arithmetic expression: ")

# Check if the input matches the pattern
if regex.match(user_input):
    print("The input is a valid arithmetic expression.")
else:
    print("The input is NOT a valid arithmetic expression.")

#test: Enter an arithmetic expression: 5*6+9
#output: The input is a valid arithmetic expression.