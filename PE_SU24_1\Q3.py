# Cau 3
#Write a Python program that takes a document as input and outputs a dictionary where the keys are the unique bigrams (two-word pharses)
#in the document and the values are the number of times each bigram appears.



import nltk
from nltk import bigrams
from collections import defaultdict

def count_bigrams(document):
    words = [word for word in nltk.word_tokenize(document) if word != "``" and word != "''"]
    
    bigram_counts = defaultdict(int)

    for w1, w2 in bigrams(words):
        bigram = (w1, w2)
        print(bigram)
    for w1, w2 in bigrams(words):
        bigram = (w1, w2)
        bigram_counts[bigram] += 1 
    return dict(bigram_counts)


document = "This is a sample document. This is document contains several sentences. We can extract bigrams from it"

bigram_count_dict = count_bigrams(document.replace(".", "").replace(",", "").replace(":", ""))
print(bigram_count_dict)

