""""Question 3: (3 marks)
Write a function that takes a text and a vocabulary as its arguments and returns the set of
words that appear in the text but not in the vocabulary. Both arguments can be represented as lists
of strings.
Example:
Input:
text = 'a text and a vocabulary
vocab = 'a vocabulary'
Output:
['and', 'text'] """

def find_words_not_in_vocab(text, vocab):
    # Split the text string into individual words
    text_words = text.split()

    # Convert both to sets
    text_set = set(text_words)
    vocab_set = set(vocab)

    # Compute the set difference
    result_set = text_set - vocab_set

    # Return the result as a list
    return list(result_set)



text = "a text and a vocabulary"
vocab = ["a", "vocabulary"]

result = find_words_not_in_vocab(text, vocab)
print(result)  # Output: ['and', 'text']

