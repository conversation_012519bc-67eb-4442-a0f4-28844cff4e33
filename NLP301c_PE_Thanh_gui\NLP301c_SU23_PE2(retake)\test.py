#Cau 1 :
# def percent(word,text):
#     word1 = word.lower()
#     text1 = text.lower()
#     word2 = word1.split()
#     text2 = text1.split()
#     percent1 = (len(word2)/len(text2))*100
#     return percent1
#
# text = "I want to pass the PE test"
# word = "test"
# result = percent(word,text)
# print(result)

# cau 2:
# from nltk.tokenize import WordPunctTokenizer
# text = "Reset your password if you just can't remember your old one."
#
# print("Original string:")
# print(text)
# print("Split all punctuation into separate tokens:")
# print(WordPunctTokenizer().tokenize(text))

#Cau 3:

# from nltk.tokenize import word_tokenize
# from nltk.probability import FreqDist
# import matplotlib.pyplot as plt
#
# def words_less_than_four(text):
#     # Tokenize the text
#     tokens = word_tokenize(text)
#     # Filter words with less than 4 letters and remove punctuation
#     short_words = [word for word in tokens if len(word) < 4 and word.isalpha()]
#     # Calculate the frequency distribution
#     fdist = FreqDist(short_words)
#     # Sort words by frequency in decreasing order
#     sorted_words = sorted(fdist.items(), key=lambda item: item[1], reverse=True)
#     # Extract only the words from the sorted list of tuples
#     sorted_words_list = [word for word, freq in sorted_words]
#     return sorted_words_list, fdist
#
# text ='He would also attend the opening ceremony for the construction of the U.S. Embassy complex in Cau Giay District, as well as meeting students, teachers and scientists at the Hanoi University of Science and Technology'
# result, fdist = words_less_than_four(text)
# print(result)
#
# fdist.plot()
# plt.show()

# Cau 4:

