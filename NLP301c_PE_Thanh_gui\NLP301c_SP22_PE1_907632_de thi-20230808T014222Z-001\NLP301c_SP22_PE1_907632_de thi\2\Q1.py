""""Question 1: (2 marks)
Find all the four-letter words in the Chat Corpus (text5). With the help of a frequency
distribution (FreqDist), show these words in decreasing order of frequency."""


import nltk
from nltk.book import text5

# Download NLTK data if not already done
nltk.download('book')

# Extract four-letter words from text5
four_letter_words = [w for w in text5 if len(w) == 4]

# Create a frequency distribution
fdist = nltk.FreqDist(four_letter_words)

# Print the most common four-letter words
print(fdist.most_common())
