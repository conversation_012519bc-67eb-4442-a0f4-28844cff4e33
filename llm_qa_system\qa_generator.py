"""
Q&A pair generator for creating training/testing data from codebase
"""

import json
import logging
import random
from typing import List, Dict, Any, Tuple
from .ollama_client import OllamaClient
from .semantic_search import SemanticSearchEngine
from .codebase_indexer import CodeChunk
from config import QA_CONFIG

logger = logging.getLogger(__name__)


class QAGenerator:
    """Generates question-answer pairs from codebase content"""
    
    def __init__(self, ollama_client: OllamaClient, search_engine: SemanticSearchEngine):
        """
        Initialize QA generator
        
        Args:
            ollama_client: Initialized Ollama client
            search_engine: Initialized semantic search engine
        """
        self.ollama_client = ollama_client
        self.search_engine = search_engine
        self.max_context_length = QA_CONFIG["max_context_length"]
        self.temperature = QA_CONFIG["temperature"]
        self.num_qa_pairs = QA_CONFIG["num_qa_pairs"]
        
    def _create_question_generation_prompt(self, code_content: str, metadata: Dict[str, Any]) -> str:
        """Create prompt for generating questions about code"""
        prompt = f"""You are an expert code instructor creating educational questions about code.
Generate 3-5 diverse, meaningful questions about the following code snippet.

CODE INFORMATION:
File: {metadata.get('file_path', 'unknown')}
Type: {metadata.get('chunk_type', 'unknown')}
Language: {metadata.get('language', 'unknown')}
Lines: {metadata.get('start_line', 'unknown')}-{metadata.get('end_line', 'unknown')}

CODE:
{code_content}

Generate questions that cover:
1. What the code does (functionality)
2. How it works (implementation details)
3. Why certain approaches are used (design decisions)
4. Potential improvements or issues
5. Usage examples or context

Format your response as a JSON array of questions:
["Question 1?", "Question 2?", "Question 3?", ...]

Questions should be:
- Clear and specific
- Educational and meaningful
- Answerable from the provided code
- Varied in complexity (basic to advanced)

JSON Response:"""
        
        return prompt
        
    def _create_answer_generation_prompt(self, question: str, code_content: str, metadata: Dict[str, Any]) -> str:
        """Create prompt for generating answers to questions"""
        prompt = f"""You are an expert code instructor providing detailed answers about code.

QUESTION: {question}

CODE CONTEXT:
File: {metadata.get('file_path', 'unknown')}
Type: {metadata.get('chunk_type', 'unknown')}
Language: {metadata.get('language', 'unknown')}
Lines: {metadata.get('start_line', 'unknown')}-{metadata.get('end_line', 'unknown')}

CODE:
{code_content}

Provide a comprehensive, educational answer that:
1. Directly addresses the question
2. References specific parts of the code
3. Explains the reasoning behind the implementation
4. Includes relevant technical details
5. Is clear and well-structured

ANSWER:"""
        
        return prompt
        
    def generate_questions_for_chunk(self, code_content: str, metadata: Dict[str, Any]) -> List[str]:
        """Generate questions for a single code chunk"""
        try:
            prompt = self._create_question_generation_prompt(code_content, metadata)
            
            response = self.ollama_client.generate(
                prompt=prompt,
                temperature=self.temperature + 0.1,  # Slightly higher for creativity
                max_tokens=500
            )
            
            # Try to parse JSON response
            try:
                # Clean up response - remove any text before/after JSON
                response = response.strip()
                if response.startswith('```'):
                    # Remove code block markers
                    lines = response.split('\n')
                    response = '\n'.join(lines[1:-1])
                
                # Find JSON array
                start_idx = response.find('[')
                end_idx = response.rfind(']') + 1
                
                if start_idx != -1 and end_idx > start_idx:
                    json_str = response[start_idx:end_idx]
                    questions = json.loads(json_str)
                    
                    if isinstance(questions, list):
                        return [q.strip() for q in questions if q.strip()]
                        
            except json.JSONDecodeError:
                logger.warning("Failed to parse JSON response, extracting questions manually")
                
            # Fallback: extract questions manually
            questions = []
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if line and ('?' in line or line.lower().startswith(('what', 'how', 'why', 'when', 'where'))):
                    # Clean up the line
                    line = line.strip('"-.,[]{}()')
                    if line and not line.startswith(('JSON', 'Response', 'Questions')):
                        questions.append(line)
                        
            return questions[:5]  # Limit to 5 questions
            
        except Exception as e:
            logger.error(f"Failed to generate questions: {e}")
            return []
            
    def generate_answer_for_question(self, question: str, code_content: str, metadata: Dict[str, Any]) -> str:
        """Generate answer for a specific question"""
        try:
            prompt = self._create_answer_generation_prompt(question, code_content, metadata)
            
            answer = self.ollama_client.generate(
                prompt=prompt,
                temperature=self.temperature,
                max_tokens=self.max_context_length // 2
            )
            
            return answer.strip()
            
        except Exception as e:
            logger.error(f"Failed to generate answer: {e}")
            return f"Error generating answer: {str(e)}"
            
    def generate_qa_pairs_from_chunks(self, chunks: List[Dict[str, Any]], max_pairs: int = None) -> List[Dict[str, Any]]:
        """
        Generate Q&A pairs from code chunks
        
        Args:
            chunks: List of code chunks with content and metadata
            max_pairs: Maximum number of Q&A pairs to generate
            
        Returns:
            List of Q&A pairs with metadata
        """
        max_pairs = max_pairs or self.num_qa_pairs
        qa_pairs = []
        
        # Shuffle chunks for variety
        shuffled_chunks = random.sample(chunks, min(len(chunks), max_pairs * 2))
        
        logger.info(f"Generating Q&A pairs from {len(shuffled_chunks)} chunks")
        
        for i, chunk in enumerate(shuffled_chunks):
            if len(qa_pairs) >= max_pairs:
                break
                
            try:
                content = chunk.get('content', '')
                metadata = chunk.get('metadata', {})
                
                # Skip very short or very long chunks
                if len(content) < 100 or len(content) > self.max_context_length:
                    continue
                
                logger.debug(f"Processing chunk {i+1}/{len(shuffled_chunks)}")
                
                # Generate questions for this chunk
                questions = self.generate_questions_for_chunk(content, metadata)
                
                if not questions:
                    continue
                
                # Generate answers for each question
                for question in questions[:3]:  # Limit to 3 questions per chunk
                    if len(qa_pairs) >= max_pairs:
                        break
                        
                    answer = self.generate_answer_for_question(question, content, metadata)
                    
                    if answer and len(answer) > 50:  # Ensure meaningful answer
                        qa_pairs.append({
                            "question": question,
                            "answer": answer,
                            "source": {
                                "file_path": metadata.get('file_path', 'unknown'),
                                "chunk_type": metadata.get('chunk_type', 'unknown'),
                                "language": metadata.get('language', 'unknown'),
                                "start_line": metadata.get('start_line', 0),
                                "end_line": metadata.get('end_line', 0)
                            },
                            "code_snippet": content[:500] + "..." if len(content) > 500 else content
                        })
                        
            except Exception as e:
                logger.error(f"Failed to process chunk {i}: {e}")
                continue
                
        logger.info(f"Generated {len(qa_pairs)} Q&A pairs")
        return qa_pairs
        
    def generate_qa_pairs_from_search(self, topics: List[str], max_pairs: int = None) -> List[Dict[str, Any]]:
        """
        Generate Q&A pairs by searching for specific topics
        
        Args:
            topics: List of topics to search for
            max_pairs: Maximum number of Q&A pairs to generate
            
        Returns:
            List of Q&A pairs
        """
        max_pairs = max_pairs or self.num_qa_pairs
        qa_pairs = []
        
        for topic in topics:
            if len(qa_pairs) >= max_pairs:
                break
                
            try:
                # Search for relevant code
                search_results = self.search_engine.search(
                    query=topic,
                    max_results=5
                )
                
                if not search_results:
                    continue
                
                # Convert search results to chunks format
                chunks = []
                for result in search_results:
                    chunks.append({
                        'content': result['content'],
                        'metadata': result['metadata']
                    })
                
                # Generate Q&A pairs from these chunks
                topic_qa_pairs = self.generate_qa_pairs_from_chunks(
                    chunks, 
                    max_pairs=(max_pairs - len(qa_pairs))
                )
                
                qa_pairs.extend(topic_qa_pairs)
                
            except Exception as e:
                logger.error(f"Failed to generate Q&A for topic '{topic}': {e}")
                continue
                
        return qa_pairs
        
    def save_qa_pairs(self, qa_pairs: List[Dict[str, Any]], output_path: str):
        """Save Q&A pairs to JSON file"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(qa_pairs, f, indent=2, ensure_ascii=False)
                
            logger.info(f"Saved {len(qa_pairs)} Q&A pairs to {output_path}")
            
        except Exception as e:
            logger.error(f"Failed to save Q&A pairs: {e}")
            raise
            
    def load_qa_pairs(self, input_path: str) -> List[Dict[str, Any]]:
        """Load Q&A pairs from JSON file"""
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                qa_pairs = json.load(f)
                
            logger.info(f"Loaded {len(qa_pairs)} Q&A pairs from {input_path}")
            return qa_pairs
            
        except Exception as e:
            logger.error(f"Failed to load Q&A pairs: {e}")
            raise
            
    def generate_sample_qa_pairs(self) -> List[Dict[str, Any]]:
        """Generate sample Q&A pairs from the indexed codebase"""
        try:
            # Get collection stats to understand what's available
            stats = self.search_engine.get_collection_stats()
            
            if stats.get('total_chunks', 0) == 0:
                logger.warning("No chunks found in search index")
                return []
            
            # Define common programming topics to search for
            topics = [
                "function definition",
                "class implementation", 
                "import statements",
                "data processing",
                "file handling",
                "error handling",
                "algorithm implementation",
                "data structures",
                "text processing",
                "machine learning"
            ]
            
            # Filter topics based on available languages
            available_languages = stats.get('languages', [])
            if 'python' in available_languages:
                topics.extend(["python function", "python class", "pandas", "numpy"])
            
            # Generate Q&A pairs
            qa_pairs = self.generate_qa_pairs_from_search(topics, max_pairs=self.num_qa_pairs)
            
            return qa_pairs
            
        except Exception as e:
            logger.error(f"Failed to generate sample Q&A pairs: {e}")
            return []
