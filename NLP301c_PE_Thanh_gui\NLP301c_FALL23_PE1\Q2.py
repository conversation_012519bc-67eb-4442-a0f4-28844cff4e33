""""Question 2: (2 marks)
Write code that removes whitespace at the beginning and end of a string, and normalizes whitespace between words to be a single space character.
Input:
s=' this is. \n a sample \t. sentence. '
Output:
'this is a sample sentence.' """

import nltk
from nltk.tokenize import word_tokenize

# Ensure you have the necessary NLTK data files
nltk.download('punkt')

# Prompt the user to enter a sentence
s = input("Please enter a sentence: ")

# Step 1: Tokenize the string to split it into words and punctuation
tokens = word_tokenize(s)

# Step 2: Filter out unwanted punctuation and join tokens back into a single string with a single space between each word
words_only = [word for word in tokens if word.isalnum()]

# Step 3: Normalize the whitespace between words
normalized_string = ' '.join(words_only)

# Step 4: Add a period at the end of the sentence, if not already present
if not normalized_string.endswith('.'):
    normalized_string += '.'

print("Normalized string:", normalized_string)

#test
#input:' this is. \n a sample \t. sentence. '
#output: this is a sample sentence.

