#########______________________________________________#######
#########Question 1: (2 marks)
#########Define s as a text
#########Write expressions for finding all words in s that ending in "ize"
#Q1
# import nltk
#
# s = input("Enter your text: ")
# words = nltk.word_tokenize(s)
#
# ize_words = [word for word in words if word.endswith('ize')]
#
# print(ize_words)


#Q1_1. Write expressions for finding all words in s that starting in "ize"
# import nltk
# s = input("Enter the text: ")
# words = nltk.word_tokenize(s)
# ize_words = [word for word in words if word.startswith('ize')]
# print(ize_words)





###########____________________________##############
##########Question 2: (2 marks)
##########Write a function that finds the 50 most frequently occurring words of a text that are [NOT]
###########stopwords.
#Q2
# import nltk
# from nltk.corpus import stopwords
# from nltk import FreqDist
#
# # Ensure to download the 'stopwords' package if not already downloaded
# nltk.download('stopwords')
#
# def fifty_most_common(text):
#     # Tokenize the text
#     tokenized_words = nltk.word_tokenize(text)
#
#     # Filter out stopwords and punctuation
#     filtered_words = [word for word in tokenized_words if word not in stopwords.words('english') and word.isalpha()]
#
#     # Get the frequency distribution of the words
#     freqDist = FreqDist(filtered_words)
#
#     # Get the 50 most common words
#     most_common = freqDist.most_common(50)
#
#     return most_common
#
# s = input("Enter your text: ")
# print(fifty_most_common(s))



###########____________________________##############
##########Question 2: (2 marks)
##########Write a function that finds the 50 most frequently occurring words of a text that are []
###########stopwords.


# import nltk
# nltk.download('stopwords')
#
# from nltk.corpus import stopwords
# from collections import Counter
#
# def find_top_50_stopwords(text):
#     # Tokenize the text into words
#     words = nltk.word_tokenize(text.lower())
#
#     # Filter out stopwords
#     stop_words = set(stopwords.words('english'))
#     filtered_words = [word for word in words if word in stop_words]
#
#     # Count the occurrences of each word
#     word_counts = Counter(filtered_words)
#
#     # Return the 50 most common stopwords
#     return word_counts.most_common(50)
#
# text = input("Enter the text: ")
# result = find_top_50_stopwords(text)
# print(result)


###########___________________################
############Question 3: (3 marks)
##########Write a Python NLTK program to split all punctuation into separate tokens.
##########Sample Output:
##########Original string:
###########Reset your password if you just can't remember your old one.
##########Split all punctuation into separate tokens:
###########['Reset', 'your', 'password', 'if', 'you', 'just', 'can', """", 't', 'remember', 'your', 'old', 'one', '']

#Q3
# import nltk
# from nltk.tokenize import wordpunct_tokenize
#
# text = "your password if you just can't remember your old one."
# tokenized_text = wordpunct_tokenize(text)
#
# print(tokenized_text)
#

#########__________________________
#Question 4: (3 marks)
#Find the similarity between any two text documents
#Input:
#text1="John lives in Canada"
#text2="James lives in America, though he's not from there"
#Desired Output:
#Similarity between text1 and text2 is 0.792817083631068

#Q4
# from sklearn.feature_extraction.text import TfidfVectorizer
# from sklearn.metrics.pairwise import cosine_similarity
#
# text1 = "John lives in Canada"
# text2 = "James lives in America, though he's not from there"
#
# # Create the Document Term Matrix
# vectorizer = TfidfVectorizer()
# transformed_vector = vectorizer.fit_transform([text1, text2])
#
# similarity_score = cosine_similarity(transformed_vector[0:1], transformed_vector[1:2])
# print("Similarity between text1 and text2 is", similarity_score[0][0])


# # Import nltk and download wordnet
# import nltk
# nltk.download('wordnet')
#
# # Define two texts
# text1 = "John lives in Canada"
# text2 = "James lives in America, though he's not from there"
#
# # Tokenize and stem the texts
# tokenizer = nltk.tokenize.RegexpTokenizer(r'\w+')
# stemmer = nltk.stem.PorterStemmer()
# tokens1 = [stemmer.stem(token) for token in tokenizer.tokenize(text1)]
# tokens2 = [stemmer.stem(token) for token in tokenizer.tokenize(text2)]
#
# # Get the wordnet synsets for each token
# synsets1 = [nltk.corpus.wordnet.synsets(token)[0] for token in tokens1 if nltk.corpus.wordnet.synsets(token)]
# synsets2 = [nltk.corpus.wordnet.synsets(token)[0] for token in tokens2 if nltk.corpus.wordnet.synsets(token)]
#
# # Compute the average wup similarity score between the texts
# similarity = 0
# count = 0
# for synset1 in synsets1:
#     for synset2 in synsets2:
#         score = synset1.wup_similarity(synset2)
#         if score:
#             similarity += score
#             count += 1
# similarity /= count
#
# # Print the similarity score
# print(f"Similarity between text1 and text2 is {similarity:.3f}")

