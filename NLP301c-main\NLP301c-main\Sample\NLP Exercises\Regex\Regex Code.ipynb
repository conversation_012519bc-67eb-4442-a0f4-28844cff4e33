{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 30 REGULAR EXPRESSION EXERCISES + SOLUTIONS!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<PERSON><PERSON>, you never know when they might come in handy. It's one of the \"good programmer\"'s fundamental yet a few people actually masters them.\n", "\n", "Challenge yourself in 30 exercises, just hide the solution in order not to peek ;)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"trusted": true}, "outputs": [], "source": ["import re"]}, {"cell_type": "markdown", "metadata": {}, "source": ["1) Write a Python program to check that a string contains only a certain set of characters (in this case a-z, A-Z and 0-9)."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n", "False\n"]}], "source": ["# Solution\n", "def is_allowed_specific_char(string):\n", "    charRe = re.compile(r'[^a-zA-Z0-9.]')\n", "    string = charRe.search(string)\n", "    return not bool(string)\n", "\n", "print(is_allowed_specific_char(\"ABCDEFabcdef123450\")) \n", "print(is_allowed_specific_char(\"*&%@#!}{\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["2) Write a Python program that matches a string that has an a followed by zero or more b's"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found a match!\n", "Found a match!\n", "Found a match!\n"]}], "source": ["# Solution\n", "def text_match(text):\n", "        patterns = 'ab*?'\n", "        if re.search(patterns,  text):\n", "                return 'Found a match!'\n", "        else:\n", "                return('Not matched!')\n", "\n", "print(text_match(\"ac\"))\n", "print(text_match(\"abc\"))\n", "print(text_match(\"abbc\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["3) Write a Python program that matches a string that has an a followed by one or more b's"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found a match!\n", "Found a match!\n"]}], "source": ["# Solution\n", "def text_match(text):\n", "        patterns = 'ab+?'\n", "        if re.search(patterns,  text):\n", "                return 'Found a match!'\n", "        else:\n", "                return('Not matched!')\n", "\n", "print(text_match(\"ab\"))\n", "print(text_match(\"abc\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["4) Write a Python program that matches a string that has an a followed by zero or one 'b'"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found a match!\n", "Found a match!\n", "Found a match!\n", "Found a match!\n"]}], "source": ["# Solution\n", "def text_match(text):\n", "        patterns = 'ab?'\n", "        if re.search(patterns,  text):\n", "                return 'Found a match!'\n", "        else:\n", "                return('Not matched!')\n", "\n", "print(text_match(\"ab\"))\n", "print(text_match(\"abc\"))\n", "print(text_match(\"abbc\"))\n", "print(text_match(\"aabbc\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["5) Write a Python program that matches a string that has an a followed by three 'b'"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found a match!\n", "Found a match!\n"]}], "source": ["# Solution\n", "def text_match(text):\n", "        patterns = 'ab{3}?'\n", "        if re.search(patterns,  text):\n", "                return 'Found a match!'\n", "        else:\n", "                return('Not matched!')\n", "\n", "print(text_match(\"abbb\"))\n", "print(text_match(\"aabbbbbc\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["6) Write a Python program that matches a string that has an a followed by two to three 'b'."]}, {"cell_type": "code", "execution_count": 12, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Not matched!\n", "Found a match!\n"]}], "source": ["# Solution\n", "def text_match(text):\n", "        patterns = 'ab{2,3}?'\n", "        if re.search(patterns,  text):\n", "                return 'Found a match!'\n", "        else:\n", "                return('Not matched!')\n", "\n", "print(text_match(\"ab\"))\n", "print(text_match(\"aabbbbbc\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["7) Write a Python program to find sequences of lowercase letters joined with a underscore."]}, {"cell_type": "code", "execution_count": 14, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found a match!\n", "Not matched!\n", "Not matched!\n"]}], "source": ["# Solution\n", "def text_match(text):\n", "        patterns = '^[a-z]+_[a-z]+$'\n", "        if re.search(patterns,  text):\n", "                return 'Found a match!'\n", "        else:\n", "                return('Not matched!')\n", "\n", "print(text_match(\"aab_cbbbc\"))\n", "print(text_match(\"aab_Abbbc\"))\n", "print(text_match(\"Aaab_abbbc\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["8) Write a Python program to find the sequences of one upper case letter followed by lower case letters."]}, {"cell_type": "code", "execution_count": 16, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Not matched!\n", "Found a match!\n", "Found a match!\n"]}], "source": ["# Solution\n", "def text_match(text):\n", "        patterns = '^[a-z]+_[a-z]+$'\n", "        if not re.search(patterns,  text):\n", "                return 'Found a match!'\n", "        else:\n", "                return('Not matched!')\n", "\n", "print(text_match(\"aab_cbbbc\"))\n", "print(text_match(\"aab_Abbbc\"))\n", "print(text_match(\"Aaab_abbbc\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["9) Write a Python program that matches a string that has an 'a' followed by anything, ending in 'b'."]}, {"cell_type": "code", "execution_count": 18, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Not matched!\n", "Not matched!\n", "Found a match!\n"]}], "source": ["# Solution\n", "def text_match(text):\n", "        patterns = 'a.*?b$'\n", "        if re.search(patterns,  text):\n", "                return 'Found a match!'\n", "        else:\n", "                return('Not matched!')\n", "\n", "print(text_match(\"aabbbbd\"))\n", "print(text_match(\"aabAbbbc\"))\n", "print(text_match(\"accddbbjjjb\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["10) Write a Python program that matches a word at the beginning of a string."]}, {"cell_type": "code", "execution_count": 20, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found a match!\n", "Not matched!\n"]}], "source": ["# Solution\n", "def text_match(text):\n", "        patterns = '^\\w+'\n", "        if re.search(patterns,  text):\n", "                return 'Found a match!'\n", "        else:\n", "                return('Not matched!')\n", "\n", "print(text_match(\"The quick brown fox jumps over the lazy dog.\"))\n", "print(text_match(\" The quick brown fox jumps over the lazy dog.\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["11) Write a Python program that matches a word at the end of string, with optional punctuation."]}, {"cell_type": "code", "execution_count": 22, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found a match!\n", "Not matched!\n", "Not matched!\n"]}], "source": ["# Solution\n", "def text_match(text):\n", "        patterns = '\\w+\\S*$'\n", "        if re.search(patterns,  text):\n", "                return 'Found a match!'\n", "        else:\n", "                return('Not matched!')\n", "\n", "print(text_match(\"The quick brown fox jumps over the lazy dog.\"))\n", "print(text_match(\"The quick brown fox jumps over the lazy dog. \"))\n", "print(text_match(\"The quick brown fox jumps over the lazy dog \"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["12) Write a Python program that matches a word containing 'z'"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found a match!\n", "Not matched!\n"]}], "source": ["# Solution\n", "def text_match(text):\n", "        patterns = '\\w*z.\\w*'\n", "        if re.search(patterns,  text):\n", "                return 'Found a match!'\n", "        else:\n", "                return('Not matched!')\n", "\n", "print(text_match(\"The quick brown fox jumps over the lazy dog.\"))\n", "print(text_match(\"Python Exercises.\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["13) Write a Python program that matches a word containing 'z', not at the start or end of the word."]}, {"cell_type": "code", "execution_count": 26, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found a match!\n", "Not matched!\n"]}], "source": ["# Solution\n", "def text_match(text):\n", "        patterns = '\\Bz\\B'\n", "        if re.search(patterns,  text):\n", "                return 'Found a match!'\n", "        else:\n", "                return('Not matched!')\n", "\n", "print(text_match(\"The quick brown fox jumps over the lazy dog.\"))\n", "print(text_match(\"Python Exercises.\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["14) Write a Python program to match a string that contains only upper and lowercase letters, numbers, and underscores."]}, {"cell_type": "code", "execution_count": 28, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Not matched!\n", "Found a match!\n"]}], "source": ["# Solution\n", "def text_match(text):\n", "        patterns = '^[a-zA-Z0-9_]*$'\n", "        if re.search(patterns,  text):\n", "                return 'Found a match!'\n", "        else:\n", "                return('Not matched!')\n", "\n", "print(text_match(\"The quick brown fox jumps over the lazy dog.\"))\n", "print(text_match(\"Python_Exercises_1\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["15) Write a Python program where a string will start with a specific number. "]}, {"cell_type": "code", "execution_count": 30, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n", "False\n"]}], "source": ["# Solution\n", "def match_num(string):\n", "    text = re.compile(r\"^5\")\n", "    if text.match(string):\n", "        return True\n", "    else:\n", "        return False\n", "print(match_num('5-2345861'))\n", "print(match_num('6-2345861'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["16) Write a Python program to remove leading zeros from an IP address"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["************\n"]}], "source": ["# Solution\n", "ip = \"**************\"\n", "string = re.sub('\\.[0]*', '.', ip)\n", "print(string)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["17) Write a Python program to check for a number at the end of a string."]}, {"cell_type": "code", "execution_count": 34, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n", "True\n"]}], "source": ["# Solution\n", "def end_num(string):\n", "    text = re.compile(r\".*[0-9]$\")\n", "    if text.match(string):\n", "        return True\n", "    else:\n", "        return False\n", "\n", "print(end_num('abcdef'))\n", "print(end_num('abcdef6'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["18) Write a Python program to search the numbers (0-9) of length between 1 to 3 in a given string. "]}, {"cell_type": "code", "execution_count": 36, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of length 1 to 3\n", "1\n", "12\n", "13\n", "345\n"]}], "source": ["# Solution\n", "results = re.finditer(r\"([0-9]{1,3})\", \"Exercises number 1, 12, 13, and 345 are important\")\n", "print(\"Number of length 1 to 3\")\n", "for n in results:\n", "     print(n.group(0))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["19) Write a Python program to search some literals strings in a string. Go to the editor\n", "<PERSON>ple text : 'The quick brown fox jumps over the lazy dog.'\n", "Searched words : 'fox', 'dog', 'horse'"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 39, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Searching for \"fox\" in \"The quick brown fox jumps over the lazy dog.\" ->\n", "Matched!\n", "Searching for \"dog\" in \"The quick brown fox jumps over the lazy dog.\" ->\n", "Matched!\n", "Searching for \"horse\" in \"The quick brown fox jumps over the lazy dog.\" ->\n", "Not Matched!\n"]}], "source": ["# Solution\n", "patterns = [ 'fox', 'dog', 'horse' ]\n", "text = 'The quick brown fox jumps over the lazy dog.'\n", "for pattern in patterns:\n", "    print('Searching for \"%s\" in \"%s\" ->' % (pattern, text),)\n", "    if re.search(pattern,  text):\n", "        print('Matched!')\n", "    else:\n", "        print('Not Matched!')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["20) Write a Python program to search a literals string in a string and also find the location within the original string where the pattern occurs\n", "\n", "<PERSON>ple text : 'The quick brown fox jumps over the lazy dog.'\n", "Searched words : 'fox'"]}, {"cell_type": "code", "execution_count": 40, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 41, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found \"fox\" in \"The quick brown fox jumps over the lazy dog.\" from 16 to 19 \n"]}], "source": ["# Solution\n", "pattern = 'fox'\n", "text = 'The quick brown fox jumps over the lazy dog.'\n", "match = re.search(pattern, text)\n", "s = match.start()\n", "e = match.end()\n", "print('Found \"%s\" in \"%s\" from %d to %d ' % (match.re.pattern, match.string, s, e))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["21) Write a Python program to find the substrings within a string.\n", "\n", "Sample text :\n", "\n", "'Python exercises, PHP exercises, C# exercises'\n", "\n", "Pattern :\n", "\n", "'exercises'\n", "\n", "Note: There are two instances of exercises in the input string."]}, {"cell_type": "code", "execution_count": 42, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 43, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found \"exercises\"\n", "Found \"exercises\"\n", "Found \"exercises\"\n"]}], "source": ["# Solution\n", "text = 'Python exercises, PHP exercises, C# exercises'\n", "pattern = 'exercises'\n", "for match in re.findall(pattern, text):\n", "    print('Found \"%s\"' % match)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["22) Write a Python program to find the occurrence and position of the substrings within a string."]}, {"cell_type": "code", "execution_count": 44, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 45, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found \"exercises\" at 7:16\n", "Found \"exercises\" at 22:31\n", "Found \"exercises\" at 36:45\n"]}], "source": ["# Solution\n", "text = 'Python exercises, PHP exercises, C# exercises'\n", "pattern = 'exercises'\n", "for match in re.finditer(pattern, text):\n", "    s = match.start()\n", "    e = match.end()\n", "    print('Found \"%s\" at %d:%d' % (text[s:e], s, e))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["23) Write a Python program to replace whitespaces with an underscore and vice versa."]}, {"cell_type": "code", "execution_count": 46, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 47, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Python_Exercises\n", "Python Exercises\n"]}], "source": ["# Solution\n", "text = 'Python Exercises'\n", "text =text.replace (\" \", \"_\")\n", "print(text)\n", "text =text.replace (\"_\", \" \")\n", "print(text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["24) Write a Python program to extract year, month and date from a an url. "]}, {"cell_type": "code", "execution_count": 48, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 49, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[('2016', '09', '02')]\n"]}], "source": ["# Solution\n", "def extract_date(url):\n", "        return re.findall(r'/(\\d{4})/(\\d{1,2})/(\\d{1,2})/', url)\n", "url1= \"https://www.washingtonpost.com/news/football-insider/wp/2016/09/02/odell-beckhams-fame-rests-on-one-stupid-little-ball-josh-norman-tells-author/\"\n", "print(extract_date(url1))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["25) Write a Python program to convert a date of yyyy-mm-dd format to dd-mm-yyyy format."]}, {"cell_type": "code", "execution_count": 50, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 51, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original date in YYY-MM-DD Format:  2026-01-02\n", "New date in DD-MM-YYYY Format:  02-01-2026\n"]}], "source": ["# Solution\n", "def change_date_format(dt):\n", "        return re.sub(r'(\\d{4})-(\\d{1,2})-(\\d{1,2})', '\\\\3-\\\\2-\\\\1', dt)\n", "dt1 = \"2026-01-02\"\n", "print(\"Original date in YYY-MM-DD Format: \",dt1)\n", "print(\"New date in DD-MM-YYYY Format: \",change_date_format(dt1))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["26) Write a Python program to match if two words from a list of words starting with letter 'P'."]}, {"cell_type": "code", "execution_count": 52, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 53, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original date in YYY-MM-DD Format:  2026-01-02\n", "New date in DD-MM-YYYY Format:  02-01-2026\n"]}], "source": ["# Solution\n", "def change_date_format(dt):\n", "        return re.sub(r'(\\d{4})-(\\d{1,2})-(\\d{1,2})', '\\\\3-\\\\2-\\\\1', dt)\n", "dt1 = \"2026-01-02\"\n", "print(\"Original date in YYY-MM-DD Format: \",dt1)\n", "print(\"New date in DD-MM-YYYY Format: \",change_date_format(dt1))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["27) Write a Python program to separate and print the numbers of a given string."]}, {"cell_type": "code", "execution_count": 54, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 55, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "10\n", "20\n", "30\n"]}], "source": ["# Solution\n", "# Sample string.\n", "text = \"Ten 10, Twenty 20, Thirty 30\"\n", "result = re.split(\"\\D+\", text)\n", "# Print results.\n", "for element in result:\n", "    print(element)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["28) Write a Python program to find all words starting with 'a' or 'e' in a given string."]}, {"cell_type": "code", "execution_count": 56, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 57, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['example', 'eates', 'an', 'ayList', 'apacity', 'elements', 'elements', 'are', 'en', 'added', 'ayList', 'and', 'ayList', 'ed', 'accordingly']\n"]}], "source": ["# Solution\n", "# Input.\n", "text = \"The following example creates an ArrayList with a capacity of 50 elements. Four elements are then added to the ArrayList and the ArrayList is trimmed accordingly.\"\n", "#find all the words starting with 'a' or 'e'\n", "list = re.findall(\"[ae]\\w+\", text)\n", "# Print result.\n", "print(list)\n", "\t"]}, {"cell_type": "markdown", "metadata": {}, "source": ["29) Write a Python program to separate and print the numbers and their position of a given string."]}, {"cell_type": "code", "execution_count": 58, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 59, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["50\n", "Index position: 62\n"]}], "source": ["# Solution\n", "# Input.\n", "text = \"The following example creates an ArrayList with a capacity of 50 elements. Four elements are then added to the ArrayList and the ArrayList is trimmed accordingly.\"\n", "\n", "for m in re.finditer(\"\\d+\", text):\n", "    print(m.group(0))\n", "    print(\"Index position:\", m.start())\n", "\t"]}, {"cell_type": "markdown", "metadata": {}, "source": ["30) Write a Python program to abbreviate 'Road' as 'Rd.' in a given string."]}, {"cell_type": "code", "execution_count": 60, "metadata": {"trusted": true}, "outputs": [], "source": ["# Your code here"]}, {"cell_type": "code", "execution_count": 61, "metadata": {"trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["21 Ramkrishna Rd.\n"]}], "source": ["# Solution\n", "street = '21 Ramkrishna Road'\n", "print(re.sub('Road$', 'Rd.', street))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 4}