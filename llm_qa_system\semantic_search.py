"""
Semantic search engine using ChromaDB for vector storage and retrieval
"""

import logging
import uuid
from typing import List, Dict, Any, Optional, Tuple
import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer
from config import SEARCH_CONFIG
from .codebase_indexer import CodeChunk

logger = logging.getLogger(__name__)


class SemanticSearchEngine:
    """Semantic search engine using ChromaDB for codebase content"""
    
    def __init__(self, db_path: str = None, collection_name: str = None):
        """
        Initialize semantic search engine
        
        Args:
            db_path: Path to ChromaDB storage (default from config)
            collection_name: Name of the collection (default from config)
        """
        self.db_path = db_path or SEARCH_CONFIG["chroma_db_path"]
        self.collection_name = collection_name or SEARCH_CONFIG["collection_name"]
        self.embedding_model_name = SEARCH_CONFIG["embedding_model"]
        self.max_results = SEARCH_CONFIG["max_results"]
        self.similarity_threshold = SEARCH_CONFIG["similarity_threshold"]
        
        # Initialize ChromaDB client
        self.client = chromadb.PersistentClient(
            path=self.db_path,
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        # Initialize embedding model
        logger.info(f"Loading embedding model: {self.embedding_model_name}")
        self.embedding_model = SentenceTransformer(self.embedding_model_name)
        
        # Get or create collection
        self.collection = self._get_or_create_collection()
        
    def _get_or_create_collection(self):
        """Get existing collection or create new one"""
        try:
            # Try to get existing collection
            collection = self.client.get_collection(name=self.collection_name)
            logger.info(f"Using existing collection: {self.collection_name}")
            return collection
        except Exception:
            # Create new collection
            logger.info(f"Creating new collection: {self.collection_name}")
            return self.client.create_collection(
                name=self.collection_name,
                metadata={"hnsw:space": SEARCH_CONFIG["distance_metric"]}
            )
    
    def _prepare_chunk_metadata(self, chunk: CodeChunk) -> Dict[str, Any]:
        """Prepare metadata for ChromaDB storage"""
        metadata = {
            "file_path": chunk.file_path,
            "start_line": chunk.start_line,
            "end_line": chunk.end_line,
            "chunk_type": chunk.chunk_type,
            "language": chunk.language,
            "size": chunk.size
        }
        
        # Add custom metadata if available
        if chunk.metadata:
            for key, value in chunk.metadata.items():
                # ChromaDB requires string values for metadata
                metadata[f"custom_{key}"] = str(value)
                
        return metadata
    
    def add_chunks(self, chunks: List[CodeChunk], batch_size: int = 100):
        """
        Add code chunks to the search index
        
        Args:
            chunks: List of CodeChunk objects to index
            batch_size: Number of chunks to process in each batch
        """
        logger.info(f"Adding {len(chunks)} chunks to search index")
        
        # Process chunks in batches
        for i in range(0, len(chunks), batch_size):
            batch = chunks[i:i + batch_size]
            
            # Prepare data for ChromaDB
            documents = []
            metadatas = []
            ids = []
            
            for chunk in batch:
                # Create unique ID for each chunk
                chunk_id = str(uuid.uuid4())
                
                # Prepare document text (combine content with metadata for better search)
                doc_text = f"File: {chunk.file_path}\n"
                doc_text += f"Type: {chunk.chunk_type}\n"
                doc_text += f"Language: {chunk.language}\n"
                doc_text += f"Lines {chunk.start_line}-{chunk.end_line}:\n"
                doc_text += chunk.content
                
                documents.append(doc_text)
                metadatas.append(self._prepare_chunk_metadata(chunk))
                ids.append(chunk_id)
            
            # Add batch to collection
            try:
                self.collection.add(
                    documents=documents,
                    metadatas=metadatas,
                    ids=ids
                )
                logger.debug(f"Added batch {i//batch_size + 1}/{(len(chunks)-1)//batch_size + 1}")
                
            except Exception as e:
                logger.error(f"Failed to add batch starting at index {i}: {e}")
                raise
        
        logger.info(f"Successfully added {len(chunks)} chunks to search index")
    
    def search(self, query: str, max_results: int = None, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Search for relevant code chunks
        
        Args:
            query: Search query
            max_results: Maximum number of results (default from config)
            filters: Optional metadata filters
            
        Returns:
            List of search results with content and metadata
        """
        max_results = max_results or self.max_results
        
        try:
            # Perform semantic search
            results = self.collection.query(
                query_texts=[query],
                n_results=max_results,
                where=filters
            )
            
            # Process results
            search_results = []
            
            if results['documents'] and results['documents'][0]:
                for i, (doc, metadata, distance) in enumerate(zip(
                    results['documents'][0],
                    results['metadatas'][0],
                    results['distances'][0]
                )):
                    # Calculate similarity score (1 - distance for cosine)
                    similarity = 1 - distance
                    
                    # Filter by similarity threshold
                    if similarity >= self.similarity_threshold:
                        # Extract original content (remove metadata prefix)
                        content_start = doc.find("Lines")
                        if content_start != -1:
                            content_start = doc.find(":\n", content_start) + 2
                            content = doc[content_start:] if content_start > 1 else doc
                        else:
                            content = doc
                        
                        search_results.append({
                            "content": content,
                            "metadata": metadata,
                            "similarity": similarity,
                            "rank": i + 1
                        })
            
            logger.info(f"Found {len(search_results)} relevant results for query: '{query[:50]}...'")
            return search_results
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            raise
    
    def search_by_file_type(self, query: str, file_types: List[str], max_results: int = None) -> List[Dict[str, Any]]:
        """Search within specific file types"""
        filters = {"language": {"$in": file_types}}
        return self.search(query, max_results, filters)
    
    def search_by_chunk_type(self, query: str, chunk_types: List[str], max_results: int = None) -> List[Dict[str, Any]]:
        """Search within specific chunk types (function, class, etc.)"""
        filters = {"chunk_type": {"$in": chunk_types}}
        return self.search(query, max_results, filters)
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the indexed collection"""
        try:
            count = self.collection.count()
            
            # Get sample of metadata to analyze
            sample_results = self.collection.get(limit=min(100, count))
            
            languages = set()
            chunk_types = set()
            files = set()
            
            if sample_results['metadatas']:
                for metadata in sample_results['metadatas']:
                    languages.add(metadata.get('language', 'unknown'))
                    chunk_types.add(metadata.get('chunk_type', 'unknown'))
                    files.add(metadata.get('file_path', 'unknown'))
            
            return {
                "total_chunks": count,
                "languages": list(languages),
                "chunk_types": list(chunk_types),
                "sample_files": list(files)[:10],  # Show first 10 files
                "total_files": len(files)
            }
            
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {"error": str(e)}
    
    def clear_collection(self):
        """Clear all data from the collection"""
        try:
            self.client.delete_collection(name=self.collection_name)
            self.collection = self._get_or_create_collection()
            logger.info("Collection cleared successfully")
        except Exception as e:
            logger.error(f"Failed to clear collection: {e}")
            raise
    
    def update_chunk(self, chunk_id: str, chunk: CodeChunk):
        """Update a specific chunk in the index"""
        try:
            # Prepare updated data
            doc_text = f"File: {chunk.file_path}\n"
            doc_text += f"Type: {chunk.chunk_type}\n"
            doc_text += f"Language: {chunk.language}\n"
            doc_text += f"Lines {chunk.start_line}-{chunk.end_line}:\n"
            doc_text += chunk.content
            
            self.collection.update(
                ids=[chunk_id],
                documents=[doc_text],
                metadatas=[self._prepare_chunk_metadata(chunk)]
            )
            
            logger.info(f"Updated chunk {chunk_id}")
            
        except Exception as e:
            logger.error(f"Failed to update chunk {chunk_id}: {e}")
            raise
    
    def delete_chunks_by_file(self, file_path: str):
        """Delete all chunks from a specific file"""
        try:
            # Find chunks from the file
            results = self.collection.get(
                where={"file_path": file_path}
            )
            
            if results['ids']:
                self.collection.delete(ids=results['ids'])
                logger.info(f"Deleted {len(results['ids'])} chunks from {file_path}")
            else:
                logger.info(f"No chunks found for file {file_path}")
                
        except Exception as e:
            logger.error(f"Failed to delete chunks from {file_path}: {e}")
            raise
