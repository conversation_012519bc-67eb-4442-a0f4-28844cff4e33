{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": true}, "outputs": [], "source": ["import nltk\n", "import re\n", "import pprint\n", "import random\n", "from urllib import request\n", "from nltk import word_tokenize\n", "from nltk.corpus import brown\n", "from nltk.corpus import wordnet as wn"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**1. Define a string ** `s = 'colorless'` **. Write a Python statement that changes this to \"colourless\" using only the slice and concatenation operations.**"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"collapsed": true}, "outputs": [], "source": ["s = 'colorless'\n", "s = s[:4] + 'u' + s[4:]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**2. We can use the slice notation to remove morphological endings on words. For example, ** `'dogs'[:-1]` ** removes the last character of ** `dogs` **, leaving ** `dog` **. Use slice notation to remove the affixes from these words (we've inserted a hyphen to indicate the affix boundary, but omit this from your strings): ** `dish-es` **, ** `run-ning` **, ** `nation-ality` **, ** `un-do` **, ** `pre-heat` **.**"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": true}, "outputs": [], "source": ["dish = 'dishes'[:-2]\n", "run = 'running'[:-4]\n", "nation = 'nationality'[:-5]\n", "do = 'undo'[2:]\n", "heat = 'preheat'[3:]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**3. We saw how we can generate an ** `IndexError` ** by indexing beyond the end of a string. Is it possible to construct an index that goes too far to the left, before the start of the string?**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Yes, that is possible. Given a string `s`, `s[-(len(s)+1)]` will generate an `IndexError` since it goes too far to the left."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**4. We can specify a \"step\" size for the slice. The following returns every second character within the slice: ** `monty[6:11:2]` **. It also works in the reverse direction: ** `monty[10:5:-2]` ** Try these for yourself, then experiment with different step values.**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Omitted."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**5. What happens if you ask the interpreter to evaluate ** `monty[::-1]` **? Explain why this is a reasonable result.**"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["'nohtyP ytnoM'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["monty = 'Monty Python'\n", "monty[::-1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Reverse the string. `monty[:]` is the string itself, and `:-1` takes the reverse order."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**6. Describe the class of strings matched by the following regular expressions.**  \n", "a. `[a-zA-Z]+`  \n", "b. `[A-Z][a-z]*`  \n", "c. `p[a<PERSON><PERSON>]{,2}t`  \n", "d. `\\d+(\\.\\d+)?`  \n", "e. `([^aeiou][aeiou][^aeiou])*`  \n", "f. `\\w+|[^\\w\\s]+`  \n", "**Test your answers using ** `nltk.re_show()`."]}, {"cell_type": "markdown", "metadata": {}, "source": ["a. Normal words(with one or more letters in either upper or lower case)  \n", "b. Titled words(first letter is upper case)  \n", "c. Words starting with `p`, ending with `t`, and with 0 to 2 vowel(s) between. E.g., `pt`, `pet`, `poet`, etc.  \n", "d. Real numbers(integers and fractions)  \n", "e. [Consonant-Vowel-Consonant] with zero or more times  \n", "f. Alphanumeric character(s) or non-whitespace character(s), can be used for tokenizing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**7. Write regular expressions to match the following classes of strings:**  \n", "a. **A single determiner (assume that ** `a`**, **`an`**, and ** `the` ** are the only determiners).**  \n", "b. **An arithmetic expression using integers, addition, and multiplication, such as ** `2*3+8`."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": true}, "outputs": [], "source": ["re_a = r'(\\ban?\\b|\\bthe\\b)'\n", "re_b = r'[\\d\\*\\+]+'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**8. Write a utility function that takes a URL as its argument, and returns the contents of the URL, with all HTML markup removed. Use ** `from urllib import request` ** and then ** ` request.urlopen('http://nltk.org/').read().decode('utf8')` ** to access the contents of the URL.**"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from bs4 import BeautifulSoup\n", "def content_of_URL(URL):\n", "    html = request.urlopen(URL).read().decode('utf8')\n", "    raw = BeautifulSoup(html).get_text()\n", "    tokens = word_tokenize(raw)\n", "    return tokens\n", "\n", "# well, I haven't installed BeautifulSoup so I skip running this block"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**9. Save some text into a file ** `corpus.txt` **. Define a function ** `load(f)` ** that reads from the file named in its sole argument, and returns a string containing the text of the file.**  \n", "a. **Use ** `nltk.regexp_tokenize()` ** to create a tokenizer that tokenizes the various kinds of punctuation in this text. Use one multi-line regular expression, with inline comments, using the verbose flag (?x).**  \n", "b. **Use ** `nltk.regexp_tokenize()` ** to create a tokenizer that tokenizes the following kinds of expression: monetary amounts; dates; names of people and organizations.**"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["['.',\n", " ',',\n", " ',',\n", " ',',\n", " ',',\n", " ',',\n", " ',',\n", " ',',\n", " ',',\n", " '.',\n", " ',',\n", " ',',\n", " ',',\n", " ',',\n", " ',',\n", " ',',\n", " ',',\n", " '.',\n", " ',',\n", " ',',\n", " '.',\n", " ',',\n", " ',',\n", " ',',\n", " '.',\n", " '“',\n", " ',',\n", " ',',\n", " ',',\n", " '“',\n", " '.',\n", " '.',\n", " ',',\n", " ',',\n", " ',',\n", " ',',\n", " ',',\n", " '.',\n", " '.',\n", " '(',\n", " ':',\n", " '.',\n", " '.',\n", " ')',\n", " ',',\n", " '.',\n", " '.',\n", " '.',\n", " '?',\n", " '.',\n", " '.',\n", " '.']"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["def load_punctuations(f):\n", "    file = open(f, encoding='UTF-8')\n", "    raw = file.read()\n", "    pattern = r'''(?x)        # set flag to allow verbose regexps\n", "        [,\\.]                 # comma, period\n", "      | [\\[\\](){}<>]          # brackets () {} [] <>\n", "      | ['\"“]                 # quotation marks\n", "      | [?!]                  # question mark and exclamation mark\n", "      | [:;]                  # colon and semicolon\n", "      | \\.\\.\\.                # ellipsis\n", "      | [，。？！、‘：；]       # some Chinese punctuations\n", "    '''\n", "    return nltk.regexp_tokenize(raw, pattern)\n", "\n", "load_punctuations('corpus.txt')"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["['$1,000', '£999.99', '￥1000']"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["def load_monetary(f):\n", "    file = open(f, encoding='UTF-8')\n", "    raw = file.read()\n", "    pattern = r'''(?x)\n", "        \\$\\d+(?:,\\d+)*(?:\\.\\d+)?      # USD\n", "      | £\\d+(?:,\\d+)*(?:\\.\\d+)?       # GBP\n", "      | ￥\\d+(?:\\.\\d+)?               # CNY\n", "    '''\n", "    return nltk.regexp_tokenize(raw, pattern)\n", "\n", "load_monetary('corpus.txt')"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["['2018-08-06', '2018.08.06', '08/06/20', '06/08/20', '06/08/18', '06-08-20']"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["def load_date(f):\n", "    file = open(f, encoding='UTF-8')\n", "    raw = file.read()\n", "    pattern = r'''(?x)\n", "        \\d{,4}[/\\.-]\\d{1,2}[/\\.-]\\d{1,2}       # big-endian, e.g., 1996-10-23, 1996.10.23, 1996/10/23\n", "      | \\d{1,2}[/\\.-]\\d{1,2}[/\\.-]\\d{,4}       # little-endian or middle-endian, dd/mm/yyyy or mm/dd/yyyy \n", "    '''\n", "    # There are dates with month spelled out in full or in abbreviation as well.\n", "    # But the pattern expression can be extremly tedious so I just leave them out.\n", "    \n", "    return nltk.regexp_tokenize(raw, pattern)\n", "load_date('corpus.txt')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**10. Rewrite the following loop as a list comprehension:**\n", "\n", "```<PERSON>\n", ">>> sent = ['The', 'dog', 'gave', '<PERSON>', 'the', 'newspaper']\n", ">>> result = []\n", ">>> for word in sent:\n", "...     word_len = (word, len(word))\n", "...     result.append(word_len)\n", ">>> result\n", "[('The', 3), ('dog', 3), ('gave', 4), ('<PERSON>', 4), ('the', 3), ('newspaper', 9)]\n", "```"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["[('The', 3),\n", " ('dog', 3),\n", " ('gave', 4),\n", " ('<PERSON>', 4),\n", " ('the', 3),\n", " ('newspaper', 9)]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["sent = ['The', 'dog', 'gave', '<PERSON>', 'the', 'newspaper']\n", "result = [(word, len(word)) for word in sent]\n", "result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**11. Define a string ** `raw` ** containing a sentence of your own choosing. Now, split ** `raw` ** on some character other than space, such as ** `'s'`."]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["['Define a ', 'tring  raw containing a ', 'entence of your own choo', 'ing.']"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["raw = 'Define a string  raw containing a sentence of your own choosing.'\n", "raw.split('s')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**12. Write a ** `for` ** loop to print out the characters of a string, one per line.**"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["H\n", "e\n", "l\n", "l\n", "o\n", " \n", "w\n", "o\n", "r\n", "l\n", "d\n"]}], "source": ["s = 'Hello world'\n", "for char in s:\n", "    print(char)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**13. What is the difference between calling split on a string with no argument or with ** `' '` ** as the argument, e.g. ** `sent.split()` ** versus ** `sent.split(' ')` **? What happens when the string being split contains tab characters, consecutive space characters, or a sequence of tabs and spaces? (In IDLE you will need to use ** `'\\t'` ** to enter a tab character.)**"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Hello\\t', 'World\\nNLTK']\n", "['Hello', 'World', 'NLTK']\n"]}], "source": ["sent = 'Hello\\t World\\nNLTK'\n", "print(sent.split(' '))\n", "print(sent.split())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`sent.split(' ')` will not split other blank characters like `\\t`, `\\n`."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**14. Create a variable ** `words` ** containing a list of words. Experiment with ** `words.sort()` ** and ** `sorted(words)` **. What is the difference?**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`words.sort()` modify the original variable `words`, and it will not output in default.  \n", "`sorted(words)` return a sorted list without changing the original list."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**15. Explore the difference between strings and integers by typing the following at a Python prompt: ** `\"3\" * 7` ** and ** `3 * 7` **. Try converting between strings and integers using ** `int(\"3\")` ** and ** `str(3)` **.**"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3333333\n", "21\n", "<class 'int'>\n", "<class 'str'>\n"]}], "source": ["print(\"3\" * 7)\n", "print(3 * 7)\n", "print(type(int(\"3\")))\n", "print(type(str(3)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**16. Use a text editor to create a file called ** `prog.py` ** containing the single line ** `monty = 'Monty Python'` **. Next, start up a new session with the Python interpreter, and enter the expression ** `monty` ** at the prompt. You will get an error from the interpreter. Now, try the following (note that you have to leave off the ** `.py` ** part of the filename):**\n", "```<PERSON>\n", "from prog import monty\n", "monty\n", "```\n", "**This time, Python should return with a value. You can also try ** `import prog` **, in which case Python should be able to evaluate the expression ** `prog.monty` ** at the prompt.**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Omitted."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**17. What happens when the formatting strings ** `%6s` ** and ** `%-6s` ** are used to display strings that are longer than six characters?**"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["helloworld\n", "helloworld\n"]}], "source": ["s = 'helloworld'\n", "print('%6s' %s)\n", "print('%-6s' %s)\n", "# There seems no difference."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**18. Read in some text from a corpus, tokenize it, and print the list of all ** *wh-* **word types that occur. (** *wh-* **words in English are used in questions, relative clauses and exclamations: ** *who*, *which*, *what* **, and so on.) Print them in order. Are any words duplicated in this list, because of the presence of case distinctions or punctuation?**"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['What', 'Why', 'who']\n"]}], "source": ["f = 'corpus.txt'\n", "file = open(f, encoding='UTF-8')\n", "raw = file.read()\n", "tokens = word_tokenize(raw)\n", "print([wh for wh in tokens if wh.lower().startswith('wh')])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**19. Create a file consisting of words and (made up) frequencies, where each line consists of a word, the space character, and a positive integer, e.g. ** `fuzzy 53` **. Read the file into a Python list using ** ` open(filename).readlines()` **. Next, break each line into its two fields using ** `split()` **, and convert the number into an integer using ** `int()` **. The result should be a list of the form: ** `[['fuzzy', 53], ...]` **.**"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["[['fuzzy', 53], ['natural', 14], ['language', 12], ['processing', 16]]"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["filename = 'word_freq.txt'\n", "lines = open(filename).readlines()\n", "fields = []\n", "for line in lines:\n", "    field = line.split()\n", "    field[1] = int(field[1])\n", "    fields.append(field)\n", "fields"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**20. Write code to access a favorite webpage and extract some text from it. For example, access a weather site and extract the forecast top temperature for your town or city today.**"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["91"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["url = 'https://weather.com/weather/5day/l/CHXX0044:1:CH'\n", "html = request.urlopen(url).read().decode('utf8')\n", "high = int(re.findall(r'High (\\d+)F', html)[0])                    \n", "high\n", "# I just use regular expression instead of BeautifulSoup, which is a bit tricky:D"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**21. Write a function ** `unknown()` ** that takes a URL as its argument, and returns a list of unknown words that occur on that webpage. In order to do this, extract all substrings consisting of lowercase letters (using ** `re.findall()` **) and remove any items from this set that occur in the Words Corpus ** `(nltk.corpus.words)` **. Try to categorize these words manually and discuss your findings.**"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["def unknown(url):\n", "    html = request.urlopen(url).read().decode('utf8')\n", "    lowers = re.findall(r'\\b[a-z]+', html)\n", "    unknowns = [w for w in lowers if w not in nltk.corpus.words.words()]\n", "    return unknowns\n", "\n", "unknown('https://en.wikipedia.org')\n", "# the function is quite slow..."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**22. Examine the results of processing the URL http://news.bbc.co.uk/ using the regular expressions suggested above. You will see that there is still a fair amount of non-textual data there, particularly Javascript commands. You may also find that sentence breaks have not been properly preserved. Define further regular expressions that improve the extraction of text from this web page.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["['html',\n", " 'html',\n", " 'lang',\n", " 'pw',\n", " 'charset',\n", " 'utf',\n", " 'viewport',\n", " 'http',\n", " 'equiv',\n", " 'ompatible',\n", " 'google',\n", " 'bx',\n", " 'oqt',\n", " 'fdr',\n", " 'gxr',\n", " 'tj',\n", " 'href',\n", " 'bbc',\n", " 'co',\n", " 'uk',\n", " 'preconnect',\n", " 'crossorigin',\n", " 'href',\n", " 'files',\n", " 'bbci',\n", " 'co',\n", " 'uk',\n", " 'preconnect',\n", " 'crossorigin',\n", " 'href',\n", " 'nav',\n", " 'files',\n", " 'bbci',\n", " 'co',\n", " 'uk',\n", " 'preconnect',\n", " 'crossorigin',\n", " 'href',\n", " 'ichef',\n", " 'bbci',\n", " 'co',\n", " 'uk',\n", " 'preconnect',\n", " 'crossorigin',\n", " 'dns',\n", " 'prefetch',\n", " 'href',\n", " 'mybbc',\n", " 'files',\n", " 'bbci',\n", " 'co',\n", " 'uk',\n", " 'dns']"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["file = open('BBC.html').read()         \n", "# For the reason of GFW, BBC is not accessible now. \n", "# I don't know how to set up proxy in Jupyter Notebook\n", "# so I save HTML via safari and deal with the file instead.\n", "\n", "lowers = re.findall(r'[a-z]+', file)\n", "unknowns = [w for w in lowers[:100] if w not in nltk.corpus.words.words()]\n", "# It's too costly to judge whether a word is in nltk's words corpus.\n", "# Therefore, I choose the first words \n", "unknowns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**23. Are you able to write a regular expression to tokenize text in such a way that the word ** *don't* ** is tokenized into**  *do* ** and ** *n't* **? Explain why this regular expression won't work: ** `«n't|\\w+»` **.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": ["pattern = r\"\\w+(?:'t)?\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["The regular expression would detect `don` first and leave out `'t`, which doesn't match the expression `n't` and `t` will be matched to `\\w+`."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**24. Try to write code to convert text into hAck3r, using regular expressions and substitution, where ** `e → 3, i → 1, o → 0, l → |, s → 5, . → 5w33t!, ate → 8` **. Normalize the text to lowercase before converting it. Add more substitutions of your own. Now try to map s to two different values: ** `$` ** for word-initial ** `s` **, and ** `5` ** for word-internal ** `s`**.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["'I 8 an app|3 y35t3rday 1n my (ar, $a1d by T0m5w33t!'"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["text = 'I ate an apple yesterday in my car, said by <PERSON>.'\n", "\n", "# text = re.sub(pattern, repl, text)\n", "text = re.sub(r'ate', '8', text)        # ate -> 8, replace it first, or 'e' will be replaced\n", "text = re.sub(r'e', '3', text)          # e -> 3\n", "text = re.sub(r'i', '1', text)          # i -> 1\n", "text = re.sub(r'o', '0', text)          # o -> 0\n", "text = re.sub(r'l', '|', text)          # l -> |\n", "text = re.sub(r'\\.', '5w33t!', text)    # . -> 5w33t!\n", "\n", "text = re.sub(r'(\\b)(s)', r'\\1$', text)              # word-initial s\n", "text = re.sub(r'(\\w)(s)', r'\\g<1>5', text)           # word-internal s\n", "# reference: https://stackoverflow.com/questions/5984633/python-re-sub-group-number-after-number\n", "\n", "text = re.sub(r'g', '9', text)          # g -> 9\n", "text = re.sub(r'c', '(', text)          # c -> (\n", "text"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**25. ** *Pig Latin* ** is a simple transformation of English text. Each word of the text is converted as follows: move any consonant (or consonant cluster) that appears at the start of the word to the end, then append ** *ay* **, e.g.**  *string → ingstray*, *idle → idleay* **.  http://en.wikipedia.org/wiki/Pig_Latin**  \n", "a. **Write a function to convert a word to Pig Latin.**  \n", "b. **Write code that converts text, instead of individual words.**  \n", "c. **Extend it further to preserve capitalization, to keep ** `qu` ** together (i.e. so that ** `quiet` ** becomes ** `ietquay` **), and to detect when ** `y` ** is used as a consonant (e.g. ** `yellow` **) vs a vowel (e.g. ** `style` **).**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["'ingstray'"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["def pig_latin_word(word):\n", "    pattern = r'\\b([^aei<PERSON>]*)(\\w*)'\n", "    repl = r'\\2\\1ay'\n", "    word = re.sub(pattern, repl, word)\n", "    # word += 'ay'\n", "    return word\n", "\n", "pig_latin_word('string')\n", "\n", "# word = 'idle'\n", "# word = re.sub(r'\\b([^aeiou]*)(\\w*)', r'\\2\\1', word)\n", "# word += 'ay'\n", "# word"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["'ingstray idleay'"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["def pig_latin_text(text):\n", "    pattern = r'\\b([b-df-hj-np-tv-z]*)(\\w*)\\b'\n", "    repl = r'\\2\\1ay'\n", "    text = re.sub(pattern, repl, text)\n", "    return text\n", "\n", "pig_latin_text('string idle')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["'itequay IDLEay'"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["def pig_latin_extention(text):\n", "    # Well, 'Y' as the initial letter seems to be just consonant?\n", "    pattern = r'(?i)\\b(qu|[b-df-hj-np-tv-z]*)(\\w*)\\b'\n", "    repl = r'\\2\\1ay'\n", "    text = re.sub(pattern, repl, text)\n", "    return text\n", "\n", "pig_latin_extention('quite IDLE')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**26. Download some text from a language that has vowel harmony (e.g. Hungarian), extract the vowel sequences of words, and create a vowel bigram table.**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Omitted."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**27. <PERSON>'s random module includes a function ** `choice()` ** which randomly chooses an item from a sequence, e.g. ** `choice(\"aehh \")` ** will produce one of four possible characters, with the letter ** `h` ** being twice as frequent as the others. Write a generator expression that produces a sequence of 500 randomly chosen letters drawn from the string ** `\"aehh \"` **, and put this expression inside a call to the ** `''.join()` ** function, to concatenate them into one long string. You should get a result that looks like uncontrolled sneezing or maniacal laughter: ** `he  haha ee  heheeh eha` **. Use ** `split()` ** and ** `join()` ** again to normalize the whitespace in this string.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["'ehhahehhahe ehaahhe heheha hh hhha e haeehhhhh hhahaheeehehh hahh eeeaehe hhhhheaaaheeahea eeeh heh hhae h ehehaa hheee eeh h haaa heehehee aeaee haehaaha hha e hh a hhee aehh e h hhehahh ahahhhhh aheh h hhh hh ah h aea a heehhhaeeheehha aeaha h aaeaahaaaeaha haaeahahheehe ahae hh haheeehh hhh aha ehhh ehhee aahhee ahhaeee h hehheahehhaa e hehhhe ahhhhhh hhh ahe h a e hahahhhhehh hhehaah heh hhh hh hahh heeh ehhhaaaahahhhehh eeaheeaeahahhhe eaah a aaeh eahhahae hhhahhaeaee hhh'"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["s = []\n", "for i in range(500):\n", "    s.append(random.choice(\"aehh \"))\n", "ori = ''.join(s)                     # the original form may contain multiple blank spaces at the same time\n", "' '.join(ori.split())                # normalize the whitespace"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**28. Consider the numeric expressions in the following sentence from the MedLine Corpus: ** *The corresponding free cortisol fractions in these sera were 4.53 +/- 0.15% and 8.16 +/- 0.23%, respectively.* ** Should we say that the numeric expression ** *4.53 +/- 0.15%* ** is three words? Or should we say that it's a single compound word? Or should we say that it is actually ** *nine* ** words, since it's read \"four point five three, plus or minus zero point fifteen percent\"? Or should we say that it's not a \"real\" word at all, since it wouldn't appear in any dictionary? Discuss these different possibilities. Can you think of application domains that motivate at least two of these answers?**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Well, all the explanations are reasonable. But I don't understand why it is *nine*, shouldn't it be *eleven*?"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**29. Readability measures are used to score the reading difficulty of a text, for the purposes of selecting texts of appropriate difficulty for language learners. Let us define ** `μw` ** to be the average number of letters per word, and ** `μs` ** to be the average number of words per sentence, in a given text. The Automated Readability Index (ARI) of the text is defined to be: ** `4.71 μw + 0.5 μs - 21.43` **. Compute the ARI score for various sections of the Brown Corpus, including section ** `f`** (lore) and ** `j` ** (learned). Make use of the fact that ** `nltk.corpus.brown.words()` ** produces a sequence of words, while ** `nltk.corpus.brown.sents()` ** produces a sequence of sentences.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["adventure 4.0841684990890705\n", "belles_lettres 10.987652885621749\n", "editorial 9.471025332953673\n", "fiction 4.9104735321302115\n", "government 12.08430349501021\n", "hobbies 8.922356393630267\n", "humor 7.887805248319808\n", "learned 11.926007043317348\n", "lore 10.254756197101155\n", "mystery 3.8335518942055167\n", "news 10.176684595052684\n", "religion 10.203109907301261\n", "reviews 10.769699888473433\n", "romance 4.34922419804213\n", "science_fiction 4.978058336905399\n"]}], "source": ["def miu_w(category):\n", "    word_length = sum(len(w) for w in brown.words(categories=category))\n", "    word_number = len(brown.words(categories=category))\n", "    return word_length / word_number\n", "\n", "def miu_s(category):\n", "    sent_length = sum(len(s) for s in brown.sents(categories=category))\n", "    sent_number = len(brown.sents(categories=category))\n", "    return sent_length / sent_number\n", "\n", "def ari(category):\n", "    return 4.71 * miu_w(category) + 0.5 * miu_s(category) - 21.43\n", "\n", "for category in brown.categories():\n", "    print(category, ari(category))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**30. Use the Porter Stemmer to normalize some tokenized text, calling the stemmer on each word. Do the same thing with the Lancaster Stemmer and see if you observe any differences.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw = \"\"\"THE Dawn of Love is an oil painting by English artist \n", "<PERSON>, first exhibited in 1828. Loosely based on a passage \n", "from <PERSON>'s 1634 Comus, it shows <PERSON> leaning across to \n", "wake the sleeping <PERSON> by stroking his wings. It was very poorly \n", "received when first exhibited; the stylised Venus was thought unduly \n", "influenced by foreign artists such as <PERSON><PERSON><PERSON> as well as being overly \n", "voluptuous and unrealistically coloured, while the painting as a whole \n", "was considered tasteless and obscene. The Dawn of Love was omitted \n", "from the major 1849 retrospective exhibition of <PERSON><PERSON>'s works, and \n", "its exhibition in Glasgow in 1899 drew complaints for its supposed \n", "obscenity. In 1889 it was bought by Merton Russell-Cotes, and has \n", "remained in the collection of the Russell-Cotes Art Gallery & Museum ever since.\"\"\"\n", "# from Wikipedia 2018-08-08's featured article\n", "tokens = word_tokenize(raw)\n", "\n", "porter = nltk.<PERSON>()\n", "lancaster = nltk.LancasterStemmer()\n", "\n", "porter_output = [porter.stem(t) for t in tokens]             \n", "lancaster_output = [lancaster.stem(t) for t in tokens]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**31. Define the variable saying to contain the list ** `['After', 'all', 'is', 'said', 'and', 'done', ',', 'more',\n", "'is', 'said', 'than', 'done', '.']` **. Process this list using a ** `for` ** loop, and store the length of each word in a new list lengths. Hint: begin by assigning the empty list to ** `lengths` **, using ** `lengths = []` **. Then each time through the loop, use ** `append()` ** to add another length value to the list. Now do the same thing using a list comprehension.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["var = ['After', 'all', 'is', 'said', 'and', 'done', ',', 'more', 'is', 'said', 'than', 'done', '.']\n", "lengths = []\n", "\n", "for w in var:\n", "    lengths.append(len(w))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lengths = [len(w) for w in var]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**32. Define a variable ** `silly` ** to contain the string: ** 'newly formed bland ideas are inexpressible in an infuriating way' **. (This happens to be the legitimate interpretation that bilingual English-Spanish speakers can assign to <PERSON><PERSON><PERSON>'s famous nonsense phrase, colorless green ideas sleep furiously according to Wikipedia). Now write code to perform the following tasks:**  \n", "a. **Split ** `silly` ** into a list of strings, one per word, using Python's ** `split()` ** operation, and save this to a variable called ** `bland`.  \n", "b. **Extract the second letter of each word in ** `silly` ** and join them into a string, to get ** 'e<PERSON><PERSON><PERSON>na'.  \n", "c. **Combine the words in ** `bland` ** back into a single string, using ** `join()` **. Make sure the words in the resulting string are separated with whitespace.**  \n", "d. **Print the words of ** `silly` ** in alphabetical order, one per line.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['an', 'are', 'bland', 'formed', 'ideas', 'in', 'inexpressible', 'infuriating', 'newly', 'way']\n"]}], "source": ["silly = 'newly formed bland ideas are inexpressible in an infuriating way'\n", "bland = silly.split()                 # a\n", "''.join(w[1] for w in bland)          # b\n", "' '.join(bland)                       # c\n", "print(sorted(bland))                  # d"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**33. The ** `index()` ** function can be used to look up items in sequences. For example, ** `'inexpressible'.index('e')` ** tells us the index of the first position of the letter ** `e`.  \n", "a. **What happens when you look up a substring, e.g. ** `'inexpressible'.index('re')` **?**  \n", "b. **Define a variable ** `words` ** containing a list of words. Now use ** `words.index()` ** to look up the position of an individual word.**  \n", "c. **Define a variable ** `silly` ** as in the exercise above. Use the ** `index()` ** function in combination with list slicing to build a list ** `phrase` ** consisting of all the words up to (but not including) ** `in` ** in silly.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["'inexpressible'.index('re')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["words = ['a', 'list', 'of', 'words']\n", "words.index('of')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["['newly', 'formed', 'bland', 'ideas', 'are', 'inexpressible']"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["phrase = bland[:bland.index('in')]             # use bland rather than silly here\n", "phrase"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**34. Write code to convert nationality adjectives like ** *Canadian* ** and ** *Australian* ** to their corresponding nouns ** *Canada* ** and ** *Australia* ** (see http://en.wikipedia.org/wiki/List_of_adjectival_forms_of_place_names).**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Canada'"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["# the link should be\n", "# https://en.wikipedia.org/wiki/List_of_adjectival_and_demonymic_forms_for_countries_and_nations\n", "\n", "# Argentina - Argentinian\n", "# Australia - Australian\n", "# Austria - Austrian\n", "# to be finished...\n", "\n", "pattern = r'(\\w+)ian'\n", "repl = r'\\1a'\n", "re.sub(pattern, repl, 'Canadian')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**35. Read the LanguageLog post on phrases of the form ** *as best as p can* ** and ** *as best p can* **, where ** *p* ** is a pronoun. Investigate this phenomenon with the help of a corpus and the ** `findall()` ** method for searching tokenized text described in 3.5. http://itre.cis.upenn.edu/~myl/languagelog/archives/002733.html**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["['as best I can', 'as best as I can', 'As best as she can']"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["text = \"\"\" I wil straight dispose, as best I can, th'inferiour Magistrate ...\n", "And I haue thrust my selfe into this maze, Happily to wiue and thriue, as best I may ...\n", "In fine, my life is that of a great schoolboy, getting into scrapes for the fun of it,\n", "and fighting my way out as best as I can!\n", "As best as she can she hides herself in the full sunlight\n", "\"\"\"\n", "# text sample from the given url link\n", "re.findall(r'(?i)as best (?:as )?(?:I|we|you|he|she|they|it) can', text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**36. Study the ** *lolcat* ** version of the book of Genesis, accessible as ** `nltk.corpus.genesis.words('lolcat.txt')` **, and the rules for converting text into ** *lolspeak* ** at http://www.lolcatbible.com/index.php?title=How_to_speak_lolcat. Define regular expressions to convert English words into corresponding lolspeak words.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["'siet kiet dood ovah kitteh littel'"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["# nltk.corpus.genesis.words('lolcat.txt')\n", "text = 'sight kite dude over kitty little'\n", "# just implement some easy-to-check rules\n", "text = re.sub(r'ight', 'iet', text)                             # ight -> iet\n", "text = re.sub(r'\\bdude\\b', 'dood', text)                        # dude -> dood\n", "text = re.sub(r'([b-df-hj-np-tv-z])(e)\\b', r'\\2\\1', text)       # exchange the consonant and the endding 'e'\n", "text = re.sub(r'er\\b', 'ah', text)                              # -er -> -ah\n", "text = re.sub(r'y\\b', 'eh', text)                               # -y -> -eh\n", "text = re.sub(r'le\\b', 'el', text)                              # -le -> -el\n", "text"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**37. Read about the ** `re.sub()` ** function for string substitution using regular expressions, using ** `help(re.sub)` ** and by consulting the further readings for this chapter. Use ** `re.sub` ** in writing code to remove HTML tags from an HTML file, and to normalize whitespace.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["file = open('BBC.html').read()\n", "file = re.sub(r'<.*>', '', file)\n", "file = re.sub(r'\\s+', ' ', file)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**38. An interesting challenge for tokenization is words that have been split across a line-break. E.g. if ** *long-term* ** is split, then we have the string **  *long-\\nterm*.  \n", "a. **Write a regular expression that identifies words that are hyphenated at a line-break. The expression will need to include the ** `\\n` ** character.**  \n", "b. **Use ** `re.sub()` ** to remove the \\n character from these words.**  \n", "c. **How might you identify words that should not remain hyphenated once the newline is removed, e.g. ** 'encyclo-\\npedia'?"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["['long-\\nterm']"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["text = \"\"\"long-\n", "term\"\"\"\n", "pattern = r'\\w+-\\n\\w+'\n", "re.findall(pattern, text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["'long-term'"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["pattern = r'(\\w+-)(\\n)(\\w+)'\n", "re.findall(pattern, text)\n", "re.sub(pattern, r'\\1\\3', text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Check whether the hyphenated word is in the word corpus."]}, {"cell_type": "markdown", "metadata": {}, "source": ["**39. Read the Wikipedia entry on ** *Soundex* **. Implement this algorithm in Python.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["'H555'"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["# https://en.wikipedia.org/wiki/Soundex\n", "# cumbersome implementation...\n", "def soundex(word):\n", "    word = word.upper()         # convert the word to upper case for convenience\n", "    \n", "    # Step 1: Retain the first letter\n", "    sound = word[0]\n", "\n", "    # Step 3: If two or more letters with the same number are adjacent \n", "    # in the original name (before step 1), only retain the first letter;\n", "    word = re.sub(r'([BFPV])[BFPV]', r'\\1', word)             # \n", "    word = re.sub(r'([CGJKQSXZ])[CGJKQSXZ]', r'\\1', word)\n", "    word = re.sub(r'([DT])[DT]', r'\\1', word)\n", "    word = re.sub(r'LL', r'L', word)\n", "    word = re.sub(r'([MN])[MN]', r'\\1', word)\n", "    word = re.sub(r'RR', r'R', word)\n", "    \n", "    # Step 3:  two letters with the same number separated by 'h' or 'w' are coded as a single number\n", "    word = re.sub(r'([BFPV])([HW])[BFPV]', r'\\1\\2', word)\n", "    word = re.sub(r'([CGJKQSXZ])([HW])[CGJKQSXZ]', r'\\1\\2', word)\n", "    word = re.sub(r'([DT])([HW])[DT]', r'\\1\\2', word)\n", "    word = re.sub(r'L([HW])L', r'L\\1', word)\n", "    word = re.sub(r'([MN])([HW])[MN]', r'\\1\\2', word)\n", "    word = re.sub(r'R([HW])R', r'R\\1', word)\n", "    \n", "    # Replace consonants with digits as follows (after the first letter)\n", "    word = re.sub(r'[AEIOUYHW]', r'', word)\n", "    word = re.sub(r'[BFPV]', '1', word)\n", "    word = re.sub(r'[CGJKQSXZ]', '2', word)\n", "    word = re.sub(r'[DT]', '3', word)\n", "    word = re.sub(r'L', '4', word)\n", "    word = re.sub(r'[MN]', '5', word)\n", "    word = re.sub(r'R', '6', word)\n", "    \n", "    # Step 4: If you have too few letters in your word that you can't assign three numbers, \n", "    # append with zeros until there are three numbers. If you have more than 3 letters, \n", "    # just retain the first 3 numbers.\n", "    if sound in 'AEIOUYHW':\n", "        sound = (sound + word + '000')[:4]\n", "    else:\n", "        sound = (sound + word[1:] + '000')[:4]\n", "    return sound\n", "\n", "soundex('Honeyman')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**40. Obtain raw texts from two or more genres and compute their respective reading difficulty scores as in the earlier exercise on reading difficulty. E.g. compare ABC Rural News and ABC Science News (** `nltk.corpus.abc` **). Use Punkt to perform sentence segmentation.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["10.66074843699441\n", "10.703963706930097\n"]}], "source": ["# nltk.corpus.abc.fileids()\n", "\n", "def ari(fileid):\n", "    words = nltk.corpus.abc.words(fileids=fileid)\n", "    \n", "    text = nltk.corpus.abc.raw(fileids=fileid)\n", "    sents = nltk.sent_tokenize(text)\n", "    \n", "    word_number = len(words)\n", "    word_length = sum(len(w) for w in words)\n", "    miu_w = word_length / word_number\n", "\n", "    sent_length = sum(len(s.split()) for s in sents)\n", "    sent_number = len(sents)\n", "    miu_s = sent_length / sent_number\n", "    \n", "    ari = 4.71 * miu_w + 0.5 * miu_s - 21.43\n", "    return ari\n", "print(ari('rural.txt'))\n", "print(ari('science.txt'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**41. Rewrite the following nested loop as a nested list comprehension:**  \n", "```<PERSON>\n", ">>> words = ['attribution', 'confabulation', 'elocution',\n", "...          'sequoia', 'tenacious', 'unidirectional']\n", ">>> vsequences = set()\n", ">>> for word in words:\n", "...     vowels = []\n", "...     for char in word:\n", "...         if char in 'aei<PERSON>':\n", "...             vowels.append(char)\n", "...     vsequences.add(''.join(vowels))\n", ">>> sorted(vsequences)\n", "['aiuio', 'eaiou', 'eouio', 'euoia', 'oauaio', 'uiieioa']\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["['aiuio', 'eaiou', 'eouio', 'euoia', 'oauaio', 'uiieioa']"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["words = ['attribution', 'confabulation', 'elocution', 'sequoia', 'tenacious', 'unidirectional']\n", "vsequences = [''.join(re.findall(r'[aeiou]', v)) for v in words]\n", "sorted(vsequences)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**42. Use WordNet to create a semantic index for a text collection. Extend the concordance search program in 3.6, indexing each word using the offset of its first synset, e.g. ** `wn.synsets('dog')[0].offset` ** (and optionally the offset of some of its ancestors in the hypernym hierarchy).**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ <PERSON> music stops ] ARTHUR : Old woman ! DENNIS : Man ! ARTHUR : Man .   \n", "     I did say ' sorry ' about the ' old woman ', but from the behind you looked \n", "ver going to be any progress with the -- WOMAN : <PERSON> , there ' s some lovely f\n", "  the Britons . Who ' s castle is that ? <PERSON><PERSON><PERSON> : King of the who ? ARTHUR : The  \n", "King of the who ? ARTHUR : The Britons . WOMAN : Who are the Britons ? ARTHUR : W\n", "  are all Britons , and I am your king . WOMAN : I didn ' t know we had a        \n", "utocracy in which the working classes -- WOM<PERSON> : Oh , there you go , bringing cla\n", "am in haste . Who lives in that castle ? WOMAN : No one live there . ARTHUR : The\n", "there . <PERSON><PERSON><PERSON> : Then who is your lord ? WOMAN : We don ' t have a lord .        \n", "    Be quiet ! I order you to be quiet ! WOMAN : Order , eh ? Who does he think  \n", "       ? Heh . <PERSON><PERSON><PERSON> : I am your king ! WOMAN : Well , I didn ' t vote for      \n", "   ARTHUR : You don ' t vote for kings . WOMAN : Well , how did you become king t\n", "am your king ! DENNIS : Listen , strange women lying in ponds distributing swords\n", "icalism is a way of preserving freedom . WOMAN : Oh , <PERSON> , forget about freed\n", "     : Are you saying ' ni ' to that old woman ? ARTHUR : Erm , yes . ROGER :    \n"]}], "source": ["class IndexedText(object):\n", "\n", "    def __init__(self, stemmer, text):\n", "        self._text = text\n", "        self._stemmer = stemmer\n", "        # self._index = nltk.Index((self._stem(word), i)\n", "        #                          for (i, word) in enumerate(text))\n", "        self._index = nltk.Index((wn.synsets(self._stem(word))[0].offset(), i)\n", "                                 for (i, word) in enumerate(text) \n", "                                 if wn.synsets(self._stem(word)) != [])     # to avoid list index out of range\n", "        \n", "        # basic idea: use WordNet's offset as the word's key rather than the word itself\n", "        \n", "    def concordance(self, word, width=40):\n", "        key = wn.synsets(self._stem(word))[0].offset()\n", "        wc = int(width/4)                # words of context\n", "        for i in self._index[key]:\n", "            lcontext = ' '.join(self._text[i-wc:i])\n", "            rcontext = ' '.join(self._text[i:i+wc])\n", "            ldisplay = '{:>{width}}'.format(lcontext[-width:], width=width)\n", "            rdisplay = '{:{width}}'.format(rcontext[:width], width=width)\n", "            print(ldisplay, rdisplay)\n", "\n", "    def _stem(self, word):\n", "        return self._stemmer.stem(word).lower()\n", "    \n", "porter = nltk.<PERSON>()\n", "grail = nltk.corpus.webtext.words('grail.txt')\n", "text = IndexedText(porter, grail)\n", "text.concordance('women')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**43. With the help of a multilingual corpus such as the Universal Declaration of Human Rights Corpus (** `nltk.corpus.udhr` **), and NLTK's frequency distribution and rank correlation functionality (** `nltk.FreqDist`, `nltk.spearman_correlation` **), develop a system that guesses the language of a previously unseen text. For simplicity, work with a single character encoding and just a few languages.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["English-Latin1\n", "French_Francais-Latin1\n", "German_Deutsch-Latin1\n", "Italian-Latin1\n", "Spanish-Latin1\n"]}], "source": ["def guess_language(text):\n", "    candidate_language = ['English-Latin1', 'French_Francais-Latin1', \n", "                          'German_Deutsch-Latin1', 'Italian-Latin1', 'Spanish-Latin1']\n", "\n", "    fdist = nltk.FreqDist(lang for lang in candidate_language\n", "                               for w in text if w in nltk.corpus.udhr.words(lang))\n", "    return fdist\n", "\n", "# well, I just don't want to show the text in multiple lines since the words doesn't matter\n", "text_english = \"Wikipedia is a project dedicated to the building of free encyclopedias in all languages of the world. The project started with the English-language Wikipedia on January 15, 2001. On March 23, 2001 it was joined by a French Wikipedia, and shortly afterwards by many other languages. Large efforts are underway to highlight the international nature of the project. On 20 September 2004 Wikipedia reached a total of 1,000,000 articles in over 100 languages.\".split()\n", "text_french = \"Wikipédia Écouter est un projet d'encyclopédie universelle, multilingue, créé par <PERSON> et <PERSON> le 15 janvier 2001 en wiki sous le nom de domaine wikipedia.org. Les versions des différentes langues utilisent le même logiciel de publication, MediaWiki, et ont la même apparence, mais elles comportent des variations dans leurs contenus, leurs structures et leurs modalités d'édition et de gestion.\".split()\n", "text_german = \"Wikipedia ist ein am 15. Januar 2001 gegründetes gemeinnütziges Projekt zur Erstellung einer Enzyklopädie in zahlreichen Sprachen mit Hilfe des Wiki­prinzips. Gemäß Publikumsnachfrage und Verbreitung gehört Wikipedia unterdessen zu den Massenmedien. Aufgrund der für die Entstehung und Weiterentwicklung dieser Enzyklopädie charakteristischen kollaborativen Erstellungs-, Kontroll- und Aushandlungsprozesse der ehrenamtlichen Beteiligten zählt Wikipedia zugleich zu den Social Media.\".split()\n", "text_italian = \"Wikipedia (pronuncia: vedi sotto) è un'enciclopedia online a contenuto libero, collaborativa, multilingue e gratuita, nata nel 2001, sostenuta e ospitata dalla Wikimedia Foundation, un'organizzazione non a scopo di lucro statunitense. Lanciata da Jimmy Wales e Larry Sanger il 15 gennaio 2001, inizialmente nell'edizione in lingua inglese, nei mesi successivi ha aggiunto edizioni in numerose altre lingue. Sanger ne suggerì il nome,[1] una parola macedonia nata dall'unione della radice wiki al suffisso pedia (da enciclopedia).\".split()\n", "text_spanish = \"Wikipedia es una enciclopedia libre, políglota y editada de manera colaborativa. Es administrada por la Fundación Wikimedia, una organización sin ánimo de lucro cuya financiación está basada en donaciones. Sus más de 46 millones de artículos en 288 idiomas han sido redactados conjuntamente por voluntarios de todo el mundo, lo que hace un total de más de 2000 millones de ediciones, y prácticamente cualquier persona con acceso al proyecto6​ puede editarlos, salvo que la página se encuentre protegida contra vandalismos para evitar problemas y/o trifulcas.\".split()\n", "\n", "print(guess_language(text_english).max())\n", "print(guess_language(text_french).max())\n", "print(guess_language(text_german).max())\n", "print(guess_language(text_italian).max())\n", "print(guess_language(text_spanish).max())\n", "\n", "# I don't know how to use rank correlation functionality :("]}, {"cell_type": "markdown", "metadata": {}, "source": ["**44. Write a program that processes a text and discovers cases where a word has been used with a novel sense. For each word, compute the WordNet similarity between all synsets of the word and all synsets of the words in its context. (Note that this is a crude approach; doing it well is a difficult, open research problem.)**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def novel_sense(text):\n", "    for word in text:\n", "        all_synsets = wn.synsets(word)\n", "        context_synsets = []\n", "        for other_word in text:\n", "            for synset in all_synsets:\n", "                if other_word in synsest:\n", "                    context_synsets.append(synset)\n", "        # after this I don't know what to do...\n", "        # for s1 in all_synsets:\n", "        #     for s2 in context_synsets:\n", "        #         s1.path_similarity(s2)        ?"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**45. Read the article on normalization of non-standard words (<PERSON><PERSON><PERSON><PERSON> et al, 2001), and implement a similar system for text normalization.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": ["# paper link:\n", "# http://citeseerx.ist.psu.edu/viewdoc/download?doi=**********.200&rep=rep1&type=pdf\n", "\n", "# well, I don't have much time reading such a paper now."]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 2}