from openai import OpenAI
client = OpenAI(api_key="********************************************************************************************************************************************************************")
content ="""
Write a python program that takes a sentence as input 
and outputs the longest word in the sentence
"""
completion = client.chat.completions.create(
    model="gpt-4o-mini",
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {
            "role": "user",
            "content": content
        }
    ]
)

print(completion.choices[0].message.content)
#API1: ********************************************************************************************************************************************************************
#API2:********************************************************************************************************************************************************************