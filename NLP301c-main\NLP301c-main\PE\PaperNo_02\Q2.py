import nltk

text = ''''
<PERSON> waited for the train. The train was late. 
<PERSON> and <PERSON> took the bus. 
I looked for <PERSON> and <PERSON><PERSON><PERSON> at the bus size.
'''

word = "<PERSON>"


def percent(word, text):
    text = nltk.tokenize.word_tokenize(text.lower())
    text = [word for word in text if word.isalnum()]
    return text.count(word.lower()) / len(text)


print(percent(word, text))
