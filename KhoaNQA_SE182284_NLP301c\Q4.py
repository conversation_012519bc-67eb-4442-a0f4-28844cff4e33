text = ['one','two','two','four','four','four','four','three','three','three']

import nltk
from nltk.probability import FreqDist

def sort_by_frequency(text):
    # Create a FreqDist object to count word frequencies
    freq_dist = FreqDist(text)

    # Sort the words based on frequency (ascending order)
    sorted_words = sorted(freq_dist, key=freq_dist.get, reverse=True)

    return sorted_words

# Example usage
output_words = sort_by_frequency(text)
print(output_words)