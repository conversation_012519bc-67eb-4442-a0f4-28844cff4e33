"""
Main application for the LLM Question-Answering System
"""

import logging
import sys
import os
from pathlib import Path
from typing import Optional, List, Dict, Any
import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Prompt, Confirm
from rich.markdown import Markdown

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent))

from llm_qa_system import (
    OllamaClient, 
    SemanticSearchEngine, 
    QuestionAnsweringEngine,
    QAGenerator,
    CodebaseIndexer
)
from config import get_config, LOGGING_CONFIG

# Initialize Rich console
console = Console()
app = typer.Typer(help="LLM Question-Answering System for Codebase Analysis")

# Setup logging
logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG["level"]),
    format=LOGGING_CONFIG["format"],
    handlers=[
        logging.FileHandler(LOGGING_CONFIG["file"]),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class QASystemApp:
    """Main application class"""
    
    def __init__(self):
        self.config = get_config()
        self.ollama_client = None
        self.search_engine = None
        self.qa_engine = None
        self.qa_generator = None
        self.indexer = None
        
    def initialize_components(self, codebase_path: str = "."):
        """Initialize all system components"""
        console.print("[bold blue]Initializing LLM QA System...[/bold blue]")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            # Initialize Ollama client
            task1 = progress.add_task("Connecting to Ollama...", total=None)
            self.ollama_client = OllamaClient()
            
            if not self.ollama_client.check_connection():
                console.print("[bold red]❌ Failed to connect to Ollama service![/bold red]")
                console.print("Please ensure Ollama is running on http://localhost:11434")
                raise typer.Exit(1)
            
            # Ensure model is available
            if not self.ollama_client.ensure_model_available():
                console.print(f"[bold red]❌ Failed to load model: {self.ollama_client.default_model}[/bold red]")
                raise typer.Exit(1)
                
            progress.update(task1, description="✅ Ollama connected")
            
            # Initialize search engine
            task2 = progress.add_task("Setting up semantic search...", total=None)
            self.search_engine = SemanticSearchEngine()
            progress.update(task2, description="✅ Semantic search ready")
            
            # Initialize other components
            task3 = progress.add_task("Initializing QA engine...", total=None)
            self.qa_engine = QuestionAnsweringEngine(self.ollama_client, self.search_engine)
            self.qa_generator = QAGenerator(self.ollama_client, self.search_engine)
            self.indexer = CodebaseIndexer(codebase_path)
            progress.update(task3, description="✅ All components ready")
        
        console.print("[bold green]✅ System initialized successfully![/bold green]")
        
    def display_stats(self):
        """Display system statistics"""
        stats = self.search_engine.get_collection_stats()
        
        table = Table(title="Codebase Index Statistics")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Total Chunks", str(stats.get("total_chunks", 0)))
        table.add_row("Languages", ", ".join(stats.get("languages", [])))
        table.add_row("Chunk Types", ", ".join(stats.get("chunk_types", [])))
        table.add_row("Total Files", str(stats.get("total_files", 0)))
        
        console.print(table)


@app.command()
def index(
    path: str = typer.Argument(".", help="Path to codebase to index"),
    force: bool = typer.Option(False, "--force", "-f", help="Force re-indexing")
):
    """Index a codebase for semantic search"""
    
    if not os.path.exists(path):
        console.print(f"[bold red]❌ Path does not exist: {path}[/bold red]")
        raise typer.Exit(1)
    
    qa_system = QASystemApp()
    qa_system.initialize_components(path)
    
    # Check if already indexed
    stats = qa_system.search_engine.get_collection_stats()
    if stats.get("total_chunks", 0) > 0 and not force:
        console.print(f"[yellow]⚠️  Codebase already indexed ({stats['total_chunks']} chunks)[/yellow]")
        if not Confirm.ask("Re-index anyway?"):
            return
        qa_system.search_engine.clear_collection()
    
    console.print(f"[bold blue]📁 Indexing codebase: {path}[/bold blue]")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        
        task = progress.add_task("Scanning files...", total=None)
        chunks = qa_system.indexer.index_codebase()
        
        if not chunks:
            console.print("[bold red]❌ No code chunks found to index![/bold red]")
            return
        
        progress.update(task, description=f"Adding {len(chunks)} chunks to search index...")
        qa_system.search_engine.add_chunks(chunks)
        
        progress.update(task, description="✅ Indexing complete")
    
    console.print(f"[bold green]✅ Successfully indexed {len(chunks)} code chunks![/bold green]")
    qa_system.display_stats()


@app.command()
def ask(
    question: str = typer.Argument(..., help="Question to ask about the codebase"),
    codebase_path: str = typer.Option(".", "--path", "-p", help="Path to codebase"),
    interactive: bool = typer.Option(False, "--interactive", "-i", help="Start interactive mode after answering")
):
    """Ask a question about the codebase"""
    
    qa_system = QASystemApp()
    qa_system.initialize_components(codebase_path)
    
    # Check if codebase is indexed
    stats = qa_system.search_engine.get_collection_stats()
    if stats.get("total_chunks", 0) == 0:
        console.print("[bold red]❌ Codebase not indexed! Run 'python main.py index' first.[/bold red]")
        raise typer.Exit(1)
    
    # Answer the question
    console.print(f"[bold blue]❓ Question:[/bold blue] {question}")
    console.print()
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Searching and generating answer...", total=None)
        result = qa_system.qa_engine.answer_question(question)
    
    # Display answer
    console.print(Panel(
        Markdown(result["answer"]),
        title="🤖 Answer",
        border_style="green"
    ))
    
    # Display sources
    if result["sources"]:
        console.print("\n[bold blue]📚 Sources:[/bold blue]")
        for i, source in enumerate(result["sources"][:3], 1):
            console.print(f"  {i}. {source['file_path']} (lines {source['lines']}) - {source['type']}")
    
    console.print(f"\n[dim]Confidence: {result['confidence']:.2f} | Results: {result['search_results_count']}[/dim]")
    
    if interactive:
        interactive_mode(qa_system)


@app.command()
def interactive(
    codebase_path: str = typer.Option(".", "--path", "-p", help="Path to codebase")
):
    """Start interactive Q&A session"""
    
    qa_system = QASystemApp()
    qa_system.initialize_components(codebase_path)
    
    # Check if codebase is indexed
    stats = qa_system.search_engine.get_collection_stats()
    if stats.get("total_chunks", 0) == 0:
        console.print("[bold red]❌ Codebase not indexed! Run 'python main.py index' first.[/bold red]")
        raise typer.Exit(1)
    
    interactive_mode(qa_system)


def interactive_mode(qa_system: QASystemApp):
    """Interactive Q&A mode"""
    console.print(Panel(
        "[bold green]🚀 Interactive Q&A Mode[/bold green]\n"
        "Ask questions about your codebase. Type 'quit' to exit, 'help' for commands.",
        border_style="blue"
    ))
    
    chat_history = []
    
    while True:
        try:
            question = Prompt.ask("\n[bold blue]Your question[/bold blue]")
            
            if question.lower() in ['quit', 'exit', 'q']:
                console.print("[bold green]👋 Goodbye![/bold green]")
                break
            elif question.lower() == 'help':
                show_help()
                continue
            elif question.lower() == 'stats':
                qa_system.display_stats()
                continue
            elif question.lower() == 'clear':
                chat_history = []
                console.print("[green]Chat history cleared.[/green]")
                continue
            
            # Get answer with chat history
            with console.status("[bold green]Thinking..."):
                result = qa_system.qa_engine.answer_with_chat_history(
                    question, chat_history
                )
            
            # Display answer
            console.print(Panel(
                Markdown(result["answer"]),
                title="🤖 Answer",
                border_style="green"
            ))
            
            # Update chat history
            chat_history.append({"role": "user", "content": question})
            chat_history.append({"role": "assistant", "content": result["answer"]})
            
            # Keep history manageable
            if len(chat_history) > 10:
                chat_history = chat_history[-10:]
            
        except KeyboardInterrupt:
            console.print("\n[bold green]👋 Goodbye![/bold green]")
            break
        except Exception as e:
            console.print(f"[bold red]❌ Error: {e}[/bold red]")


def show_help():
    """Show help information"""
    help_text = """
[bold blue]Available Commands:[/bold blue]
• [green]help[/green] - Show this help message
• [green]stats[/green] - Show codebase statistics  
• [green]clear[/green] - Clear chat history
• [green]quit/exit/q[/green] - Exit interactive mode

[bold blue]Tips:[/bold blue]
• Ask specific questions about functions, classes, or files
• Use natural language - "How does the login function work?"
• Ask for explanations - "Explain the data processing pipeline"
• Request examples - "Show me how to use the API client"
"""
    console.print(Panel(help_text, title="Help", border_style="yellow"))


@app.command()
def generate_qa(
    output_file: str = typer.Option("qa_pairs.json", "--output", "-o", help="Output file for Q&A pairs"),
    num_pairs: int = typer.Option(10, "--num", "-n", help="Number of Q&A pairs to generate"),
    codebase_path: str = typer.Option(".", "--path", "-p", help="Path to codebase")
):
    """Generate Q&A pairs from the codebase"""
    
    qa_system = QASystemApp()
    qa_system.initialize_components(codebase_path)
    
    # Check if codebase is indexed
    stats = qa_system.search_engine.get_collection_stats()
    if stats.get("total_chunks", 0) == 0:
        console.print("[bold red]❌ Codebase not indexed! Run 'python main.py index' first.[/bold red]")
        raise typer.Exit(1)
    
    console.print(f"[bold blue]🔄 Generating {num_pairs} Q&A pairs...[/bold blue]")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Generating Q&A pairs...", total=None)
        
        # Update config for this generation
        qa_system.qa_generator.num_qa_pairs = num_pairs
        qa_pairs = qa_system.qa_generator.generate_sample_qa_pairs()
        
        if qa_pairs:
            qa_system.qa_generator.save_qa_pairs(qa_pairs, output_file)
            progress.update(task, description="✅ Q&A pairs generated")
        else:
            progress.update(task, description="❌ Failed to generate Q&A pairs")
    
    if qa_pairs:
        console.print(f"[bold green]✅ Generated {len(qa_pairs)} Q&A pairs saved to {output_file}[/bold green]")
        
        # Show sample
        if qa_pairs:
            console.print("\n[bold blue]📝 Sample Q&A Pair:[/bold blue]")
            sample = qa_pairs[0]
            console.print(f"[green]Q:[/green] {sample['question']}")
            console.print(f"[blue]A:[/blue] {sample['answer'][:200]}...")
    else:
        console.print("[bold red]❌ Failed to generate Q&A pairs![/bold red]")


@app.command()
def stats(
    codebase_path: str = typer.Option(".", "--path", "-p", help="Path to codebase")
):
    """Show codebase indexing statistics"""
    
    qa_system = QASystemApp()
    qa_system.initialize_components(codebase_path)
    qa_system.display_stats()


if __name__ == "__main__":
    app()
