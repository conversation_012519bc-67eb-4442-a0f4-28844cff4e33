"""
Comprehensive demo of the LLM QA System functionality
"""

import sys
import os
from pathlib import Path
import json
import time

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from llm_qa_system import (
    OllamaClient, 
    SemanticSearchEngine, 
    QuestionAnsweringEngine,
    QAGenerator,
    CodebaseIndexer
)
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.markdown import Markdown

console = Console()


def demo_system_initialization():
    """Demo: Initialize all system components"""
    console.print(Panel(
        "[bold blue]🚀 Demo: System Initialization[/bold blue]",
        border_style="blue"
    ))
    
    # Initialize Ollama client
    console.print("1. Initializing Ollama client...")
    ollama_client = OllamaClient()
    
    if not ollama_client.check_connection():
        console.print("[bold red]❌ Ollama not available! Please start Ollama service.[/bold red]")
        return None, None, None, None
    
    console.print("[green]✅ Ollama connected[/green]")
    
    # List available models
    models = ollama_client.list_models()
    if models:
        console.print(f"[green]Available models: {[m.get('name', 'Unknown') for m in models[:3]}[/green]")
    
    # Initialize semantic search
    console.print("2. Initializing semantic search engine...")
    search_engine = SemanticSearchEngine()
    console.print("[green]✅ Semantic search ready[/green]")
    
    # Initialize QA engine
    console.print("3. Initializing QA engine...")
    qa_engine = QuestionAnsweringEngine(ollama_client, search_engine)
    console.print("[green]✅ QA engine ready[/green]")
    
    # Initialize QA generator
    console.print("4. Initializing QA generator...")
    qa_generator = QAGenerator(ollama_client, search_engine)
    console.print("[green]✅ QA generator ready[/green]")
    
    return ollama_client, search_engine, qa_engine, qa_generator


def demo_codebase_indexing(search_engine):
    """Demo: Index the current codebase"""
    console.print(Panel(
        "[bold blue]📁 Demo: Codebase Indexing[/bold blue]",
        border_style="blue"
    ))
    
    # Initialize indexer for current directory
    indexer = CodebaseIndexer(".")
    
    console.print("1. Scanning codebase...")
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Indexing files...", total=None)
        
        # Index the codebase
        chunks = indexer.index_codebase()
        
        if chunks:
            progress.update(task, description=f"Adding {len(chunks)} chunks to search index...")
            search_engine.add_chunks(chunks)
            progress.update(task, description="✅ Indexing complete")
        else:
            progress.update(task, description="❌ No chunks found")
    
    if chunks:
        console.print(f"[green]✅ Successfully indexed {len(chunks)} code chunks[/green]")
        
        # Show some statistics
        stats = search_engine.get_collection_stats()
        table = Table(title="Indexing Results")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Total Chunks", str(stats.get("total_chunks", 0)))
        table.add_row("Languages", ", ".join(stats.get("languages", [])))
        table.add_row("Chunk Types", ", ".join(stats.get("chunk_types", [])))
        
        console.print(table)
        return True
    else:
        console.print("[bold red]❌ No code chunks found to index[/bold red]")
        return False


def demo_semantic_search(search_engine):
    """Demo: Semantic search functionality"""
    console.print(Panel(
        "[bold blue]🔍 Demo: Semantic Search[/bold blue]",
        border_style="blue"
    ))
    
    # Test queries
    test_queries = [
        "function definition",
        "class implementation",
        "import statements",
        "data processing",
        "machine learning"
    ]
    
    for i, query in enumerate(test_queries, 1):
        console.print(f"\n{i}. Searching for: [bold cyan]'{query}'[/bold cyan]")
        
        results = search_engine.search(query, max_results=3)
        
        if results:
            console.print(f"[green]Found {len(results)} results:[/green]")
            for j, result in enumerate(results, 1):
                metadata = result["metadata"]
                similarity = result["similarity"]
                console.print(f"  {j}. {metadata['file_path']} (lines {metadata['start_line']}-{metadata['end_line']}) - Similarity: {similarity:.2f}")
        else:
            console.print("[yellow]No results found[/yellow]")


def demo_question_answering(qa_engine):
    """Demo: Question answering functionality"""
    console.print(Panel(
        "[bold blue]❓ Demo: Question Answering[/bold blue]",
        border_style="blue"
    ))
    
    # Sample questions
    sample_questions = [
        "What programming languages are used in this codebase?",
        "How is the codebase structured?",
        "What are the main functions or classes?",
        "Are there any machine learning or NLP components?",
        "What external libraries are being used?"
    ]
    
    for i, question in enumerate(sample_questions, 1):
        console.print(f"\n[bold blue]Question {i}:[/bold blue] {question}")
        
        with console.status("[bold green]Generating answer..."):
            result = qa_engine.answer_question(question)
        
        # Display answer
        console.print(Panel(
            Markdown(result["answer"]),
            title=f"🤖 Answer (Confidence: {result['confidence']:.2f})",
            border_style="green"
        ))
        
        # Show sources
        if result["sources"]:
            console.print("[dim]Sources:[/dim]")
            for source in result["sources"][:2]:
                console.print(f"[dim]  - {source['file_path']} (lines {source['lines']})[/dim]")
        
        # Pause between questions
        if i < len(sample_questions):
            time.sleep(1)


def demo_qa_generation(qa_generator):
    """Demo: Q&A pair generation"""
    console.print(Panel(
        "[bold blue]📝 Demo: Q&A Pair Generation[/bold blue]",
        border_style="blue"
    ))
    
    console.print("Generating sample Q&A pairs from the codebase...")
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Generating Q&A pairs...", total=None)
        
        # Generate Q&A pairs
        qa_generator.num_qa_pairs = 5  # Generate 5 pairs for demo
        qa_pairs = qa_generator.generate_sample_qa_pairs()
        
        if qa_pairs:
            progress.update(task, description="✅ Q&A pairs generated")
        else:
            progress.update(task, description="❌ Failed to generate Q&A pairs")
    
    if qa_pairs:
        console.print(f"[green]✅ Generated {len(qa_pairs)} Q&A pairs[/green]")
        
        # Show first Q&A pair as example
        if qa_pairs:
            console.print("\n[bold blue]📋 Sample Q&A Pair:[/bold blue]")
            sample = qa_pairs[0]
            
            console.print(Panel(
                f"[bold green]Q:[/bold green] {sample['question']}\n\n"
                f"[bold blue]A:[/bold blue] {sample['answer'][:300]}{'...' if len(sample['answer']) > 300 else ''}",
                title=f"From: {sample['source']['file_path']}",
                border_style="cyan"
            ))
        
        # Save to file
        output_file = "demo_qa_pairs.json"
        qa_generator.save_qa_pairs(qa_pairs, output_file)
        console.print(f"[green]💾 Q&A pairs saved to {output_file}[/green]")
        
    else:
        console.print("[bold red]❌ Failed to generate Q&A pairs[/bold red]")


def demo_advanced_features(qa_engine, search_engine):
    """Demo: Advanced features"""
    console.print(Panel(
        "[bold blue]⚡ Demo: Advanced Features[/bold blue]",
        border_style="blue"
    ))
    
    # 1. Search with filters
    console.print("1. [bold cyan]Filtered Search[/bold cyan] - Python files only:")
    results = search_engine.search_by_file_type("function", ["python"])
    if results:
        console.print(f"[green]Found {len(results)} Python functions[/green]")
        for result in results[:2]:
            metadata = result["metadata"]
            console.print(f"  - {metadata['file_path']} (lines {metadata['start_line']}-{metadata['end_line']})")
    
    # 2. Chat with history
    console.print("\n2. [bold cyan]Chat with History:[/bold cyan]")
    chat_history = [
        {"role": "user", "content": "What files are in this codebase?"},
        {"role": "assistant", "content": "This codebase contains Python files for NLP and machine learning tasks."}
    ]
    
    follow_up = "Can you tell me more about the specific NLP techniques used?"
    console.print(f"[blue]Follow-up question:[/blue] {follow_up}")
    
    with console.status("[bold green]Processing with context..."):
        result = qa_engine.answer_with_chat_history(follow_up, chat_history)
    
    console.print(Panel(
        Markdown(result["answer"][:200] + "..."),
        title="🤖 Contextual Answer",
        border_style="green"
    ))


def main():
    """Run the complete demo"""
    console.print(Panel(
        "[bold green]🎯 LLM QA System - Complete Demo[/bold green]\n"
        "This demo showcases all major features of the system.",
        title="Demo Start",
        border_style="green"
    ))
    
    # 1. Initialize system
    components = demo_system_initialization()
    if not all(components):
        return
    
    ollama_client, search_engine, qa_engine, qa_generator = components
    
    # 2. Index codebase
    console.print("\n" + "="*60)
    if not demo_codebase_indexing(search_engine):
        console.print("[bold red]Cannot continue without indexed codebase[/bold red]")
        return
    
    # 3. Semantic search
    console.print("\n" + "="*60)
    demo_semantic_search(search_engine)
    
    # 4. Question answering
    console.print("\n" + "="*60)
    demo_question_answering(qa_engine)
    
    # 5. Q&A generation
    console.print("\n" + "="*60)
    demo_qa_generation(qa_generator)
    
    # 6. Advanced features
    console.print("\n" + "="*60)
    demo_advanced_features(qa_engine, search_engine)
    
    # Demo complete
    console.print("\n" + "="*60)
    console.print(Panel(
        "[bold green]🎉 Demo Complete![/bold green]\n\n"
        "[bold blue]What you've seen:[/bold blue]\n"
        "✅ System initialization and setup\n"
        "✅ Codebase indexing and chunking\n"
        "✅ Semantic search with embeddings\n"
        "✅ AI-powered question answering\n"
        "✅ Automatic Q&A pair generation\n"
        "✅ Advanced filtering and chat features\n\n"
        "[bold yellow]Try the interactive mode:[/bold yellow]\n"
        "[green]python main.py interactive[/green]",
        title="Demo Summary",
        border_style="green"
    ))


if __name__ == "__main__":
    main()
