"""
Question-Answering Engine that combines semantic search with LLM processing
"""

import logging
from typing import List, Dict, Any, Optional
from .ollama_client import OllamaClient
from .semantic_search import SemanticSearchEngine
from config import QA_CONFIG

logger = logging.getLogger(__name__)


class QuestionAnsweringEngine:
    """Main QA engine that combines semantic search with LLM responses"""
    
    def __init__(self, ollama_client: OllamaClient, search_engine: SemanticSearchEngine):
        """
        Initialize QA engine
        
        Args:
            ollama_client: Initialized Ollama client
            search_engine: Initialized semantic search engine
        """
        self.ollama_client = ollama_client
        self.search_engine = search_engine
        self.max_context_length = QA_CONFIG["max_context_length"]
        self.temperature = QA_CONFIG["temperature"]
        self.max_tokens = QA_CONFIG["max_tokens"]
        
    def _format_search_results(self, results: List[Dict[str, Any]]) -> str:
        """Format search results into context for LLM"""
        if not results:
            return "No relevant code found in the codebase."
            
        context_parts = []
        total_length = 0
        
        for i, result in enumerate(results):
            metadata = result["metadata"]
            content = result["content"]
            similarity = result["similarity"]
            
            # Create formatted context entry
            entry = f"""
--- Code Snippet {i+1} (Similarity: {similarity:.2f}) ---
File: {metadata['file_path']}
Type: {metadata['chunk_type']} ({metadata['language']})
Lines: {metadata['start_line']}-{metadata['end_line']}

{content}
"""
            
            # Check if adding this entry would exceed context length
            if total_length + len(entry) > self.max_context_length:
                break
                
            context_parts.append(entry)
            total_length += len(entry)
            
        return "\n".join(context_parts)
        
    def _create_qa_prompt(self, question: str, context: str) -> str:
        """Create a prompt for the LLM with question and context"""
        prompt = f"""You are an expert code analyst helping developers understand a codebase. 
Based on the provided code snippets from the codebase, answer the user's question accurately and helpfully.

CODEBASE CONTEXT:
{context}

USER QUESTION: {question}

INSTRUCTIONS:
1. Answer based primarily on the provided code snippets
2. If the code snippets don't contain enough information, say so clearly
3. Provide specific examples from the code when possible
4. Include file paths and line numbers when referencing specific code
5. If you see patterns or best practices in the code, mention them
6. Be concise but thorough in your explanation

ANSWER:"""
        
        return prompt
        
    def answer_question(self, question: str, search_filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Answer a question about the codebase
        
        Args:
            question: User's question
            search_filters: Optional filters for semantic search
            
        Returns:
            Dictionary containing answer, sources, and metadata
        """
        try:
            logger.info(f"Processing question: {question[:100]}...")
            
            # Step 1: Perform semantic search
            search_results = self.search_engine.search(
                query=question,
                filters=search_filters
            )
            
            if not search_results:
                return {
                    "answer": "I couldn't find any relevant code in the codebase to answer your question. Please try rephrasing your question or check if the codebase has been properly indexed.",
                    "sources": [],
                    "confidence": 0.0,
                    "search_results_count": 0
                }
            
            # Step 2: Format context for LLM
            context = self._format_search_results(search_results)
            
            # Step 3: Create prompt and get LLM response
            prompt = self._create_qa_prompt(question, context)
            
            answer = self.ollama_client.generate(
                prompt=prompt,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            
            # Step 4: Prepare response with sources
            sources = []
            for result in search_results:
                metadata = result["metadata"]
                sources.append({
                    "file_path": metadata["file_path"],
                    "lines": f"{metadata['start_line']}-{metadata['end_line']}",
                    "type": metadata["chunk_type"],
                    "language": metadata["language"],
                    "similarity": result["similarity"]
                })
            
            # Calculate confidence based on search results quality
            avg_similarity = sum(r["similarity"] for r in search_results) / len(search_results)
            confidence = min(avg_similarity * 1.2, 1.0)  # Boost slightly, cap at 1.0
            
            return {
                "answer": answer.strip(),
                "sources": sources,
                "confidence": confidence,
                "search_results_count": len(search_results),
                "context_used": len(context)
            }
            
        except Exception as e:
            logger.error(f"Failed to answer question: {e}")
            return {
                "answer": f"Sorry, I encountered an error while processing your question: {str(e)}",
                "sources": [],
                "confidence": 0.0,
                "search_results_count": 0,
                "error": str(e)
            }
    
    def answer_with_chat_history(self, question: str, chat_history: List[Dict[str, str]] = None, 
                                search_filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Answer question with conversation context
        
        Args:
            question: Current question
            chat_history: Previous conversation messages
            search_filters: Optional search filters
            
        Returns:
            Answer with conversation context
        """
        try:
            # Perform semantic search
            search_results = self.search_engine.search(
                query=question,
                filters=search_filters
            )
            
            context = self._format_search_results(search_results)
            
            # Build conversation messages
            messages = []
            
            # System message with context
            system_msg = f"""You are an expert code analyst helping developers understand a codebase.
Use the following code snippets to answer questions:

{context}

Answer based on the provided code, be specific, and include file references when possible."""
            
            messages.append({"role": "system", "content": system_msg})
            
            # Add chat history
            if chat_history:
                messages.extend(chat_history)
            
            # Add current question
            messages.append({"role": "user", "content": question})
            
            # Get response using chat API
            answer = self.ollama_client.chat(
                messages=messages,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            
            # Prepare sources
            sources = []
            for result in search_results:
                metadata = result["metadata"]
                sources.append({
                    "file_path": metadata["file_path"],
                    "lines": f"{metadata['start_line']}-{metadata['end_line']}",
                    "type": metadata["chunk_type"],
                    "language": metadata["language"],
                    "similarity": result["similarity"]
                })
            
            avg_similarity = sum(r["similarity"] for r in search_results) / len(search_results) if search_results else 0
            confidence = min(avg_similarity * 1.2, 1.0)
            
            return {
                "answer": answer.strip(),
                "sources": sources,
                "confidence": confidence,
                "search_results_count": len(search_results)
            }
            
        except Exception as e:
            logger.error(f"Failed to answer with chat history: {e}")
            return {
                "answer": f"Sorry, I encountered an error: {str(e)}",
                "sources": [],
                "confidence": 0.0,
                "search_results_count": 0,
                "error": str(e)
            }
    
    def explain_code(self, file_path: str, start_line: int = None, end_line: int = None) -> Dict[str, Any]:
        """
        Explain specific code from a file
        
        Args:
            file_path: Path to the file
            start_line: Starting line number (optional)
            end_line: Ending line number (optional)
            
        Returns:
            Explanation of the code
        """
        try:
            # Search for code from specific file
            filters = {"file_path": file_path}
            
            search_results = self.search_engine.search(
                query=f"code from {file_path}",
                filters=filters,
                max_results=10
            )
            
            if not search_results:
                return {
                    "answer": f"No code found for file: {file_path}",
                    "sources": [],
                    "confidence": 0.0
                }
            
            # Filter by line numbers if specified
            if start_line is not None or end_line is not None:
                filtered_results = []
                for result in search_results:
                    metadata = result["metadata"]
                    chunk_start = metadata["start_line"]
                    chunk_end = metadata["end_line"]
                    
                    # Check if chunk overlaps with requested lines
                    if start_line is not None and chunk_end < start_line:
                        continue
                    if end_line is not None and chunk_start > end_line:
                        continue
                        
                    filtered_results.append(result)
                
                search_results = filtered_results
            
            if not search_results:
                return {
                    "answer": f"No code found for the specified lines in {file_path}",
                    "sources": [],
                    "confidence": 0.0
                }
            
            context = self._format_search_results(search_results)
            
            prompt = f"""Explain the following code in detail. Describe what it does, how it works, and any important patterns or techniques used:

{context}

Provide a clear, educational explanation that would help a developer understand this code."""
            
            answer = self.ollama_client.generate(
                prompt=prompt,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            
            sources = []
            for result in search_results:
                metadata = result["metadata"]
                sources.append({
                    "file_path": metadata["file_path"],
                    "lines": f"{metadata['start_line']}-{metadata['end_line']}",
                    "type": metadata["chunk_type"],
                    "language": metadata["language"]
                })
            
            return {
                "answer": answer.strip(),
                "sources": sources,
                "confidence": 0.9,  # High confidence for direct code explanation
                "search_results_count": len(search_results)
            }
            
        except Exception as e:
            logger.error(f"Failed to explain code: {e}")
            return {
                "answer": f"Sorry, I encountered an error while explaining the code: {str(e)}",
                "sources": [],
                "confidence": 0.0,
                "error": str(e)
            }
