# Cau 2
#Write a Python program that takes a sentences as input and outputs a new sentence where the first and last words are swapped.


def swap_first_and_last_word(sentence):
    words = sentence.split()

    words[0], words[-1] = words[-1], words[0]

    new_sentence = ' '.join(words)
    return new_sentence

input_sentence = "This is a sample sentence"
result = swap_first_and_last_word(input_sentence)
print(result)