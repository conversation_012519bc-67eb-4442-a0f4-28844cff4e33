"""
Configuration settings for the LLM QA System
"""

import os
from pathlib import Path
from typing import Dict, Any

# Base configuration
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "data"
MODELS_DIR = BASE_DIR / "models"
LOGS_DIR = BASE_DIR / "logs"

# Create directories if they don't exist
for directory in [DATA_DIR, MODELS_DIR, LOGS_DIR]:
    directory.mkdir(exist_ok=True)

# Ollama configuration
OLLAMA_CONFIG = {
    "base_url": "http://localhost:11434",
    "default_model": "qwen2.5",
    "timeout": 60,
    "max_retries": 3
}

# Semantic search configuration with ChromaDB
SEARCH_CONFIG = {
    "embedding_model": "all-MiniLM-L6-v2",
    "chunk_size": 1000,
    "chunk_overlap": 200,
    "max_results": 5,
    "similarity_threshold": 0.7,
    "chroma_db_path": str(DATA_DIR / "chroma_db"),
    "collection_name": "codebase_chunks",
    "distance_metric": "cosine"
}

# Codebase indexing configuration
INDEXING_CONFIG = {
    "supported_extensions": [".py", ".md", ".txt", ".rst", ".ipynb"],
    "ignore_dirs": ["__pycache__", ".git", "node_modules", ".venv", "venv"],
    "ignore_files": [".gitignore", ".env", "*.pyc", "*.log"],
    "max_file_size_mb": 10
}

# QA generation configuration
QA_CONFIG = {
    "max_context_length": 4000,
    "temperature": 0.7,
    "max_tokens": 500,
    "num_qa_pairs": 10
}

# Logging configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": LOGS_DIR / "qa_system.log"
}

def get_config() -> Dict[str, Any]:
    """Get complete configuration dictionary"""
    return {
        "ollama": OLLAMA_CONFIG,
        "search": SEARCH_CONFIG,
        "indexing": INDEXING_CONFIG,
        "qa": QA_CONFIG,
        "logging": LOGGING_CONFIG,
        "paths": {
            "base": BASE_DIR,
            "data": DATA_DIR,
            "models": MODELS_DIR,
            "logs": LOGS_DIR
        }
    }
