2025-07-30 10:22:49,453 - llm_qa_system.ollama_client - INFO - Model llama3.2 not found, attempting to pull...
2025-07-30 10:22:51,514 - llm_qa_system.ollama_client - INFO - Pull status: pulling manifest
2025-07-30 10:22:53,420 - llm_qa_system.ollama_client - INFO - Pull status: pulling dde5aa3fc5ff
2025-07-30 10:22:53,420 - llm_qa_system.ollama_client - INFO - Pull status: pulling 966de95ca8a6
2025-07-30 10:22:53,421 - llm_qa_system.ollama_client - INFO - Pull status: pulling fcc5a6bec9da
2025-07-30 10:22:53,421 - llm_qa_system.ollama_client - INFO - Pull status: pulling a70ff7e570d9
2025-07-30 10:22:53,421 - llm_qa_system.ollama_client - INFO - Pull status: pulling 56bb8bd477a5
2025-07-30 10:22:53,421 - llm_qa_system.ollama_client - INFO - Pull status: pulling 34bb5ab01051
2025-07-30 10:22:53,422 - llm_qa_system.ollama_client - INFO - Pull status: verifying sha256 digest
2025-07-30 10:22:53,422 - llm_qa_system.ollama_client - INFO - Pull status: writing manifest
2025-07-30 10:22:53,422 - llm_qa_system.ollama_client - INFO - Pull status: success
2025-07-30 10:22:53,642 - llm_qa_system.semantic_search - INFO - Loading embedding model: all-MiniLM-L6-v2
2025-07-30 10:22:53,644 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-30 10:22:53,645 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-30 10:22:56,691 - llm_qa_system.semantic_search - INFO - Using existing collection: codebase_chunks
2025-07-30 10:22:56,703 - llm_qa_system.codebase_indexer - INFO - Starting codebase indexing from .
2025-07-30 10:22:56,704 - llm_qa_system.codebase_indexer - ERROR - Failed to process file config.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,704 - llm_qa_system.codebase_indexer - ERROR - Failed to process file download.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,705 - llm_qa_system.codebase_indexer - ERROR - Failed to process file main.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,705 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP_Coursera.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,706 - llm_qa_system.codebase_indexer - ERROR - Failed to process notebook note.ipynb: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,707 - llm_qa_system.codebase_indexer - ERROR - Failed to process file open_ai.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,709 - llm_qa_system.codebase_indexer - ERROR - Failed to process notebook pe_NLP.ipynb: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,709 - llm_qa_system.codebase_indexer - ERROR - Failed to process file README.md: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,710 - llm_qa_system.codebase_indexer - ERROR - Failed to process file requirements.txt: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,710 - llm_qa_system.codebase_indexer - ERROR - Failed to process file setup.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,711 - llm_qa_system.codebase_indexer - ERROR - Failed to process file test_installation.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,711 - llm_qa_system.codebase_indexer - ERROR - Failed to process file Tonghop.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,712 - llm_qa_system.codebase_indexer - ERROR - Failed to process file examples\demo.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,713 - llm_qa_system.codebase_indexer - ERROR - Failed to process file KhoaNQA_SE182284_NLP301c\Q1.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,713 - llm_qa_system.codebase_indexer - ERROR - Failed to process file KhoaNQA_SE182284_NLP301c\Q2.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,713 - llm_qa_system.codebase_indexer - ERROR - Failed to process file KhoaNQA_SE182284_NLP301c\Q3.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,713 - llm_qa_system.codebase_indexer - ERROR - Failed to process file KhoaNQA_SE182284_NLP301c\Q4.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,715 - llm_qa_system.codebase_indexer - ERROR - Failed to process file llm_qa_system\codebase_indexer.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,715 - llm_qa_system.codebase_indexer - ERROR - Failed to process file llm_qa_system\ollama_client.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,715 - llm_qa_system.codebase_indexer - ERROR - Failed to process file llm_qa_system\qa_engine.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,716 - llm_qa_system.codebase_indexer - ERROR - Failed to process file llm_qa_system\qa_generator.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,716 - llm_qa_system.codebase_indexer - ERROR - Failed to process file llm_qa_system\semantic_search.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,716 - llm_qa_system.codebase_indexer - ERROR - Failed to process file llm_qa_system\__init__.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,717 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\README.md: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,717 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\README.txt: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,718 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\requirements.txt: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,718 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_01\Q1.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,718 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_01\Q2.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,718 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_01\Q3.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,719 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_01\Q4.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,719 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_02\Q1.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,719 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_02\Q2.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,720 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_02\Q3.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,720 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_02\Q4.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,721 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_03\Q1.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,722 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_03\Q2.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,722 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_03\Q3.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,723 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_03\Q4.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,723 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_05\Q1.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,724 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_05\Q2.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,724 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_05\Q3.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,724 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_05\Q4.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,725 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_06\Q1.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,725 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_06\Q2.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,725 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_06\Q3.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,725 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_06\Q4.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,726 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_07\Q1.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,726 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_07\Q2.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,726 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_07\Q3.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,727 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_07\Q4.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,727 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_07\Q5.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,727 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_08\Q1.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,728 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_08\Q2.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,728 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_08\Q3.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,728 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_08\Q4.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,728 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_09\Q1.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,729 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_09\Q2.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,729 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_09\Q3.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,729 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_09\Q4.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,730 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_10\Q1.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,730 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_10\Q2.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,730 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_10\Q3.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,730 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\PE\PaperNo_10\Q4.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,731 - llm_qa_system.codebase_indexer - ERROR - Failed to process notebook NLP301c-main\NLP301c-main\Sample\NLP Exercises\Raw\Code.ipynb: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,732 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\Sample\NLP Exercises\Raw\Code.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,733 - llm_qa_system.codebase_indexer - ERROR - Failed to process notebook NLP301c-main\NLP301c-main\Sample\NLP Exercises\Refined\Code.ipynb: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,736 - llm_qa_system.codebase_indexer - ERROR - Failed to process notebook NLP301c-main\NLP301c-main\Sample\NLP Exercises\Regex\Regex Code.ipynb: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,738 - llm_qa_system.codebase_indexer - ERROR - Failed to process notebook NLP301c-main\NLP301c-main\Sample\NLP with Python and NLTK\Chapter 1.ipynb: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,738 - llm_qa_system.codebase_indexer - ERROR - Failed to process notebook NLP301c-main\NLP301c-main\Sample\NLP with Python and NLTK\Chapter 2.ipynb: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,740 - llm_qa_system.codebase_indexer - ERROR - Failed to process notebook NLP301c-main\NLP301c-main\Sample\NLP with Python and NLTK\Chapter 3.ipynb: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,740 - llm_qa_system.codebase_indexer - ERROR - Failed to process notebook NLP301c-main\NLP301c-main\Sample\NLP with Python and NLTK\Chapter 4.ipynb: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,740 - llm_qa_system.codebase_indexer - ERROR - Failed to process notebook NLP301c-main\NLP301c-main\Sample\NLP with Python and NLTK\Chapter 5.ipynb: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,741 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\Sample\NLP with Python and NLTK\corpus.txt: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,741 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c-main\NLP301c-main\Sample\NLP with Python and NLTK\word_freq.txt: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,742 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_FALL23_PE1\Q1-1.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,742 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_FALL23_PE1\Q1-2.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,742 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_FALL23_PE1\Q2.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,743 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_FALL23_PE1\Q3.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,743 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_FALL23_PE1\Q4.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,743 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_FALL23_PE1\test.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,744 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SM22_PE1_771098_de thi-20230808T014144Z-001\NLP301c_SM22_PE1_771098_de thi\1\Q1_test.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,744 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SM22_PE1_771098_de thi-20230808T014144Z-001\NLP301c_SM22_PE1_771098_de thi\2\Q2_test.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,745 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SM22_PE2_436217_de thi-20230808T014205Z-001\NLP301c_SM22_PE2_436217_de thi\1\Q3_test.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,745 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SM22_PE2_436217_de thi-20230808T014205Z-001\NLP301c_SM22_PE2_436217_de thi\2\Q4_test.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,746 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SP22_PE1_907632_de thi-20230808T014222Z-001\NLP301c_SP22_PE1_907632_de thi\1\Q5_test.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,747 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SP22_PE1_907632_de thi-20230808T014222Z-001\NLP301c_SP22_PE1_907632_de thi\2\Q1.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,747 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SP22_PE1_907632_de thi-20230808T014222Z-001\NLP301c_SP22_PE1_907632_de thi\2\Q2.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,748 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SP22_PE1_907632_de thi-20230808T014222Z-001\NLP301c_SP22_PE1_907632_de thi\2\Q3.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,748 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SP22_PE1_907632_de thi-20230808T014222Z-001\NLP301c_SP22_PE1_907632_de thi\2\Q4.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,749 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SP24_PE1\Q1.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,749 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SP24_PE1\Q2.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,749 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SP24_PE1\Q3.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,750 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SP24_PE1\Q4.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,750 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SP24_RETAKE\Q1.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,750 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SP24_RETAKE\Q2.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,751 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SP24_RETAKE\Q3.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,751 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SP24_RETAKE\Q4.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,751 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SU23_PE1\Q1.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,751 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SU23_PE1\Q2.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,752 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SU23_PE1\Q3.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,752 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SU23_PE1\Q4.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,752 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SU23_PE2(retake)\Q1.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,752 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SU23_PE2(retake)\Q2.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,753 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SU23_PE2(retake)\Q3.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,753 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SU23_PE2(retake)\Q4.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,753 - llm_qa_system.codebase_indexer - ERROR - Failed to process file NLP301c_PE_Thanh_gui\NLP301c_SU23_PE2(retake)\test.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,754 - llm_qa_system.codebase_indexer - ERROR - Failed to process file PE_SU24_1\Q1.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,754 - llm_qa_system.codebase_indexer - ERROR - Failed to process file PE_SU24_1\Q2.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,754 - llm_qa_system.codebase_indexer - ERROR - Failed to process file PE_SU24_1\Q3.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,754 - llm_qa_system.codebase_indexer - ERROR - Failed to process file PE_SU24_1\Q4.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,754 - llm_qa_system.codebase_indexer - ERROR - Failed to process file PE_SU24_1\Q5.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,755 - llm_qa_system.codebase_indexer - ERROR - Failed to process file PE_SU24_2\Q3.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,755 - llm_qa_system.codebase_indexer - ERROR - Failed to process file PE_SU24_2\Q4.py: CodeChunk.__init__() missing 1 required positional argument: 'size'
2025-07-30 10:22:56,755 - llm_qa_system.codebase_indexer - INFO - Indexed 113 files, extracted 0 chunks
