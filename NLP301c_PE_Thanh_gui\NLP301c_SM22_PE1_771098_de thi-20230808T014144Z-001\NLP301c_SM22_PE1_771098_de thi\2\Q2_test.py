#####Q1.Define s as a text
###Write expressions for finding all words in s that contain the letter z
#####The result should be in the form of a list of words: ['word1', 'word2', ...].

# import nltk
#
# s = input("Enter the text: ")
#
# # Tokenize the text
# words = nltk.word_tokenize(s)
#
# # Find the words that contain 'z'
# z_words = [word for word in words if 'z' in word.lower()]
#
# print(z_words)
#
#

#####Q2.Question 2: (2 marks)
######Write a program to find all words that occur at least three times in the Brown Corpus.


# import nltk
# from nltk.corpus import brown
# from nltk.probability import FreqDist
#
# # Ensure to download the 'brown' package if not already downloaded
# nltk.download('brown')
#
# # Create a frequency distribution for all words in the Brown Corpus
# freqDist = FreqDist(word.lower() for word in brown.words())
#
# # Find words that appear at least three times
# words = [word for word, freq in freqDist.items() if freq >= 3]
#
# print(words)


#####Q3.Write a Python NLTK program to tokenize words, sentence wise.
#Sample Output:
#Original string:
#<PERSON> waited for the train. The train was late. <PERSON> and <PERSON> took the bus. I looked for
#<PERSON> and Samantha at the bus station.
#Tokenize words sentence wise:
#Read the list:
#['Joe', 'waited', 'for', 'the', 'train', '']
#['The', 'train', 'was', 'late', '']
#['Mary', 'and', 'Samantha', 'took', 'the', 'bus', '']
#['I', 'looked', 'for', 'Mary', 'and', 'Samantha', 'at', 'the', 'bus', 'station', '.']

# import nltk
# nltk.download('punkt')
# s = "Joe waited for the train. The train was late. Mary and Samantha took the bus. I looked for Mary and Samantha at the bus station."
# sentences = nltk.sent_tokenize(s)
# for sentence in sentences:
#     words = nltk.word_tokenize(sentence)
#     print(words)


####Q4.Replace the pronouns in below text by the respective object names
#Input:
#text=" My sister has a dog and she loves him"
#Desired Output:
#[My sister,she]
#[a dog,him]


# import nltk
# from nltk.tokenize import word_tokenize
# from nltk.tag import pos_tag
#
# nltk.download('punkt')
# nltk.download('averaged_perceptron_tagger')
#
# def replace_pronouns(text):
#     tokens = word_tokenize(text)
#     tagged_tokens = pos_tag(tokens)
#
#     antecedents = {}
#     pronoun_pairs = []
#
#     for i, (token, tag) in enumerate(tagged_tokens):
#         if tag == 'PRP':
#             if i > 0 and tagged_tokens[i - 1][1] == 'NN':
#                 antecedents[token] = tagged_tokens[i - 1][0]
#
#     for token, antecedent in antecedents.items():
#         pronoun_pairs.append([antecedent, token])
#
#     return pronoun_pairs
#
# text = "My sister has a dog and she loves him"
# pronoun_pairs = replace_pronouns(text)
#
# for antecedent, pronoun in pronoun_pairs:
#     print(f"[{antecedent},{pronoun}]")