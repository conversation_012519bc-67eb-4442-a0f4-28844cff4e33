""""Question 4: (3 marks)
Tokenize the given text with stop words ('is", "the", "was") as delimiters.
Input:
text = "<PERSON> was feeling anxious. He was diagnosed today. He probably is the best I know."
Output:
['<PERSON>',
'feeling anxious',
'He',
'diagnosed today',
'He probably',
'best person I know'] """

import nltk
import re

# Ensure necessary data is downloaded
nltk.download('punkt')

# Define the input text and stop words
text = "<PERSON> was feeling anxious. He was diagnosed today. He probably is the best I know."
stop_words = ['is', 'the', 'was']

# Tokenize the text into sentences
sentences = nltk.sent_tokenize(text)

# Define a function to split text using stop words and remove periods
def split_by_stop_words(sentence, stop_words):
    # Create a regular expression pattern for the stop words
    pattern = r'\b(?:' + '|'.join(re.escape(word) for word in stop_words) + r')\b'
    # Split the sentence using the pattern
    tokens = re.split(pattern, sentence)
    # Strip any leading or trailing whitespace, remove periods, and filter out empty strings
    return [token.replace('.', '').strip() for token in tokens if token.strip()]

# Apply the split function to each sentence and flatten the result
tokens = [split_by_stop_words(sentence, stop_words) for sentence in sentences]
# Flatten the list of lists into a single list
tokens = [item for sublist in tokens for item in sublist]

print(tokens)
