"""
Setup script for the LLM QA System
"""

import subprocess
import sys
import os
from pathlib import Path
import requests
import time
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()


def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        console.print("[bold red]❌ Python 3.8+ is required![/bold red]")
        console.print(f"Current version: {sys.version}")
        return False
    console.print(f"[green]✅ Python {sys.version_info.major}.{sys.version_info.minor} detected[/green]")
    return True


def install_dependencies():
    """Install Python dependencies"""
    console.print("[bold blue]📦 Installing Python dependencies...[/bold blue]")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        console.print("[green]✅ Dependencies installed successfully[/green]")
        return True
    except subprocess.CalledProcessError as e:
        console.print(f"[bold red]❌ Failed to install dependencies: {e}[/bold red]")
        return False


def check_ollama_installation():
    """Check if Ollama is installed and running"""
    console.print("[bold blue]🔍 Checking Ollama installation...[/bold blue]")
    
    # Check if Ollama service is running
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            console.print("[green]✅ Ollama is running[/green]")
            return True
    except requests.exceptions.RequestException:
        pass
    
    console.print("[yellow]⚠️  Ollama is not running or not installed[/yellow]")
    return False


def install_ollama_instructions():
    """Provide instructions for installing Ollama"""
    instructions = """
[bold blue]📋 Ollama Installation Instructions:[/bold blue]

[bold yellow]For macOS/Linux:[/bold yellow]
curl -fsSL https://ollama.ai/install.sh | sh

[bold yellow]For Windows:[/bold yellow]
Download from: https://ollama.ai/download/windows

[bold yellow]After installation:[/bold yellow]
1. Start Ollama service:
   ollama serve

2. Pull a model (in another terminal):
   ollama pull llama3.2

3. Verify installation:
   curl http://localhost:11434/api/tags
"""
    
    console.print(Panel(instructions, title="Ollama Setup", border_style="blue"))


def setup_directories():
    """Create necessary directories"""
    console.print("[bold blue]📁 Setting up directories...[/bold blue]")
    
    directories = ["data", "models", "logs", "data/chroma_db"]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    console.print("[green]✅ Directories created[/green]")


def test_system():
    """Test if the system is working"""
    console.print("[bold blue]🧪 Testing system components...[/bold blue]")
    
    try:
        # Test imports
        from llm_qa_system import OllamaClient, SemanticSearchEngine
        console.print("[green]✅ Imports working[/green]")
        
        # Test Ollama connection
        client = OllamaClient()
        if client.check_connection():
            console.print("[green]✅ Ollama connection working[/green]")
            
            # Test model availability
            models = client.list_models()
            if models:
                console.print(f"[green]✅ Found {len(models)} Ollama models[/green]")
                for model in models[:3]:  # Show first 3 models
                    console.print(f"  - {model.get('name', 'Unknown')}")
            else:
                console.print("[yellow]⚠️  No models found. Run 'ollama pull llama3.2'[/yellow]")
        else:
            console.print("[red]❌ Ollama connection failed[/red]")
            return False
        
        # Test semantic search initialization
        search_engine = SemanticSearchEngine()
        console.print("[green]✅ Semantic search engine initialized[/green]")
        
        return True
        
    except Exception as e:
        console.print(f"[bold red]❌ System test failed: {e}[/bold red]")
        return False


def main():
    """Main setup function"""
    console.print(Panel(
        "[bold green]🚀 LLM QA System Setup[/bold green]\n"
        "This script will help you set up the Question-Answering system.",
        title="Setup Wizard",
        border_style="blue"
    ))
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Setup directories
    setup_directories()
    
    # Install dependencies
    if not install_dependencies():
        return False
    
    # Check Ollama
    if not check_ollama_installation():
        install_ollama_instructions()
        console.print("\n[bold yellow]Please install and start Ollama, then run this setup again.[/bold yellow]")
        return False
    
    # Test system
    if test_system():
        console.print(Panel(
            "[bold green]🎉 Setup completed successfully![/bold green]\n\n"
            "[bold blue]Next steps:[/bold blue]\n"
            "1. Index your codebase: [green]python main.py index[/green]\n"
            "2. Ask questions: [green]python main.py ask \"How does this code work?\"[/green]\n"
            "3. Start interactive mode: [green]python main.py interactive[/green]\n\n"
            "For more information, see README.md",
            title="Setup Complete",
            border_style="green"
        ))
        return True
    else:
        console.print(Panel(
            "[bold red]❌ Setup encountered issues[/bold red]\n\n"
            "Please check the error messages above and:\n"
            "1. Ensure Ollama is properly installed and running\n"
            "2. Check that all dependencies are installed\n"
            "3. Verify Python version compatibility",
            title="Setup Failed",
            border_style="red"
        ))
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
