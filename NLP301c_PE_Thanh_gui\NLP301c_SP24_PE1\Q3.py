""""Question 3: (3 marks)
Write a program to extract the FPT University email addresses present in the text.
Input:
text = 'Please contact <NAME_EMAIL> for further information. You can also
give <NAME_EMAIL>'
Output:
['<EMAIL>']"""

import re
def extract_fpt_emails(text):
    # Define the regular expression pattern for FPT University emails
    pattern = r'\b[A-Za-z0-9._%+-]+@fpt\.edu\.vn\b'

    # Find all matching email addresses
    emails = re.findall(pattern, text)

    return emails


# Example usage
text = 'Please contact <NAME_EMAIL> for further information. You can also give <NAME_EMAIL>'
fpt_emails = extract_fpt_emails(text)
print(fpt_emails)