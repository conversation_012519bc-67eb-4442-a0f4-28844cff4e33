{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### 1. Import nltk and download the ‘stopwords’ and ‘punkt’ packages\n", "Difficulty Level : L1\n", "\n", "Q. Import nltk and necessary packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import nltk\n", "nltk.download('punkt')\n", "nltk.download('stop')\n", "nltk.download('stopwords')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2. Import spacy and load the language model\n", "Difficulty Level : L1\n", "\n", "Q. Import spacy library and load ‘en_core_web_sm’ model for english language. Load ‘xx_ent_wiki_sm’ for multi language support."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import spacy\n", "nlp = spacy.load(\"en_core_web_sm\")\n", "nlp"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3. How to tokenize a given text?\n", "Difficulty Level : L1\n", "\n", "Q. Print the tokens of the given text document"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"Last week, the University of Cambridge shared its own research that shows if everyone wears a mask outside home,dreaded ‘second wave’ of the pandemic can be avoided.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "Last\n", "week\n", ",\n", "the\n", "University\n", "of\n", "Cambridge\n", "shared\n", "...(truncated)...\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Method 1\n", "# Tokeniation with nltk\n", "import nltk\n", "tokens=nltk.word_tokenize(text)\n", "for token in tokens:\n", "  print(token)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Method 2\n", "# Tokenization with spaCy\n", "import spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "doc=nlp(text)\n", "for token in doc:\n", "  print(token.text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 4. How to get the sentences of a text document?\n", "Difficulty Level : L1\n", "\n", "Q. Print the sentences of the given text document"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"\"\"The outbreak of coronavirus disease 2019 (COVID-19) has created a global health crisis that has had a deep impact on the way we perceive our world and our everyday lives. Not only the rate of contagion and patterns of transmission threatens our sense of agency, but the safety measures put in place to contain the spread of the virus also require social distancing by refraining from doing what is inherently human, which is to find solace in the company of others. Within this context of physical threat, social and physical distancing, as well as public alarm, what has been (and can be) the role of the different mass media channels in our lives on individual, social and societal levels? Mass media have long been recognized as powerful forces shaping how we experience the world and ourselves. This recognition is accompanied by a growing volume of research, that closely follows the footsteps of technological transformations (e.g. radio, movies, television, the internet, mobiles) and the zeitgeist (e.g. cold war, 9/11, climate change) in an attempt to map mass media major impacts on how we perceive ourselves, both as individuals and citizens. Are media (broadcast and digital) still able to convey a sense of unity reaching large audiences, or are messages lost in the noisy crowd of mass self-communication? \"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "The outbreak of coronavirus disease 2019 (COVID-19) has created a global health crisis that has had a deep impact on the way we perceive our world and our everyday lives.\n", "Not only the rate of contagion and patterns of transmission threatens our sense of agency, but the safety measures put in place to contain the spread of the virus also require social distancing by refraining from doing what is inherently human, which is to find solace in the company of others.\n", "Within this context of physical threat, social and physical distancing, as well as public alarm, what has been (and can be)\n", "...(truncated)...\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Tokenizing the text into sentences with spaCy\n", "import spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "doc=nlp(text)\n", "for sentence in doc.sents:\n", "  print(sentence)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extracting sentences with nltk\n", "nltk.sent_tokenize(text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 5. How to tokenize a text using the `transformers` package?\n", "Difficulty Level : L1\n", "\n", "<PERSON>. Tokenize the given text in encoded form using the tokenizer of Huggingface’s transformer package."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"I love spring season. I go hiking with my friends\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "[101, 1045, 2293, 3500, 2161, 1012, 1045, 2175, 13039, 2007, 2026, 2814, 102]\n", "[CLS] i love spring season. i go hiking with my friends [SEP]\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import tokenizer from transfromers\n", "from transformers import AutoTokenizer\n", "import warnings\n", "warnings.filterwarnings(\"ignore\", category=DeprecationWarning) \n", "\n", "# Initialize the tokenizer\n", "tokenizer=AutoTokenizer.from_pretrained('bert-base-uncased')\n", "\n", "# Encoding with the tokenizer\n", "inputs=tokenizer.encode(text)\n", "print(inputs)\n", "print(tokenizer.decode(inputs))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 6. How to tokenize text with stopwords as delimiters?\n", "Difficulty Level : L2\n", "\n", "<PERSON>. Tokenize the given text with stop words (“is”,”the”,”was”) as delimiters. Tokenizing this way identifies meaningful phrases. Sometimes, useful for topic modeling"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text = \"<PERSON> was feeling anxious. He was diagnosed today. He probably is the best person I know.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Expected Output :\n", "```\n", "['<PERSON>',\n", " 'feeling anxious',\n", " 'He',\n", " 'diagnosed today',\n", " 'He probably',\n", " 'best person I know']\n", "```\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Solution\n", "text = \"<PERSON> was feeling anxious. He was diagnosed today. He probably is the best person I know.\"\n", "\n", "stop_words_and_delims = ['was', 'is', 'the', '.', ',', '-', '!', '?']\n", "for r in stop_words_and_delims:\n", "    text = text.replace(r, 'DELIM')\n", "\n", "words = [t.strip() for t in text.split('DELIM')]\n", "words_filtered = list(filter(lambda a: a not in [''], words))\n", "words_filtered"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### 7. How to remove stop words in a text ?\n", "Difficulty Level : L1\n", "\n", "<PERSON>. Remove all the stopwords ( ‘a’ , ‘the’, ‘was’…) from the text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"\"\"the outbreak of coronavirus disease 2019 (COVID-19) has created a global health crisis that has had a deep impact on the way we perceive our world and our everyday lives. Not only the rate of contagion and patterns of transmission threatens our sense of agency, but the safety measures put in place to contain the spread of the virus also require social distancing by refraining from doing what is inherently human, which is to find solace in the company of others. Within this context of physical threat, social and physical distancing, as well as public alarm, what has been (and can be) the role of the different mass media channels in our lives on individual, social and societal levels? Mass media have long been recognized as powerful forces shaping how we experience the world and ourselves. This recognition is accompanied by a growing volume of research, that closely follows the footsteps of technological transformations (e.g. radio, movies, television, the internet, mobiles) and the zeitgeist (e.g. cold war, 9/11, climate change) in an attempt to map mass media major impacts on how we perceive ourselves, both as individuals and citizens. Are media (broadcast and digital) still able to convey a sense of unity reaching large audiences, or are messages lost in the noisy crowd of mass self-communication?\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "'outbreak coronavirus disease 2019 ( COVID-19 ) created global health crisis deep impact way perceive world everyday lives . rate contagion patterns transmission threatens sense agency , safety measures place contain spread virus require social distancing refraining inherently human , find solace company . context physical threat , social physical distancing , public alarm , ( ) role different mass media channels lives individual , social societal levels ? Mass media long recognized powerful forces shaping experience world . recognition accompanied growing volume research , closely follows footsteps technological transformations ( e.g. radio , movies , television , internet , mobiles ) zeitgeist ( e.g. cold war , 9/11 , climate change ) attempt map mass media major impacts perceive , individuals citizens . media ( broadcast digital ) able convey sense unity reaching large audiences , messages lost noisy crowd mass self - communication ?'\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Method 1\n", "# Removing stopwords in nltk\n", "\n", "import nltk\n", "from nltk.corpus import stopwords\n", "my_stopwords=set(stopwords.words('english'))\n", "new_tokens=[]\n", "\n", "# Tokenization using word_tokenize()\n", "all_tokens=nltk.word_tokenize(text)\n", "\n", "for token in all_tokens:\n", "  if token not in my_stopwords:\n", "    new_tokens.append(token)\n", "\n", "\n", "\" \".join(new_tokens)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Method 2\n", "# Removing stopwords in spaCy\n", "\n", "import spacy\n", "nlp = spacy.load(\"en_core_web_sm\")\n", "doc=nlp(text)\n", "new_tokens=[]\n", "\n", "# Using is_stop attribute of each token to check if it's a stopword\n", "for token in doc:\n", "  if token.is_stop==False:\n", "    new_tokens.append(token.text)\n", "\n", "\" \".join(new_tokens)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 8. How to add custom stop words in spaCy ?\n", "Difficulty Level : L1\n", "\n", "<PERSON><PERSON> Add the custom stopwords “NIL” and “JUNK” in spaCy and remove the stopwords in below text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\" <PERSON> was a JUNK great guy NIL Adam was evil NIL Martha JUNK was more of a fool \""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Expected Output :\n", "```\n", "'Jonas great guy Adam evil Martha fool'\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import spacy\n", "nlp = spacy.load(\"en_core_web_sm\")\n", "\n", "# list of custom stop words\n", "customize_stop_words = ['NIL','JUNK']\n", "\n", "# Adding these stop words\n", "for w in customize_stop_words:\n", "    nlp.vocab[w].is_stop = True\n", "doc = nlp(text.strip())\n", "tokens = [token.text for token in doc if not token.is_stop]\n", "\n", "\" \".join(tokens)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 9. How to remove punctuations ?\n", "Difficulty Level : L1\n", "\n", "<PERSON>. Remove all the punctuations in the given text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"The match has concluded !!! India has won the match . Will we fin the finals too ? !\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "'The match has concluded India has won the match Will we fin the finals too'\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Method 1\n", "# Removing punctuations in spaCy\n", "import spacy\n", "nlp = spacy.load(\"en_core_web_sm\")\n", "\n", "doc=nlp(text)\n", "new_tokens=[]\n", "# Check if a token is a punctuation through is_punct attribute\n", "for token in doc:\n", "  if token.is_punct==False:\n", "    new_tokens.append(token.text)\n", "\n", "\" \".join(new_tokens)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Method 2\n", "# Removing punctuation in nltk with RegexpTokenizer\n", "import nltk\n", "tokenizer=nltk.RegexpTokenizer(r\"\\w+\")\n", "\n", "tokens=tokenizer.tokenize(text)\n", "\" \".join(tokens)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 10. How to perform stemming\n", "Difficulty Level : L2\n", "\n", "Q. Perform stemming/ convert each token to it’s root form in the given text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text= \"Dancing is an art. Students should be taught dance as a subject in schools . I danced in many of my school function. Some people are always hesitating to dance.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "'danc is an art . student should be taught danc as a subject in school . I danc in mani of my school function . some peopl are alway hesit to danc .'\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Stemming with nltk's <PERSON><PERSON><PERSON><PERSON>\n", "import nltk\n", "from nltk.stem import PorterStemmer\n", "\n", "stemmer=PorterStemmer()\n", "stemmed_tokens=[]\n", "for token in nltk.word_tokenize(text):\n", "  stemmed_tokens.append(stemmer.stem(token))\n", "\n", "\" \".join(stemmed_tokens)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 11. How to lemmatize a given text ?\n", "Difficulty Level : L2\n", "\n", "Q. Perform lemmatzation on the given text\n", "\n", "\n", "Hint: Lemmatization Approaches"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text= \"Dancing is an art. Students should be taught dance as a subject in schools . I danced in many of my school function. Some people are always hesitating to dance.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "'dancing be an art . student should be teach dance as a subject in school . -PRON- dance in many of -PRON- school function . some people be always hesitate to dance .'\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Lemmatization using spacy's lemma_ attribute of token\n", "import spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "doc=nlp(text)\n", "\n", "lemmatized=[token.lemma_ for token in doc]\n", "\" \".join(lemmatized)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 12. How to extract usernames from emails ?\n", "Difficulty Level : L2\n", "\n", "Q. Extract the usernames from the email addresses present in the text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text= \"The new <NAME_EMAIL> , <EMAIL>. If you find any disruptions, <NAME_EMAIL> or <EMAIL> \""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "['potter709', 'elixir101', 'granger111', 'severus77']\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Using regular expression to extract usernames\n", "import re  \n", "\n", "# \\S matches any non-whitespace character \n", "# @ for as in the Email \n", "# + for Repeats a character one or more times \n", "usernames= re.findall('(\\S+)@', text)     \n", "print(usernames) "]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 13. How to find the most common words in the text excluding stopwords\n", "Difficulty Level : L2\n", "\n", "Q. Extract the top 10 most common words in the given text excluding stopwords."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"\"\"Junkfood - Food that do no good to our body. And there's no need of them in our body but still we willingly eat them because they are great in taste and easy to cook or ready to eat. Junk foods have no or very less nutritional value and irrespective of the way they are marketed, they are not healthy to consume.The only reason of their gaining popularity and increased trend of consumption is \n", "that they are ready to eat or easy to cook foods. People, of all age groups are moving towards Junkfood as it is hassle free and often ready to grab and eat. Cold drinks, chips, noodles, pizza, burgers, French fries etc. are few examples from the great variety of junk food available in the market.\n", " Junkfood is the most dangerous food ever but it is pleasure in eating and it gives a great taste in mouth examples of Junkfood are kurkure and chips.. cold rings are also source of junk food... they shud nt be ate in high amounts as it results fatal to our body... it cn be eated in a limited extend ... in research its found tht ths junk foods r very dangerous fr our health\n", "Junkfood is very harmful that is slowly eating away the health of the present generation. The term itself denotes how dangerous it is for our bodies. Most importantly, it tastes so good that people consume it on a daily basis. However, not much awareness is spread about the harmful effects of Junkfood .\n", "The problem is more serious than you think. Various studies show that Junkfood impacts our health negatively. They contain higher levels of calories, fats, and sugar. On the contrary, they have very low amounts of healthy nutrients and lack dietary fibers. Parents must discourage their children from consuming junk food because of the ill effects it has on one’s health.\n", "Junkfood is the easiest way to gain unhealthy weight. The amount of fats and sugar in the food makes you gain weight rapidly. However, this is not a healthy weight. It is more of fats and cholesterol which will have a harmful impact on your health. Junk food is also one of the main reasons for the increase in obesity nowadays.\n", "This food only looks and tastes good, other than that, it has no positive points. The amount of calorie your body requires to stay fit is not fulfilled by this food. For instance, foods like French fries, burgers, candy, and cookies, all have high amounts of sugar and fats. Therefore, this can result in long-term illnesses like diabetes and high blood pressure. This may also result in kidney failure.\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "text= {Junkfood: 10,\n", " food: 8,\n", " good: 5,\n", " harmful : 3\n", " body: 1,\n", " need: 1,\n", "\n", " ...(truncated)\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Creating spacy doc of the text\n", "\n", "import spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "doc=nlp(text)\n", "\n", "# Removal of stop words and punctuations\n", "words=[str(token).strip() for token in doc if token.is_stop==False and token.is_punct==False]\n", "\n", "freq_dict={}\n", "\n", "# Calculating frequency count\n", "for word in words:\n", "  if word not in freq_dict:\n", "    freq_dict[word]=1\n", "  else:\n", "    freq_dict[word]+=1\n", "\n", "print(freq_dict)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 14. How to do spell correction in a given text ?\n", "Difficulty Level : L2\n", "\n", "<PERSON><PERSON> Correct the spelling errors in the following text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"He is a gret person. He beleives in bod\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "text=\"He is a great person. He believes in god\"\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import textblob\n", "from textblob import TextBlob\n", "import warnings\n", "warnings.filterwarnings(\"ignore\", category=DeprecationWarning) \n", "\n", "# Using textb<PERSON>b's correct() function\n", "text=TextBlob(text)\n", "print(text.correct())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 15. How to tokenize tweets ?\n", "Difficulty Level : L2\n", "\n", "Q. Clean the following tweet and tokenize them"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\" Having lots of fun #goa #vaction #summervacation. Fancy dinner @Beachbay restro :) \""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "['Having',\n", " 'lots',\n", " 'of',\n", " 'fun',\n", " 'goa',\n", " 'vaction',\n", " 'summervacation',\n", " 'Fancy',\n", " 'dinner',\n", " 'Beachbay',\n", " 'restro']\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "# Cleaning the tweets\n", "text=re.sub(r'[^\\w]', ' ', text)\n", "\n", "# Using nltk's TweetTokenizer\n", "from nltk.tokenize import TweetTokenizer\n", "tokenizer=TweetTokenizer()\n", "tokenizer.tokenize(text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 16. How to extract all the nouns in a text?\n", "Difficulty Level : L2\n", "\n", "Q. Extract and print all the nouns present in the below text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"<PERSON> works at Microsoft. She lives in manchester and likes to play the flute\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "<PERSON>\n", "Microsoft\n", "manchester\n", "flute\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Coverting the text into a spacy Doc\n", "import spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "doc=nlp(text)\n", "\n", "# Using spacy's pos_ attribute to check for part of speech tags\n", "for token in doc:\n", "  if token.pos_=='NOUN' or token.pos_=='PROPN':\n", "    print(token.text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 17. How to extract all the pronouns in a text?\n", "Difficulty Level : L2\n", "\n", "Q. Extract and print all the pronouns in the text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"<PERSON> is happy finally. He had landed his dream job finally. He told his mom. She was elated \""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", " He\n", " He\n", " She\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Using spacy's pos_ attribute to check for part of speech tags\n", "import spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "doc=nlp(text)\n", "\n", "for token in doc:\n", "  if token.pos_=='PRON':\n", "    print(token.text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 18. How to find similarity between two words?\n", "Difficulty Level : L2\n", "\n", "Find the similarity between any two words."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["word1=\"amazing\"\n", "word2=\"terrible\"\n", "word3=\"excellent\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "similarity between amazing and terrible is 0.46189071343764604\n", "similarity between amazing and excellent is 0.6388207086737778\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Convert words into spacy tokens\n", "import spacy\n", "nlp=spacy.load('en_core_web_lg')\n", "\n", "token1=nlp(word1)\n", "token2=nlp(word2)\n", "token3=nlp(word3)\n", "\n", "# Use similarity() function of tokens\n", "print('similarity between', word1,'and' ,word2, 'is' ,token1.similarity(token2))\n", "print('similarity between', word1,'and' ,word3, 'is' ,token1.similarity(token3))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 19. How to find similarity between two documents?\n", "Difficulty Level : L2\n", "\n", "Q. Find the similarity between any two text documents"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text1=\"<PERSON> lives in Canada\"\n", "text2=\"<PERSON> lives in America, though he's not from there\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "0.792817083631068\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Method 1\n", "# Finding similarity using spacy library\n", "import spacy\n", "import warnings\n", "warnings.filterwarnings(\"ignore\", category=UserWarning) \n", "\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "\n", "doc1=nlp(text1)\n", "doc2=nlp(text2)\n", "print(doc1.similarity(doc2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Method 2\n", "from nltk.corpus import wordnet\n", "\n", "list1 = text1.split(\" \")\n", "list2 = text2.split(\" \")\n", "\n", "lst = []\n", "for word1 in list1:\n", "    for word2 in list2:\n", "        wordFromList1 = wordnet.synsets(word1)\n", "        wordFromList2 = wordnet.synsets(word2)\n", "        if wordFromList1 and wordFromList2: #Thanks to @alexis' note\n", "            s = wordFromList1[0].wup_similarity(wordFromList2[0])\n", "            lst.append(s)\n", "            \n", "s = 0\n", "for i in lst:\n", "    s += i\n", "\n", "print(1- s/len(lst))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 20. How to find the cosine similarity of two documents?\n", "Difficulty Level : L3\n", "\n", "Q. Find the cosine similarity between two given documents"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text1='Taj Mahal is a tourist place in India'\n", "text2='Great Wall of China is a tourist place in china'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "[[1.         0.45584231]\n", " [0.45584231 1.        ]]\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Using Vectorizer of sklearn to get vector representation\n", "\n", "from sklearn.feature_extraction.text import CountVectorizer\n", "import pandas as pd\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "\n", "documents=[text1,text2]\n", "\n", "vectorizer=CountVectorizer()\n", "matrix=vectorizer.fit_transform(documents)\n", "\n", "# Obtaining the document-word matrix\n", "doc_term_matrix=matrix.todense()\n", "doc_term_matrix\n", "\n", "# Computing cosine similarity\n", "df=pd.DataFrame(doc_term_matrix)\n", "\n", "print(cosine_similarity(df,df))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 21. How to find soft cosine similarity of documents ?\n", "Difficulty Level : L3\n", "\n", "<PERSON><PERSON> Compute the soft cosine similarity of the given documents\n", "\n", "\n", "Hint: Soft Cosine Similarity"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["doc_soup = \"Soup is a primarily liquid food, generally served warm or hot (but may be cool or cold), that is made by combining ingredients of meat or vegetables with stock, juice, water, or another liquid. \"\n", "doc_noodles = \"Noodles are a staple food in many cultures. They are made from unleavened dough which is stretched, extruded, or rolled flat and cut into one of a variety of shapes.\"\n", "doc_dosa = \"Dosa is a type of pancake from the Indian subcontinent, made from a fermented batter. It is somewhat similar to a crepe in appearance. Its main ingredients are rice and black gram.\"\n", "doc_trump = \"Mr. <PERSON> became president after winning the political election. Though he lost the support of some republican friends, <PERSON> is friends with President <PERSON>\"\n", "doc_election = \"President <PERSON> says <PERSON> had no political interference is the election outcome. He says it was a witchhunt by political parties. He claimed President <PERSON> is a friend who had nothing to do with the election\"\n", "doc_putin = \"Post elections, <PERSON> became President of Russia. President <PERSON> had served as the Prime Minister earlier in his political career\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "0.5842470477718544\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings(\"ignore\", category=DeprecationWarning) \n", "\n", "# Import and download stopwords from NLTK.\n", "from nltk.corpus import stopwords\n", "from nltk import download\n", "download('stopwords')  # Download stopwords list.\n", "stop_words = stopwords.words('english')\n", "\n", "# Preprocess the sentences\n", "def preprocess(sentence):\n", "    return [w for w in sentence.lower().split() if w not in stop_words]\n", "\n", "doc_soup = preprocess(doc_soup)\n", "doc_noodles = preprocess(doc_noodles)\n", "doc_dosa = preprocess(doc_dosa)\n", "doc_trump = preprocess(doc_trump)\n", "doc_election = preprocess(doc_election)\n", "doc_putin = preprocess(doc_putin)\n", "\n", "# Build a dictionary and an TF-IDF model, convert the sentences to the bag-of-words format\n", "from gensim.corpora import Dictionary\n", "documents = [doc_soup, doc_noodles, doc_dosa, doc_trump, doc_election, doc_putin]\n", "dictionary = Dictionary(documents)\n", "\n", "doc_soup = dictionary.doc2bow(doc_soup)\n", "doc_noodles = dictionary.doc2bow(doc_noodles)\n", "doc_dosa = dictionary.doc2bow(doc_dosa)\n", "doc_trump = dictionary.doc2bow(doc_trump)\n", "doc_election = dictionary.doc2bow(doc_election)\n", "doc_putin = dictionary.doc2bow(doc_putin)\n", "\n", "from gensim.models import TfidfModel\n", "documents = [doc_soup, doc_noodles, doc_dosa, doc_trump, doc_election, doc_putin]\n", "tfidf = TfidfModel(documents)\n", "\n", "doc_soup = tfidf[doc_soup]\n", "doc_noodles = tfidf[doc_noodles]\n", "doc_dosa = tfidf[doc_dosa]\n", "doc_trump = tfidf[doc_trump]\n", "doc_election = tfidf[doc_election]\n", "doc_putin = tfidf[doc_putin]\n", "\n", "# Download the FastText model\n", "import gensim.downloader as api\n", "model = api.load('fasttext-wiki-news-subwords-300')\n", "# model = api.load('word2vec-google-news-300')\n", "\n", "# Prepare the similarity matrix\n", "from gensim.similarities import SparseTermSimilarityMatrix, WordEmbeddingSimilarityIndex\n", "termsim_index = WordEmbeddingSimilarityIndex(model)\n", "termsim_matrix = SparseTermSimilarityMatrix(termsim_index, dictionary, tfidf)\n", "\n", "# Compute SCM using the inner_product method\n", "similarity = termsim_matrix.inner_product(doc_soup, doc_noodles, normalized=(True, True))\n", "print('similarity = %f' % similarity)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare the soft cosines for all documents against each other\n", "import numpy as np\n", "import pandas as pd\n", "\n", "sentences = [doc_soup, doc_noodles, doc_dosa, doc_trump, doc_election, doc_putin]\n", "\n", "def create_soft_cossim_matrix(sentences):\n", "    len_array = np.arange(len(sentences))\n", "    xx, yy = np.meshgrid(len_array, len_array)\n", "    cossim_mat = pd.DataFrame([[round(termsim_matrix.inner_product(sentences[i],sentences[j], normalized=(True, True)), 2) for i, j in zip(x,y)] for y, x in zip(xx, yy)])\n", "    return cossim_mat\n", "\n", "create_soft_cossim_matrix(sentences)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 22. How to find similar words using pre-trained Word2Vec?\n", "Difficulty Level : L2\n", "\n", "Q. Find all similiar words to “amazing” using Google news Word2Vec."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "[('incredible', 0.90),\n", "('awesome', 0.82),\n", "('unbelievable', 0.82),\n", "('fantastic', 0.77),\n", "('phenomenal', 0.76),\n", "('astounding', 0.73),\n", "('wonderful', 0.72),\n", "('unbelieveable', 0.71),\n", "('remarkable', 0.70),\n", "('marvelous', 0.70)]\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings(\"ignore\", category=DeprecationWarning) \n", "\n", "# Import gensim api\n", "import gensim.downloader as api\n", "\n", "# Load the pretrained google news word2vec model\n", "word2vec_model300 = api.load('word2vec-google-news-300')\n", "\n", "# Using most_similar() function\n", "word2vec_model300.most_similar('amazing')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 23. How to compute Word mover distance?\n", "Difficulty Level : L3\n", "\n", "<PERSON><PERSON>mpute the word mover distance between given two texts"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sentence_orange = 'Oranges are my favorite fruit'\n", "sent=\"apples are not my favorite\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "5.378\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings(\"ignore\", category=DeprecationWarning) \n", "\n", "# Import and download stopwords from NLTK.\n", "from nltk.corpus import stopwords\n", "from nltk import download\n", "download('stopwords')  # Download stopwords list.\n", "stop_words = stopwords.words('english')\n", "\n", "def preprocess(sentence):\n", "    return [w for w in sentence.lower().split() if w not in stop_words]\n", "\n", "sentence_orange = preprocess(sentence_orange)\n", "sent = preprocess(sent)\n", "\n", "# Importing g<PERSON><PERSON>'s model\n", "import gensim.downloader as api\n", "model = api.load('word2vec-google-news-300')\n", "\n", "# Computing the word mover distance\n", "distance = model.wmdistance(sent, sentence_orange)\n", "print(distance)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 24. How to replace all the pronouns in a text with their respective object names\n", "Difficulty Level : L2\n", "\n", "<PERSON>. Replace the pronouns in below text by the respective object names"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\" My sister has a dog and she loves him\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "[My sister, she]\n", "[a dog, him ]\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # NOT WORKING\n", "# # Import neural coref library\n", "# # neuralcoref only works with spacy v2, specifically: spacy==2.1.0, neuralcoref==4.0\n", "# import spacy\n", "# import neuralcoref\n", "\n", "# # Add it to the pipeline\n", "# nlp = spacy.load('en')\n", "# neuralcoref.add_to_pipe(nlp)\n", "\n", "# # Printing the coreferences\n", "# doc1 = nlp('My sister has a dog. She loves him.')\n", "# print(doc1._.coref_clusters)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import spacy\n", "\n", "text = \"My sister has a dog and she loves him\" \n", "\n", "nlp = spacy.load('en_core_web_sm')\n", "doc = nlp(text)\n", "\n", "pronouns = []\n", "objects = []\n", "for token in doc:\n", "    if token.pos_ == \"PRON\" and token.head.pos_ != \"PRON\":\n", "        pronouns.append(token.text)\n", "        objects.append(token.head.text)\n", "    elif token.pos_ == \"PRON\" and token.head.pos_ == \"PRON\":  \n", "        pronouns.append(token.text)\n", "        objects.append(token.head.head.text)\n", "\n", "print(pronouns)\n", "print(objects)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 25. How to extract topic keywords using LSA?\n", "Difficulty Level : L3\n", "\n", "Q. Extract the topic keywords from the given texts using LSA(Latent Semantic Analysis )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["texts= [\"\"\"It's all about travel. I travel a lot.  those who do not travel read only a page.” – said <PERSON>. He was a great travel person. Travelling can teach you more than any university course. You learn about the culture of the country you visit. If you talk to locals, you will likely learn about their thinking, habits, traditions and history as well.If you travel, you will not only learn about foreign cultures, but about your own as well. You will notice the cultural differences, and will find out what makes your culture unique. After retrurning from a long journey, you will see your country with new eyes.\"\"\",\n", "        \"\"\" You can learn a lot about yourself through travelling. You can observe how you feel beeing far from your country. You will find out how you feel about your homeland.You should travel You will realise how you really feel about foreign people. You will find out how much you know/do not know about the world. You will be able to observe how you react in completely new situations. You will test your language, orientational and social skills. You will not be the same person after returning home.During travelling you will meet people that are very different from you. If you travel enough, you will learn to accept and appreciate these differences. Traveling makes you more open and accepting.\"\"\",\n", "        \"\"\"Some of my most cherished memories are from the times when I was travelling. If you travel, you can experience things that you could never experience at home. You may see beautiful places and landscapes that do not exist where you live. You may meet people that will change your life, and your thingking. You may try activities that you have never tried before.Travelling will inevitably make you more independent and confident. You will realise that you can cope with a lot of unexpected situations. You will realise that you can survive without all that help that is always available for you at home. You will likely find out that you are much stronger and braver than you have expected.\"\"\",\n", "        \"\"\"If you travel, you may learn a lot of useful things. These things can be anything from a new recepie, to a new, more effective solution to an ordinary problem or a new way of creating something.Even if you go to a country where they speak the same language as you, you may still learn some new words and expressions that are only used there. If you go to a country where they speak a different language, you will learn even more.\"\"\",\n", "        \"\"\"After arriving home from a long journey, a lot of travellers experience that they are much more motivated than they were before they left. During your trip you may learn things that you will want to try at home as well. You may want to test your new skills and knowledge. Your experiences will give you a lot of energy.During travelling you may experience the craziest, most exciting things, that will eventually become great stories that you can tell others. When you grow old and look back at your life and all your travel experiences, you will realise how much you have done in your life and your life was not in vain. It can provide you with happiness and satisfaction for the rest of your life.\"\"\",\n", "        \"\"\"The benefits of travel are not just a one-time thing: travel changes you physically and psychologically. Having little time or money isn't a valid excuse. You can travel for cheap very easily. If you have a full-time job and a family, you can still travel on the weekends or holidays, even with a baby. travel  more is likely to have a tremendous impact on your mental well-being, especially if you're no used to going out of your comfort zone. Trust me: travel more and your doctor will be happy. Be sure to get in touch with your physician, they might recommend some medication to accompany you in your travels, especially if you're heading to regions of the globe with potentially dangerous diseases.\"\"\",\n", "        \"\"\"Sure, you probably feel comfortable where you are, but that is just a fraction of the world! If you are a student, take advantage of programs such as Erasmus to get to know more people, experience and understand their culture. Dare traveling to regions you have a skeptical opinion about. I bet that you will change your mind and realize that everything is not so bad abroad.\"\"\",\n", "        \"\"\" So, travel makes you cherish life. Let's travel more . Share your travel diaries with us too\"\"\"\n", "        ]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "Topic 0: \n", "learn new life travelling country feel  \n", "Topic 1: \n", "life cherish diaries let share experience  \n", "Topic 2: \n", "feel know time people just regions  \n", "Topic 3: \n", "time especially cherish diaries let share  \n", "..(truncated)..\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Importing the Tf-idf vectorizer from sklearn\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "\n", "# Defining the vectorizer\n", "vectorizer = TfidfVectorizer(stop_words='english', max_features= 1000,  max_df = 0.5, smooth_idf=True)\n", "\n", "# Transforming the tokens into the matrix form through .fit_transform()\n", "matrix= vectorizer.fit_transform(texts)\n", "\n", "# SVD represent documents and terms in vectors\n", "from sklearn.decomposition import TruncatedSVD\n", "SVD_model = TruncatedSVD(n_components=10, algorithm='randomized', n_iter=100, random_state=122)\n", "SVD_model.fit(matrix)\n", "\n", "# Getting the terms \n", "terms = vectorizer.get_feature_names_out()\n", "\n", "# Iterating through each topic\n", "for i, comp in enumerate(SVD_model.components_):\n", "    terms_comp = zip(terms, comp)\n", "    # sorting the 7 most important terms\n", "    sorted_terms = sorted(terms_comp, key= lambda x:x[1], reverse=True)[:7]\n", "    print(\"Topic \"+str(i)+\": \")\n", "    # printing the terms of a topic\n", "    for t in sorted_terms:\n", "        print(t[0],end=' ')\n", "    print(' ')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 26. How to extract topic Keywords using LDA ?\n", "Difficulty Level : L3\n", "\n", "Q. Extract the the topics from the given texts with the help of LDA(Latent dirichlet algorithm)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["texts= [\"\"\"It's all about travel. I travel a lot.  those who do not travel read only a page.” – said <PERSON>. He was a great travel person. Travelling can teach you more than any university course. You learn about the culture of the country you visit. If you talk to locals, you will likely learn about their thinking, habits, traditions and history as well.If you travel, you will not only learn about foreign cultures, but about your own as well. You will notice the cultural differences, and will find out what makes your culture unique. After retrurning from a long journey, you will see your country with new eyes.\"\"\",\n", "        \"\"\" You can learn a lot about yourself through travelling. You can observe how you feel beeing far from your country. You will find out how you feel about your homeland.You should travel You will realise how you really feel about foreign people. You will find out how much you know/do not know about the world. You will be able to observe how you react in completely new situations. You will test your language, orientational and social skills. You will not be the same person after returning home.During travelling you will meet people that are very different from you. If you travel enough, you will learn to accept and appreciate these differences. Traveling makes you more open and accepting.\"\"\",\n", "        \"\"\"Some of my most cherished memories are from the times when I was travelling. If you travel, you can experience things that you could never experience at home. You may see beautiful places and landscapes that do not exist where you live. You may meet people that will change your life, and your thingking. You may try activities that you have never tried before.Travelling will inevitably make you more independent and confident. You will realise that you can cope with a lot of unexpected situations. You will realise that you can survive without all that help that is always available for you at home. You will likely find out that you are much stronger and braver than you have expected.\"\"\",\n", "        \"\"\"If you travel, you may learn a lot of useful things. These things can be anything from a new recepie, to a new, more effective solution to an ordinary problem or a new way of creating something.Even if you go to a country where they speak the same language as you, you may still learn some new words and expressions that are only used there. If you go to a country where they speak a different language, you will learn even more.\"\"\",\n", "        \"\"\"After arriving home from a long journey, a lot of travellers experience that they are much more motivated than they were before they left. During your trip you may learn things that you will want to try at home as well. You may want to test your new skills and knowledge. Your experiences will give you a lot of energy.During travelling you may experience the craziest, most exciting things, that will eventually become great stories that you can tell others. When you grow old and look back at your life and all your travel experiences, you will realise how much you have done in your life and your life was not in vain. It can provide you with happiness and satisfaction for the rest of your life.\"\"\",\n", "        \"\"\"The benefits of travel are not just a one-time thing: travel changes you physically and psychologically. Having little time or money isn't a valid excuse. You can travel for cheap very easily. If you have a full-time job and a family, you can still travel on the weekends or holidays, even with a baby. travel  more is likely to have a tremendous impact on your mental well-being, especially if you're no used to going out of your comfort zone. Trust me: travel more and your doctor will be happy. Be sure to get in touch with your physician, they might recommend some medication to accompany you in your travels, especially if you're heading to regions of the globe with potentially dangerous diseases.\"\"\",\n", "        \"\"\"Sure, you probably feel comfortable where you are, but that is just a fraction of the world! If you are a student, take advantage of programs such as Erasmus to get to know more people, experience and understand their culture. Dare traveling to regions you have a skeptical opinion about. I bet that you will change your mind and realize that everything is not so bad abroad.\"\"\",\n", "        \"\"\" So, travel makes you cherish life. Let's travel more . Share your travel diaries with us too\"\"\"\n", "        ]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "[(0, '0.068*\"travel\" + 0.044*\"learn\" + 0.027*\"country\" + 0.027*\"If\" + 0.026*\"find\"'), (1, '0.054*\"may\" + 0.036*\"realise\" + 0.036*\"home\" + 0.036*\"experience\" + 0.036*\"never\"'), (2, '0.047*\"may\" + 0.044*\"life\" + 0.039*\"new\" + 0.036*\"things\" + 0.032*\"learn\"'), (3, '0.031*\"If\" + 0.031*\"people\" + 0.031*\"I\" + 0.031*\"world\" + 0.031*\"know\"'), (4, '0.085*\"travel\" + 0.042*\"\\'\" + 0.042*\"-\" + 0.042*\"time\" + 0.028*\"especially\"')]\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "warnings.simplefilter('ignore')\n", "\n", "# Import gensim, nltk\n", "from gensim import models, corpora\n", "import nltk\n", "from nltk.corpus import stopwords\n", "\n", "# Before topic extraction, we remove punctuations and stopwords.\n", "my_stopwords=set(stopwords.words('english'))\n", "punctuations=['.','!',',',\"You\",\"I\"]\n", "\n", "# We prepare a list containing lists of tokens of each text\n", "all_tokens=[]\n", "for text in texts:\n", "  tokens=[]\n", "  raw=nltk.wordpunct_tokenize(text)\n", "  for token in raw:\n", "    if token not in my_stopwords:\n", "      if token not in punctuations:\n", "        tokens.append(token)\n", "        all_tokens.append(tokens)\n", "\n", "# Creating a gensim dictionary and the matrix\n", "dictionary = corpora.Dictionary(all_tokens)\n", "doc_term_matrix = [dictionary.doc2bow(doc) for doc in all_tokens]\n", "\n", "# Building the model and training it with the matrix \n", "from gensim.models.ldamodel import LdaModel\n", "model = LdaModel(doc_term_matrix, num_topics=5, id2word = dictionary,passes=40)\n", "\n", "print(model.print_topics(num_topics=6,num_words=5))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 27. How to extract topic keywords using NMF?\n", "Difficulty Level : L3\n", "\n", "Q. Extract the the topics from the given texts with the help of NMF(Non-negative Matrix Factorization method)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["texts= [\"\"\"It's all about travel. I travel a lot.  those who do not travel read only a page.” – said <PERSON>. He was a great travel person. Travelling can teach you more than any university course. You learn about the culture of the country you visit. If you talk to locals, you will likely learn about their thinking, habits, traditions and history as well.If you travel, you will not only learn about foreign cultures, but about your own as well. You will notice the cultural differences, and will find out what makes your culture unique. After retrurning from a long journey, you will see your country with new eyes.\"\"\",\n", "        \"\"\" You can learn a lot about yourself through travelling. You can observe how you feel beeing far from your country. You will find out how you feel about your homeland.You should travel You will realise how you really feel about foreign people. You will find out how much you know/do not know about the world. You will be able to observe how you react in completely new situations. You will test your language, orientational and social skills. You will not be the same person after returning home.During travelling you will meet people that are very different from you. If you travel enough, you will learn to accept and appreciate these differences. Traveling makes you more open and accepting.\"\"\",\n", "        \"\"\"Some of my most cherished memories are from the times when I was travelling. If you travel, you can experience things that you could never experience at home. You may see beautiful places and landscapes that do not exist where you live. You may meet people that will change your life, and your thingking. You may try activities that you have never tried before.Travelling will inevitably make you more independent and confident. You will realise that you can cope with a lot of unexpected situations. You will realise that you can survive without all that help that is always available for you at home. You will likely find out that you are much stronger and braver than you have expected.\"\"\",\n", "        \"\"\"If you travel, you may learn a lot of useful things. These things can be anything from a new recepie, to a new, more effective solution to an ordinary problem or a new way of creating something.Even if you go to a country where they speak the same language as you, you may still learn some new words and expressions that are only used there. If you go to a country where they speak a different language, you will learn even more.\"\"\",\n", "        \"\"\"After arriving home from a long journey, a lot of travellers experience that they are much more motivated than they were before they left. During your trip you may learn things that you will want to try at home as well. You may want to test your new skills and knowledge. Your experiences will give you a lot of energy.During travelling you may experience the craziest, most exciting things, that will eventually become great stories that you can tell others. When you grow old and look back at your life and all your travel experiences, you will realise how much you have done in your life and your life was not in vain. It can provide you with happiness and satisfaction for the rest of your life.\"\"\",\n", "        \"\"\"The benefits of travel are not just a one-time thing: travel changes you physically and psychologically. Having little time or money isn't a valid excuse. You can travel for cheap very easily. If you have a full-time job and a family, you can still travel on the weekends or holidays, even with a baby. travel  more is likely to have a tremendous impact on your mental well-being, especially if you're no used to going out of your comfort zone. Trust me: travel more and your doctor will be happy. Be sure to get in touch with your physician, they might recommend some medication to accompany you in your travels, especially if you're heading to regions of the globe with potentially dangerous diseases.\"\"\",\n", "        \"\"\"Sure, you probably feel comfortable where you are, but that is just a fraction of the world! If you are a student, take advantage of programs such as Erasmus to get to know more people, experience and understand their culture. Dare traveling to regions you have a skeptical opinion about. I bet that you will change your mind and realize that everything is not so bad abroad.\"\"\",\n", "        \"\"\" So, travel makes you cherish life. Let's travel more . Share your travel diaries with us too\"\"\"\n", "        ]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", " Topic 0:\n", " [('new', 0.6329770846997606), ('learn', 0.49810389825931783), ('speak', 0.47477546214544547), ('language', 0.43443029670471806), ('country', 0.36653909845383115), ('things', 0.3433223730439043)]\n", " Topic 1:\n", " [('life', 0.34063551920788737), ('home', 0.31402014643240667), ('experience', 0.3025841622571281), ('realise', 0.24642870225288288), ('travelling', 0.2180915553025073), ('things', 0.2076347895889257)]\n", " Topic 2:\n", " [('feel', 0.3462484013922396), ('know', 0.28400088182008115), ('people', 0.2431266883545085), ('world', 0.22169277349692076), ('traveling', 0.22169277349692076), ('bet', 0.18671974365540467)]\n", "...(truncated)\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.feature_extraction.text import TfidfVectorizer\n", "\n", "# Defining the vectorizer\n", "vectorizer = TfidfVectorizer(stop_words='english', max_features= 1000,  max_df = 0.5, smooth_idf=True)\n", "\n", "# Transforming the tokens into the matrix form through .fit_transform()\n", "nmf_matrix= vectorizer.fit_transform(texts)\n", "\n", "from sklearn.decomposition import NMF\n", "nmf_model = NMF(n_components=6)\n", "nmf_model.fit(nmf_matrix)\n", "\n", "# Function to print topics\n", "def print_topics_nmf(model, vectorizer, top_n=6):\n", "    for idx, topic in enumerate(model.components_):\n", "        print(\"Topic %d:\" % (idx))\n", "        print([(vectorizer.get_feature_names_out()[i], topic[i])\n", "                        for i in topic.argsort()[:-top_n - 1:-1]])\n", "        \n", "print_topics_nmf(nmf_model,vectorizer)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 28. How to classify a text as positive/negative sentiment\n", "Difficulty Level : L2\n", "\n", "<PERSON>. Detect if a text is positive or negative sentiment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"It was a very pleasant day\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "Sentiment(polarity=0.9533333333333333, subjectivity=1.0)\n", "Positive\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sentiment analysis with TextBlob\n", "from textblob import TextBlob\n", "blob=TextBlob(text)\n", "\n", "# Using the sentiment attribute \n", "print(blob.sentiment)\n", "if(blob.sentiment.polarity > 0):\n", "  print(\"Positive\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 29. How to use the Word2Vec model for representing words?\n", "Difficulty Level : L2\n", "\n", "Q. Extract the word vector representation of the word using word2vec model"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["texts= [\" Photography is an excellent hobby to pursue \",\n", "        \" Photographers usually develop patience, calmnesss\",\n", "        \" You can try Photography with any good mobile too\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "array([ 2.94046826e-03, -1.31368915e-05, -3.43682081e-03, -3.73885059e-03,\n", "        2.49790819e-03, -1.23431312e-03, -9.60227044e-04,  2.31345627e-03,\n", "       -4.97973803e-03,  2.09524506e-03,  2.00997619e-03, -4.10459843e-03,\n", "        8.42132606e-04, -2.70003616e-03,  3.12150107e-03,  1.23607670e-03,\n", "        2.16376456e-03,  5.02903073e-04, -3.72780557e-03,  4.35266597e-03,\n", "       -1.80016900e-03,  3.42973252e-03, -2.12087762e-03,  1.14531387e-04,\n", "        3.03449039e-03, -8.75897415e-04, -3.50620854e-03,  5.10322629e-03,\n", "        ...(truncated)\n", "Positive\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# We prepare a list containing lists of tokens of each text\n", "import nltk\n", "\n", "all_tokens = []\n", "for text in texts:\n", "  tokens = []\n", "  raw = nltk.tokenize.wordpunct_tokenize(text.strip())\n", "  for token in raw:\n", "    tokens.append(token)\n", "  all_tokens.append(tokens)\n", "\n", "# Import and fit the model with data\n", "import gensim\n", "from gensim.models import Word2Vec\n", "\n", "model = Word2Vec(all_tokens, min_count=1)\n", "\n", "# Getting the vector representation of a word\n", "model.wv['Photography']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 30. How to visualize the word embedding obtained from word2Vec model ?\n", "Difficulty Level : L4\n", "\n", "Q. Implement Word embedding on the given texts and visualize it"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["texts= [\" Photography is an excellent hobby to pursue \",\n", "        \" Photographers usually develop patience, calmnesss\",\n", "        \" You can try Photography with any good mobile too\"]"]}, {"attachments": {"image.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["Desire output:\n", "\n", "![image.png](attachment:image.png)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import nltk\n", "# We prepare a list containing lists of tokens of each text\n", "all_tokens=[]\n", "for text in texts:\n", "  tokens=[]\n", "  raw=nltk.wordpunct_tokenize(text.strip())\n", "  for token in raw:\n", "    tokens.append(token)\n", "  all_tokens.append(tokens)\n", "\n", "# Import and fit the model with data\n", "from gensim.models import Word2Vec\n", "model=Word2Vec(all_tokens, min_count=1)\n", "\n", "# Visualizing the word embedding\n", "from sklearn.decomposition import PCA\n", "from matplotlib import pyplot\n", "\n", "col = ['col' + str(i) for i in range(len(model.wv[0]))]\n", "import pandas as pd\n", "import numpy as np\n", "\n", "X = pd.DataFrame([], columns=col)\n", "for idx in range(len(model.wv)):\n", "  X.loc[idx] = model.wv[idx]\n", "\n", "# PCA down to 2D\n", "pca = PCA(n_components=2)\n", "result = pca.fit_transform(X)\n", "\n", "# create a scatter plot of the projection\n", "pyplot.scatter(result[:, 0], result[:, 1])\n", "words = list(set(sum(all_tokens, [])))\n", "for i, word in enumerate(words):\n", "\tpyplot.annotate(word, xy=(result[i, 0], result[i, 1]))\n", "pyplot.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from gensim.models import Word2Vec"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 31. How to represent the document using Doc2Vec model?\n", "Difficulty Level : L2\n", "\n", "Q. Represent a text document in the form a vector"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["texts= [\" Photography is an excellent hobby to pursue \",\n", "        \" Photographers usually develop patience, calmnesss\"\n", "        \" You can try Photography with any good mobile too\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "array([ 2.6586275e-03,  3.2867077e-03, -2.0473711e-03,  6.0251489e-04,\n", "       -1.5340233e-03,  1.5060971e-03,  1.0988972e-03,  1.0712545e-03,\n", "       -4.3745534e-03, -4.0448168e-03, -1.8953394e-04, -2.0953947e-04,\n", "       -3.3285557e-03,  1.0409033e-03, -8.5728493e-04,  4.5999791e-03,\n", "        ...(truncated)..\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Importing the model\n", "import gensim\n", "from gensim.models import Doc2Vec\n", "\n", "# Preparing data in the format and fitting to the model\n", "all_tokens=[]\n", "for text in texts:\n", "   tokens=[]\n", "   raw=nltk.wordpunct_tokenize(text.strip())\n", "   for token in raw:\n", "      tokens.append(token)\n", "   all_tokens.append(tokens)\n", "\n", "def tagged_document(list_of_list_of_words):\n", "   for i, list_of_words in enumerate(list_of_list_of_words):\n", "      yield gensim.models.doc2vec.TaggedDocument(list_of_words, [i])\n", "\n", "my_data = list(tagged_document(all_tokens))\n", "model=Doc2Vec(my_data, min_count=1)\n", "\n", "model.infer_vector(['photography','is','an',' excellent ','hobby ','to',' pursue '])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 32. How to extract the TF-IDF Matrix ?\n", "Difficulty Level : L3\n", "\n", "Q. Extract the TF-IDF (Term Frequency -Inverse Document Frequency) Matrix for the given list of text documents"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text_documents=['Painting is a hobby for many , passion for some',\n", "                'My hobby is coin collection'\n", "                'I do some Painting every now and then']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "(0, 13)\t0.2511643891128359\n", "(0, 12)\t0.35300278529739293\n", "(0, 8)\t0.35300278529739293\n", "(0, 5)\t0.7060055705947859\n", "(0, 6)\t0.2511643891128359\n", "(0, 7)\t0.2511643891128359\n", "...(truncated)..\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Using s<PERSON><PERSON><PERSON>'s TfidfVectorizer\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "\n", "# Fit the vectorizer to our text documents\n", "vectorizer = TfidfVectorizer()\n", "matrix = vectorizer.fit_transform(text_documents)\n", "print(matrix)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 33. How to create bigrams using Gensim’s Phraser ?\n", "Difficulty Level : L3\n", "\n", "Q. Create bigrams from the given texts using Gensim library’s Phrases"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["documents = [\"the mayor of new york was there\", \"new york mayor was present\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "['the', 'mayor', 'of', 'new york', 'was', 'there']\n", "['new york', 'mayor', 'was', 'present']\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# I<PERSON>rt Phraser from gensim\n", "from gensim.models import Phrases\n", "from gensim.models.phrases import Phraser\n", "\n", "sentence_stream = [doc.split(\" \") for doc in documents]\n", "\n", "# Creating bigram phraser\n", "bigram = Phrases(sentence_stream, min_count=1, threshold=2, delimiter=\" \")\n", "bigram_phraser = Phraser(bigram)\n", "\n", "for sent in sentence_stream:\n", "    tokens_ = bigram_phraser[sent]\n", "    print(tokens_)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 34. How to create bigrams, trigrams using ngrams ?\n", "Difficulty Level : L3\n", "\n", "Q. Extract all bigrams , trigrams using ngrams of nltk library"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Sentences=\"Machine learning is a neccessary field in today's world. Data science can do wonders . Natural Language Processing is how machines understand text \""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "Bigrams are [('machine', 'learning'), ('learning', 'is'), ('is', 'a'), ('a', 'neccessary'), ('neccessary', 'field'), ('field', 'in'), ('in', \"today's\"), (\"today's\", 'world.'), ('world.', 'data'), ('data', 'science'), ('science', 'can'), ('can', 'do'), ('do', 'wonders'), ('wonders', '.'), ('.', 'natural'), ('natural', 'language'), ('language', 'processing'), ('processing', 'is'), ('is', 'how'), ('how', 'machines'), ('machines', 'understand'), ('understand', 'text')]\n", " Trigrams are [('machine', 'learning', 'is'), ('learning', 'is', 'a'), ('is', 'a', 'neccessary'), ('a', 'neccessary', 'field'), ('neccessary', 'field', 'in'), ('field', 'in', \"today's\"), ('in', \"today's\", 'world.'), (\"today's\", 'world.', 'data'), ('world.', 'data', 'science'), ('data', 'science', 'can'), ('science', 'can', 'do'), ('can', 'do', 'wonders'), ('do', 'wonders', '.'), ('wonders', '.', 'natural'), ('.', 'natural', 'language'), ('natural', 'language', 'processing'), ('language', 'processing', 'is'), ('processing', 'is', 'how'), ('is', 'how', 'machines'), ('how', 'machines', 'understand'), ('machines', 'understand', 'text')]\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from nltk import ngrams\n", "bigram=list(ngrams(Sentences.lower().split(),2))\n", "trigram=list(ngrams(Sentences.lower().split(),3))\n", "\n", "print(\" Bigrams are\",bigram)\n", "print(\" Trigrams are\", trigram)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 35. How to detect the language of entered text ?\n", "Difficulty Level : L1\n", "\n", "Q. Find out the language of the given text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"El agente imprime su pase de abordaje. Los oficiales de seguridad del aeropuerto pasan junto a él con un perro grande. El perro está olfateando alrededor del equipaje de las personas tratando de detectar drogas o explosivos.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "{'language': 'es', 'score': 0.9999963653206719}\n", " El agente imprime su pase de abordaje. {'language': 'es', 'score': 0.9999969081229643} \n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install spacy's languagedetect library\n", "import spacy\n", "from spacy_langdetect import LanguageDetector\n", "from spacy.language import Language\n", "nlp = spacy.load('en_core_web_sm')\n", "\n", "@Language.factory('language_detector')\n", "def language_detector(nlp, name):\n", "   return LanguageDetector()\n", "\n", "# Add the language detector to the processing pipeline\n", "nlp.add_pipe('language_detector', last=True)\n", "\n", "doc = nlp(text)\n", "# document level language detection. Think of it like average language of the document!\n", "print(doc._.language)\n", "# sentence level language detection\n", "for sent in doc.sents:\n", "   print(sent, sent._.language)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 36. How to merge two tokens as one ?\n", "Difficulty Level : L3\n", "\n", "<PERSON><PERSON>rge the first name and last name as single token in the given sentence"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"<PERSON> is a famous character in various books and movies \""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "<PERSON>\n", "is\n", "a\n", "famous\n", "character\n", "in\n", "various\n", "books\n", "and\n", "movies\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import spacy\n", "nlp = spacy.load('en_core_web_sm')\n", "\n", "# Using retokenize() method of Doc object to merge two tokens\n", "doc = nlp(text)\n", "with doc.retok<PERSON><PERSON>() as retokenizer:\n", "    retokenizer.merge(doc[0:2])\n", "\n", "for token in doc:\n", "  print(token.text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 37. How to extract Noun phrases from a text ?\n", "Difficulty Level : L2\n", "\n", "Q. Extract and print the noun phrases in given text document"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"There is a empty house on the Elm Street\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Expected Output :\n", "```\n", "[a empty house, the Elm Street]\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import spacy\n", "nlp = spacy.load('en_core_web_sm')\n", "\n", "# Create a spacy doc of the text\n", "doc = nlp(text)\n", "\n", "# Use `noun_chunks` attribute to extract the Noun phrases\n", "chunks = list(doc.noun_chunks)\n", "chunks"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 38. How to extract Verb phrases from the text ?\n", "Difficulty Level : L3\n", "\n", "Q. Extract the Verb Phrases from the given text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=(\"I may bake a cake for my birthday. The talk will introduce reader about Use of baking\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", "may bake\n", "will introduce\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # NOT WORKING\n", "# # Import textacy library\n", "# import textacy\n", "\n", "# # Regex pattern to identify verb phrase\n", "# pattern = r'<VERB>?<ADV>*<VERB>+'\n", "# doc = textacy.make_spacy_doc(text, lang='en_core_web_sm')\n", "\n", "# # Finding matches\n", "# verb_phrases = [_ for _ in textacy.extract.matches.regex_matches(doc, pattern)]\n", "\n", "# # Print all Verb Phrase\n", "# for chunk in verb_phrases:\n", "#   print(chunk.text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import spacy\n", "nlp = spacy.load('en_core_web_sm')\n", "doc = nlp(text)\n", "\n", "verb_phrases = []\n", "tmp = []\n", "for token in doc:\n", "    if token.pos_ in [\"VERB\", \"AUX\"]:\n", "        tmp.append(token.text)\n", "    else:\n", "        if len(tmp) != 0:\n", "            verb_phrases.append(\" \".join(tmp))\n", "            tmp = []\n", "for ele in verb_phrases:\n", "    print(ele)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 39. How to extract first name and last names present in the document ?\n", "Difficulty Level : L3\n", "\n", "Q. Extract any two consecutive Proper Nouns that occour in the text document"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"<PERSON> and <PERSON> were good friends. I am a fan of <PERSON>\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", " <PERSON>\n", " <PERSON>\n", " <PERSON>\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import and initialize spacy's matcher\n", "import spacy\n", "from spacy.matcher import Matcher\n", "\n", "nlp = spacy.load(\"en_core_web_sm\")\n", "matcher = Matcher(nlp.vocab)\n", "\n", "pattern = [{'POS': 'PROPN'}, {'POS': 'PROPN'}]\n", "matcher.add('FULL_NAME', [pattern])\n", "\n", "doc=nlp(text)\n", "matches = matcher(doc)\n", "for match_id, start, end in matches:\n", "  span = doc[start:end]\n", "  print(span.text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 40. How to identify named entities in the given text\n", "Difficulty Level : L2\n", "\n", "Q. Identify and print all the named entities with their labels in the below text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\" <PERSON> works at Google. He lives in London.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output:\n", "```\n", " <PERSON>\n", " Google ORG\n", " London GPE\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import spacy\n", "\n", "# Load spacy modelimport spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "doc=nlp(text)\n", "# Using the ents attribute of doc, identify labels\n", "for entity in doc.ents:  \n", "   print(entity.text,entity.label_)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 41. How to identify all the names of Organizations present in the text with NER ?\n", "Difficulty Level : L2\n", "\n", "Q. Identify and extract a list of all organizations/Companies mentioned in the given news article"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text =\" Google has released it's new model which has got attention of everyone. Amazon is planning to expand into Food delivery, thereby giving competition . Apple is coming up with new iphone model. Flipkart will have to catch up soon.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Expected Solution\n", "```\n", "['Google', 'Amazon', 'Apple', 'Flipkart']\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "doc=nlp(text.strip())\n", "list_of_org=[]\n", "for entity in doc.ents:\n", "  if entity.label_==\"ORG\":\n", "    list_of_org.append(entity.text)\n", "\n", "print(list_of_org)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 42. How to replace all names of people in the text with ‘UNKNOWN’\n", "Difficulty Level : L3\n", "\n", "<PERSON>. Identify and replace all the person names in the news article with UNKNOWN"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["news=\" <PERSON> was arrested yesterday at Brooklyn for murder. The suspicions and fingerprints pointed to <PERSON>  and his friend  <PERSON><PERSON> . The arrest was made by inspector <PERSON>\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "'  <PERSON><PERSON><PERSON><PERSON><PERSON> was arrested yesterday at Brooklyn for murder . The suspicions and fingerprints pointed to <PERSON><PERSON><PERSON><PERSON><PERSON>   and his friend   <PERSON><PERSON><PERSON><PERSON><PERSON> . The arrest was made by inspector <PERSON><PERSON><PERSON><PERSON><PERSON>'\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "doc=nlp(news)\n", "\n", "# Identifying the entities of category 'PERSON'\n", "entities = [entity.text  for entity in doc.ents  if entity.label_=='PERSON']\n", "updated_text=[]\n", "\n", "for token in doc:\n", "  if token.text in entities:\n", "    updated_text.append(\"UNKNOWN\")\n", "  else :\n", "    updated_text.append(token.text)\n", "\n", "\" \".join(updated_text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 43. How to visualize the named entities using spaCy\n", "Difficulty Level : L2\n", "\n", "Q. Display the named entities prsent in the given document along with their categories using spacy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\" <PERSON> was arrested yesterday at Brooklyn for murder. The suspicions and fingerprints pointed to <PERSON>  and his friend  <PERSON><PERSON> . He is from Paris \""]}, {"attachments": {"image.png": {"image/png": "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***************************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"}}, "cell_type": "markdown", "metadata": {}, "source": ["Desired output:\n", "\n", "![image.png](attachment:image.png)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Use spacy's displacy with the parameter style=\"ent\"\n", "import spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "from spacy import displacy\n", "doc=nlp(text)\n", "displacy.render(doc,style='ent',jupyter=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 44. How to implement dependency parsing ?\n", "Difficulty Level : L2\n", "\n", "Q. Find the dependencies of all the words in the given text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"<PERSON> plays volleyball every evening.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", " <PERSON>\n", " plays ROOT\n", " volleyball dobj\n", " every det\n", " evening npadvmod\n", " . punct\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "# Using dep_ attribute od tokens in spaCy to access the dependency of the word in sentence.\n", "doc=nlp(text)\n", "\n", "for token in doc:\n", "  print(token.text,token.dep_)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 45. How to find the ROOT word of any word in a sentence?\n", "Difficulty Level : L3\n", "\n", "<PERSON>. Find and print the root word / headword of any word in the given sentence"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"<PERSON> plays volleyball. <PERSON> is not into sports, he paints a lot\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", " <PERSON> plays\n", " plays plays\n", " volleyball plays\n", " . plays\n", " Sam is\n", " is paints\n", " not is\n", " into is\n", " sports into\n", " , paints\n", " he paints\n", " paints paints\n", " a lot\n", " lot paints\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "# use the head attribute of tokens to find it's rootword\n", "doc=nlp(text)\n", "for token in doc:\n", "  print(token.text,token.head)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 46. How to visualize the dependency tree in spaCy\n", "Difficulty Level : L2\n", "\n", "Q. Visualize the dependencies of various tokens of the given text using spaCy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"<PERSON> plays volleyball. <PERSON> is not into sports, he paints a lot\""]}, {"attachments": {"image.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["Expected Output:\n", "\n", "![image.png](attachment:image.png)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "# Use spacy's displacy with the parameter style=\"dep\"\n", "doc=nlp(text)\n", "\n", "from spacy import displacy\n", "displacy.render(doc,style='dep',jupyter=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 47. How to detect all the Laptop names present in the text ?\n", "Difficulty Level : L4\n", "\n", "<PERSON>. Detect all the Laptop names present in the given document ."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text=\"For my offical use, I prefer lenova. For gaming purposes, I love asus\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Expected Output:\n", "```\n", "lenova laptop\n", "asus laptop\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import EntityRuler of spacy model\n", "import spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "\n", "# Functions to create patterns of laptop name to match\n", "def create_versioned(name):\n", "    return [\n", "        [{'LOWER': name}], \n", "        [{'LOWER': {'REGEX': f'({name}\\d+\\.?\\d*.?\\d*)'}}], \n", "        [{'LOWER': name}, {'TEXT': {'REGEX': '(\\d+\\.?\\d*.?\\d*)'}}]]\n", "\n", "def create_patterns():\n", "    versioned_languages = ['dell', 'HP', 'asus','msi','Apple','HCL','sony','samsung','lenova','acer']\n", "    flatten = lambda l: [item for sublist in l for item in sublist]\n", "    versioned_patterns = flatten([create_versioned(lang) for lang in versioned_languages])\n", "\n", "    lang_patterns = [\n", "        [{'LOWER': 'dell'}, {'LIKE_NUM': True}],\n", "        [{'LOWER': 'HP'}],\n", "        [{'LOWER': 'asus'}, {'LOWER': '#'}],\n", "        [{'LOWER': 'msi'}, {'LOWER': 'sharp'}],\n", "        [{'LOWER': 'Apple'}],\n", "        [{'LOWER': 'HCL'}, {'LOWER': '#'}],\n", "        [{'LOWER': 'sony'}],\n", "        [{'LOWER': 'samsung'}],\n", "        [{'LOWER': 'toshiba'}],\n", "        [{'LOWER': 'dell'},{'LOWER': 'inspiron'}],\n", "        [{'LOWER': 'acer'},{'IS_PUNCT': True, 'OP': '?'},{'LOWER': 'c'}],\n", "        [{'LOWER': 'golang'}],\n", "        [{'LOWER': 'lenova'}],\n", "        [{'LOWER': 'HP'},{'LOWER':'gaming'}],\n", "        [{'LOWER': 'Fujitsu'}],\n", "        [{'LOWER': 'micromax'}],\n", "    ]\n", "\n", "    return versioned_patterns + lang_patterns\n", "\n", "# Add the Entity Ruler to the pipeline\n", "from spacy.pipeline import EntityRuler\n", "ruler = nlp.add_pipe(\"entity_ruler\")\n", "ruler.add_patterns([{'label':'laptop','pattern':p} for p in create_patterns()])\n", "\n", "# Identify the car names now\n", "doc=nlp(\"For my offical use, I prefer lenova. For gaming purposes, I love asus\")\n", "for ent in doc.ents:\n", "  print(ent.text,ent.label_)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 48. How to summarize text using gensim ?\n", "Difficulty Level : L3\n", "\n", "<PERSON>. Extract the summary of the given text based using gensim package based on the TextRank Algorithm."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["original_text=\"\"\"Studies show that exercise can treat mild to moderate depression as effectively as antidepressant medication—but without the side-effects, of course. As one example, a recent study done by the Harvard T.H. Chan School of Public Health found that running for 15 minutes a day or walking for an hour reduces the risk of major depression by 26%. In addition to relieving depression symptoms, research also shows that maintaining an exercise schedule can prevent you from relapsing.\n", "Exercise is a powerful depression fighter for several reasons. Most importantly, it promotes all kinds of changes in the brain, including neural growth, reduced inflammation, and new activity patterns that promote feelings of calm and well-being. It also releases endorphins, powerful chemicals in your brain that energize your spirits and make you feel good. Finally, exercise can also serve as a distraction, allowing you to find some quiet time to break out of the cycle of negative thoughts that feed depression.\n", "Exercise is not just about aerobic capacity and muscle size. Sure, exercise can improve your physical health and your physique, trim your waistline, improve your sex life, and even add years to your life. But that’s not what motivates most people to stay active.\n", "People who exercise regularly tend to do so because it gives them an enormous sense of well-being. They feel more energetic throughout the day, sleep better at night, have sharper memories, and feel more relaxed and positive about themselves and their lives. And it’s also powerful medicine for many common mental health challenges.\n", "Regular exercise can have a profoundly positive impact on depression, anxiety, ADHD, and more. It also relieves stress, improves memory, helps you sleep better, and boosts your overall mood. And you don’t have to be a fitness fanatic to reap the benefits. Research indicates that modest amounts of exercise can make a difference. No matter your age or fitness level, you can learn to use exercise as a powerful tool to feel better.\n", "Ever noticed how your body feels when you’re under stress? Your muscles may be tense, especially in your face, neck, and shoulders, leaving you with back or neck pain, or painful headaches. You may feel a tightness in your chest, a pounding pulse, or muscle cramps. You may also experience problems such as insomnia, heartburn, stomachache, diarrhea, or frequent urination. The worry and discomfort of all these physical symptoms can in turn lead to even more stress, creating a vicious cycle between your mind and body.\n", "Exercising is an effective way to break this cycle. As well as releasing endorphins in the brain, physical activity helps to relax the muscles and relieve tension in the body. Since the body and mind are so closely linked, when your body feels better so, too, will your mind.Evidence suggests that by really focusing on your body and how it feels as you exercise, you can actually help your nervous system become “unstuck” and begin to move out of the immobilization stress response that characterizes PTSD or trauma. \n", "Instead of allowing your mind to wander, pay close attention to the physical sensations in your joints and muscles, even your insides as your body moves. Exercises that involve cross movement and that engage both arms and legs—such as walking (especially in sand), running, swimming, weight training, or dancing—are some of your best choices.\n", "Outdoor activities like hiking, sailing, mountain biking, rock climbing, whitewater rafting, and skiing (downhill and cross-country) have also been shown to reduce the symptoms of PTSD.\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", " As one example, a recent study done by the Harvard T.H. Chan School of Public Health found that running for 15 minutes a day or walking for an hour reduces the risk of major depression by 26%.\n", " No matter your age or fitness level, you can learn to use exercise as a powerful tool to feel better.\n", " The worry and discomfort of all these physical symptoms can in turn lead to even more stress, creating a vicious cycle between your mind and body.\n", " As well as releasing endorphins in the brain, physical activity helps to relax the muscles and relieve tension in the body.\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Importing the summarize function from gensim module\n", "# Cannot use with gensim version > 3.6.0, need to install gensim==3.6.0\n", "%pip install gensim==3.6.0\n", "import gensim\n", "from gensim.summarization.summarizer import summarize\n", "\n", "# Pass the document along with desired word count to get the summary\n", "my_summary=summarize(original_text,word_count=100)\n", "print(my_summary)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 49. How to summarize text based on the LexRank algorithm ?\n", "Difficulty Level : L3\n", "\n", "Q. Extract the summary of the given text based on the TextRank Algorithm."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["original_text=\"\"\"Studies show that exercise can treat mild to moderate depression as effectively as antidepressant medication—but without the side-effects, of course. As one example, a recent study done by the Harvard T.H. Chan School of Public Health found that running for 15 minutes a day or walking for an hour reduces the risk of major depression by 26%. In addition to relieving depression symptoms, research also shows that maintaining an exercise schedule can prevent you from relapsing.\n", "Exercise is a powerful depression fighter for several reasons. Most importantly, it promotes all kinds of changes in the brain, including neural growth, reduced inflammation, and new activity patterns that promote feelings of calm and well-being. It also releases endorphins, powerful chemicals in your brain that energize your spirits and make you feel good. Finally, exercise can also serve as a distraction, allowing you to find some quiet time to break out of the cycle of negative thoughts that feed depression.\n", "Exercise is not just about aerobic capacity and muscle size. Sure, exercise can improve your physical health and your physique, trim your waistline, improve your sex life, and even add years to your life. But that’s not what motivates most people to stay active.\n", "People who exercise regularly tend to do so because it gives them an enormous sense of well-being. They feel more energetic throughout the day, sleep better at night, have sharper memories, and feel more relaxed and positive about themselves and their lives. And it’s also powerful medicine for many common mental health challenges.\n", "Regular exercise can have a profoundly positive impact on depression, anxiety, ADHD, and more. It also relieves stress, improves memory, helps you sleep better, and boosts your overall mood. And you don’t have to be a fitness fanatic to reap the benefits. Research indicates that modest amounts of exercise can make a difference. No matter your age or fitness level, you can learn to use exercise as a powerful tool to feel better.\n", "Ever noticed how your body feels when you’re under stress? Your muscles may be tense, especially in your face, neck, and shoulders, leaving you with back or neck pain, or painful headaches. You may feel a tightness in your chest, a pounding pulse, or muscle cramps. You may also experience problems such as insomnia, heartburn, stomachache, diarrhea, or frequent urination. The worry and discomfort of all these physical symptoms can in turn lead to even more stress, creating a vicious cycle between your mind and body.\n", "Exercising is an effective way to break this cycle. As well as releasing endorphins in the brain, physical activity helps to relax the muscles and relieve tension in the body. Since the body and mind are so closely linked, when your body feels better so, too, will your mind.Evidence suggests that by really focusing on your body and how it feels as you exercise, you can actually help your nervous system become “unstuck” and begin to move out of the immobilization stress response that characterizes PTSD or trauma. \n", "Instead of allowing your mind to wander, pay close attention to the physical sensations in your joints and muscles, even your insides as your body moves. Exercises that involve cross movement and that engage both arms and legs—such as walking (especially in sand), running, swimming, weight training, or dancing—are some of your best choices.\n", "Outdoor activities like hiking, sailing, mountain biking, rock climbing, whitewater rafting, and skiing (downhill and cross-country) have also been shown to reduce the symptoms of PTSD.\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", " Since the body and mind are so closely linked, when your body feels better so, too, will your mind.Evidence suggests that by really focusing on your body and how it feels as you exercise, you can actually help your nervous system become “unstuck” and begin to move out of the immobilization stress response that characterizes PTSD or trauma.>, <Sentence: Instead of allowing your mind to wander, pay close attention to the physical sensations in your joints and muscles, even your insides as your body moves.\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sumy.summarizers.lex_rank import LexRankSummarizer\n", "\n", "#Plain text parsers since we are parsing through text\n", "from sumy.parsers.plaintext import PlaintextParser\n", "from sumy.nlp.tokenizers import Tokenizer\n", "\n", "parser=PlaintextParser.from_string(original_text,Tokenizer(\"english\"))\n", "\n", "summarizer=LexRankSummarizer()\n", "my_summary=summarizer(parser.document,2)\n", "print(my_summary)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 50. How to summarize text using <PERSON><PERSON> algorithm?\n", "<PERSON>. Extract the summary of the given text based on the Luhn Algorithm.\n", "\n", "Difficulty Level : L3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["original_text=\"\"\"Studies show that exercise can treat mild to moderate depression as effectively as antidepressant medication—but without the side-effects, of course. As one example, a recent study done by the Harvard T.H. Chan School of Public Health found that running for 15 minutes a day or walking for an hour reduces the risk of major depression by 26%. In addition to relieving depression symptoms, research also shows that maintaining an exercise schedule can prevent you from relapsing.\n", "Exercise is a powerful depression fighter for several reasons. Most importantly, it promotes all kinds of changes in the brain, including neural growth, reduced inflammation, and new activity patterns that promote feelings of calm and well-being. It also releases endorphins, powerful chemicals in your brain that energize your spirits and make you feel good. Finally, exercise can also serve as a distraction, allowing you to find some quiet time to break out of the cycle of negative thoughts that feed depression.\n", "Exercise is not just about aerobic capacity and muscle size. Sure, exercise can improve your physical health and your physique, trim your waistline, and even add years to your life. But that’s not what motivates most people to stay active.\n", "People who exercise regularly tend to do so because it gives them an enormous sense of well-being. They feel more energetic throughout the day, sleep better at night, have sharper memories, and feel more relaxed and positive about themselves and their lives. And it’s also powerful medicine for many common mental health challenges.\n", "Regular exercise can have a profoundly positive impact on depression, anxiety, ADHD, and more. It also relieves stress, improves memory, helps you sleep better, and boosts your overall mood. And you don’t have to be a fitness fanatic to reap the benefits. Research indicates that modest amounts of exercise can make a difference. No matter your age or fitness level, you can learn to use exercise as a powerful tool to feel better.\n", "Ever noticed how your body feels when you’re under stress? Your muscles may be tense, especially in your face, neck, and shoulders, leaving you with back or neck pain, or painful headaches. You may feel a tightness in your chest, a pounding pulse, or muscle cramps. You may also experience problems such as insomnia, heartburn, stomachache, diarrhea, or frequent urination. The worry and discomfort of all these physical symptoms can in turn lead to even more stress, creating a vicious cycle between your mind and body.\n", "Exercising is an effective way to break this cycle. As well as releasing endorphins in the brain, physical activity helps to relax the muscles and relieve tension in the body. Since the body and mind are so closely linked, when your body feels better so, too, will your mind.Evidence suggests that by really focusing on your body and how it feels as you exercise, you can actually help your nervous system become “unstuck” and begin to move out of the immobilization stress response that characterizes PTSD or trauma. \n", "Instead of allowing your mind to wander, pay close attention to the physical sensations in your joints and muscles, even your insides as your body moves. Exercises that involve cross movement and that engage both arms and legs—such as walking (especially in sand), running, swimming, weight training, or dancing—are some of your best choices.\n", "Outdoor activities like hiking, sailing, mountain biking, rock climbing, whitewater rafting, and skiing (downhill and cross-country) have also been shown to reduce the symptoms of PTSD.\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "  Finally, exercise can also serve as a distraction, allowing you to find some quiet time to break out of the cycle of negative thoughts that feed depression.  Since the body and mind are so closely linked, when your body feels better so, too, will your mind.Evidence suggests that by really focusing on your body and how it feels as you exercise, you can actually help your nervous system become “unstuck” and begin to move out of the immobilization stress response that characterizes PTSD or trauma\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sumy.summarizers.luhn import LuhnSummarizer\n", "\n", "#Plain text parsers since we are parsing through text\n", "from sumy.parsers.plaintext import PlaintextParser\n", "from sumy.nlp.tokenizers import Tokenizer\n", "\n", "parser=PlaintextParser.from_string(original_text,Tokenizer(\"english\"))\n", "\n", "summarizer=LuhnSummarizer()\n", "my_summary=summarizer(parser.document,2)\n", "print(my_summary)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 51. How to summarize text based on LSA algorithm ?\n", "Difficulty Level : L3\n", "\n", "Q. Extract the summary of the given text based on the LSA Algorithm."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["original_text=\"\"\"Studies show that exercise can treat mild to moderate depression as effectively as antidepressant medication—but without the side-effects, of course. As one example, a recent study done by the Harvard T.H. Chan School of Public Health found that running for 15 minutes a day or walking for an hour reduces the risk of major depression by 26%. In addition to relieving depression symptoms, research also shows that maintaining an exercise schedule can prevent you from relapsing.\n", "Exercise is a powerful depression fighter for several reasons. Most importantly, it promotes all kinds of changes in the brain, including neural growth, reduced inflammation, and new activity patterns that promote feelings of calm and well-being. It also releases endorphins, powerful chemicals in your brain that energize your spirits and make you feel good. Finally, exercise can also serve as a distraction, allowing you to find some quiet time to break out of the cycle of negative thoughts that feed depression.\n", "Exercise is not just about aerobic capacity and muscle size. Sure, exercise can improve your physical health and your physique, trim your waistline, and even add years to your life. But that’s not what motivates most people to stay active.\n", "People who exercise regularly tend to do so because it gives them an enormous sense of well-being. They feel more energetic throughout the day, sleep better at night, have sharper memories, and feel more relaxed and positive about themselves and their lives. And it’s also powerful medicine for many common mental health challenges.\n", "Regular exercise can have a profoundly positive impact on depression, anxiety, ADHD, and more. It also relieves stress, improves memory, helps you sleep better, and boosts your overall mood. And you don’t have to be a fitness fanatic to reap the benefits. Research indicates that modest amounts of exercise can make a difference. No matter your age or fitness level, you can learn to use exercise as a powerful tool to feel better.\n", "Ever noticed how your body feels when you’re under stress? Your muscles may be tense, especially in your face, neck, and shoulders, leaving you with back or neck pain, or painful headaches. You may feel a tightness in your chest, a pounding pulse, or muscle cramps. You may also experience problems such as insomnia, heartburn, stomachache, diarrhea, or frequent urination. The worry and discomfort of all these physical symptoms can in turn lead to even more stress, creating a vicious cycle between your mind and body.\n", "Exercising is an effective way to break this cycle. As well as releasing endorphins in the brain, physical activity helps to relax the muscles and relieve tension in the body. Since the body and mind are so closely linked, when your body feels better so, too, will your mind.Evidence suggests that by really focusing on your body and how it feels as you exercise, you can actually help your nervous system become “unstuck” and begin to move out of the immobilization stress response that characterizes PTSD or trauma. \n", "Instead of allowing your mind to wander, pay close attention to the physical sensations in your joints and muscles, even your insides as your body moves. Exercises that involve cross movement and that engage both arms and legs—such as walking (especially in sand), running, swimming, weight training, or dancing—are some of your best choices.\n", "Outdoor activities like hiking, sailing, mountain biking, rock climbing, whitewater rafting, and skiing (downhill and cross-country) have also been shown to reduce the symptoms of PTSD.\"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "In addition to relieving depression symptoms, research also shows that maintaining an exercise schedule can prevent you from relapsing. People who exercise regularly tend to do so because it gives them an enormous sense of well-being.\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sumy.summarizers.lsa import LsaSummarizer\n", "\n", "#Plain text parsers since we are parsing through text\n", "from sumy.parsers.plaintext import PlaintextParser\n", "from sumy.nlp.tokenizers import Tokenizer\n", "\n", "parser=PlaintextParser.from_string(original_text,Tokenizer(\"english\"))\n", "\n", "summarizer=LsaSummarizer()\n", "my_summary=summarizer(parser.document,2)\n", "print(my_summary)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 52. How to convert documents into json format ?\n", "Difficulty Level : L3\n", "\n", "<PERSON><PERSON> Covert the given text documents into json format for spacy usage"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text1=\"Netflix has released a new series\"\n", "text2=\"It was shot in London\"\n", "text3=\"It is called Dark and the main character is <PERSON>\"\n", "text4=\"<PERSON> is the evil character\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "{'id': 0,\n", " 'paragraphs': [{'cats': [],\n", "   'raw': 'Netflix has released a new series',\n", "   'sentences': [{'brackets': [],\n", "     'tokens': [{'dep': 'nsubj',\n", "       'head': 2,\n", "       'id': 0,\n", "       'ner': 'U-ORG',\n", "       'orth': 'Netflix',\n", "       'tag': 'NNP'},\n", "      {'dep': 'aux',\n", "       'head': 1,\n", "       'id': 1,\n", "       'ner': 'O',\n", "       'orth': 'has',\n", "       'tag': 'VBZ'},\n", "      {'dep': 'ROOT',\n", "       'head': 0,\n", "       'id': 2,\n", "       'ner': 'O',\n", "       'orth': 'released',\n", "       'tag': 'VBN'},\n", "      {'dep': 'det', 'head': 2, 'id': 3, 'ner': 'O', 'orth': 'a', 'tag': 'DT'},\n", "      {'dep': 'amod',\n", "       'head': 1,\n", "       'id': 4,\n", "       'ner': 'O',\n", "       'orth': 'new',\n", "       'tag': 'JJ'},\n", "      {'dep': 'dobj',\n", "       'head': -3,\n", "       'id': 5,\n", "       'ner': 'O',\n", "       'orth': 'series',\n", "       'tag': 'NN'}]}]},\n", "    ...(truncated)\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import spacy\n", "nlp=spacy.load(\"en_core_web_sm\")\n", "\n", "# Covert into spacy documents\n", "doc1=nlp(text1)\n", "doc2=nlp(text2)\n", "doc3=nlp(text3)\n", "doc4=nlp(text4)\n", "\n", "# Import docs_to_json\n", "# spacy.gold.docs_to_json is depricated. Cannot use with current version\n", "# from spacy.gold import docs_to_json\n", "from spacy.tokens import Doc\n", "\n", "import json\n", "formatt = json.dumps(doc1.to_json(), indent=2)\n", "print(formatt)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 53. How to build a text classifier with TextBlob ?\n", "Difficulty Level : L3\n", "\n", "Q Build a text classifier with available train data using textblob library"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data to train the classifier\n", "train = [\n", "    ('I love eating sushi','food-review'),\n", "    ('This is an amazing place!', 'Tourist-review'),\n", "    ('Pizza is my all time favorite food','food-review'),\n", "    ('I baked a cake yesterday, it was tasty', 'food-review'),\n", "    (\"What an awesome taste this sushi has\", 'food-review'),\n", "    ('It is a perfect place for outing', 'Tourist-review'),\n", "    ('This is a nice picnic spot', 'Tourist-review'),\n", "    (\"Families come out on tours here\", 'Tourist-review'),\n", "    ('It is a beautiful place !', 'Tourist-review'),\n", "    ('The place was warm and nice', 'Tourist-review')\n", "]\n", "test = [\n", "    ('The sushi was good', 'food-review'),\n", "    ('The place was perfect for picnics ', 'Tourist-review'),\n", "    (\"Burgers are my favorite food\", 'food-review'),\n", "    (\"I feel amazing!\", 'food-review'),\n", "    ('It is an amazing place', 'Tourist-review'),\n", "    (\"This isn't a very good place\", 'Tourist-review')\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "Accuracy: 0.8333333333333334\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Importing the classifier\n", "from textblob.classifiers import NaiveBayesClassifier\n", "from textblob import TextBlob\n", "\n", "# Training\n", "cl = NaiveBayesClassifier(train)\n", "\n", "# Classify some text\n", "print(cl.classify(\"My favorite food is spring rolls\"))  \n", "print(cl.classify(\"It was a cold place for picnic\"))  \n", "\n", "# Printing accuracy of classifier\n", "print(\"Accuracy: {0}\".format(cl.accuracy(test)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 54. How to train a text classifier using Simple transformers ?\n", "Difficulty Level : L4\n", "\n", "Q. Build and train a text classifier for the given data using simpletransformers library"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_data = [\n", "    [\"The movie was amazing\", 1],\n", "    [\"It was a boring movie\", 0],\n", "    [\"I had a great experience\",1],\n", "    [\"I was bored during the movie\",0],\n", "    [\"The movie was great\",1],\n", "    [\"The movie was bad\",0],\n", "    [\"The movie was good\",1]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import requirements\n", "from simpletransformers.classification import ClassificationModel, ClassificationArgs\n", "import pandas as pd\n", "import logging\n", "\n", "\n", "logging.basicConfig(level=logging.INFO)\n", "transformers_logger = logging.getLogger(\"transformers\")\n", "transformers_logger.setLevel(logging.WARNING)\n", "\n", "# Preparing train data\n", "\n", "train_df = pd.DataFrame(train_data)\n", "train_df.columns = [\"text\", \"labels\"]\n", "\n", "# Optional model configuration\n", "model_args = ClassificationArgs(num_train_epochs=5)\n", "\n", "# Create a ClassificationModel\n", "model = ClassificationModel(\"bert\", \"bert-base-uncased\", args=model_args,use_cuda=False)\n", "\n", "# Train the model\n", "model.train_model(train_df)\n", "\n", "\n", "# Make predictions with the model\n", "predictions, raw_outputs = model.predict([\"The titanic was a good movie\"])\n", "\n", "predictions"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 55. How to perform text classification using spaCy ?\n", "Difficulty Level : L4\n", "\n", "Q. Build a text classifier using spacy that can classify IMDB reviews as positive or negative"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # NOT WORKING\n", "# import spacy\n", "# nlp=spacy.load(\"en_core_web_sm\")\n", "\n", "\n", "# textcat = nlp.create_pipe(\"textcat\", config={\"exclusive_classes\": True, \"architecture\": \"simple_cnn\"})\n", "# nlp.add_pipe(textcat, last=True)\n", "# textcat = nlp.get_pipe(\"textcat\")\n", "\n", "# # add label to text classifier\n", "# textcat.add_label(\"POSITIVE\")\n", "# textcat.add_label(\"NEGATIVE\")\n", "\n", "\n", "# def load_data(limit=0, split=0.8):\n", "#     \"\"\"Load data from the IMDB dataset.\"\"\"\n", "#     # Partition off part of the train data for evaluation\n", "#     train_data, _ = thinc.extra.datasets.imdb()\n", "#     random.shuffle(train_data)\n", "#     train_data = train_data[-limit:]\n", "#     texts, labels = zip(*train_data)\n", "#     cats = [{\"POSITIVE\": bool(y), \"NEGATIVE\": not bool(y)} for y in labels]\n", "#     split = int(len(train_data) * split)\n", "#     return (texts[:split], cats[:split]), (texts[split:], cats[split:])\n", "\n", "\n", "# # load the IMDB dataset\n", "# print(\"Loading IMDB data...\")\n", "# (train_texts, train_cats), (dev_texts, dev_cats) = load_data()\n", "# train_texts = train_texts[:n_texts]\n", "# train_cats = train_cats[:n_texts]\n", "    \n", "# train_data = list(zip(train_texts, [{\"cats\": cats} for cats in train_cats]))\n", "\n", "# # get names of other pipes to disable them during training\n", "# pipe_exceptions = [\"textcat\", \"trf_wordpiecer\", \"trf_tok2vec\"]\n", "# other_pipes = [pipe for pipe in nlp.pipe_names if pipe not in pipe_exceptions]\n", "\n", "# # Training the text classifier\n", "# with nlp.disable_pipes(*other_pipes):  # only train textcat\n", "#    optimizer = nlp.begin_training()\n", "#    if init_tok2vec is not None:\n", "#       with init_tok2vec.open(\"rb\") as file_:\n", "#         textcat.model.tok2vec.from_bytes(file_.read())\n", "#         print(\"Training the model...\")\n", "#         print(\"{:^5}\\t{:^5}\\t{:^5}\\t{:^5}\".format(\"LOSS\", \"P\", \"R\", \"F\"))\n", "#         batch_sizes = compounding(4.0, 32.0, 1.001)\n", "#         for i in range(n_iter):\n", "#             losses = {}\n", "#             # batch up the examples using <PERSON><PERSON><PERSON>'s minibatch\n", "#             random.shuffle(train_data)\n", "#             batches = minibatch(train_data, size=batch_sizes)\n", "#             for batch in batches:\n", "#                 texts, annotations = zip(*batch)\n", "#                 nlp.update(texts, annotations, sgd=optimizer, drop=0.2, losses=losses)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 56. How to translate the text (using simpletransformers) ?\n", "Difficulty Level : L3\n", "\n", "Q. Translate the given list of texts from English to Dutch using simpletransformers package"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["['Our experienced writers travel the world to bring you informative and inspirational features, destination roundups, travel ideas, tips and beautiful photos in order to help you plan your next holiday',\n", "                  'Each part of Germany is different, and there are thousands of memorable places to visit.',\n", "                  \"Christmas Markets originated in Germany, and the tradition dates to the Late Middle Ages.\",\n", "                  \"Garmisch-Partenkirchen is a small town in Bavaria, near Germany’s highest mountain Zugspitze, which rises to 9,718 feet (2,962 meters)\",\n", "                  \"It’s one of the country’s top alpine destinations, extremely popular during the winter\",\n", "                  \"In spring, take a road trip through Bavaria and enjoy the view of the dark green Alps and the first alpine wildflowers. \"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "['Unsere erfahrenen Autoren reisen die Welt, um Ihnen informative und inspirierende Funktionen, Destination Rund',\n", "'Jeder Teil Deutschlands ist anders, und es gibt Tausende von denkwürdigen Orten zu besuchen.',\n", "'Weihnachtsmärkte entstanden in Deutschland, und die Tradition stammt aus dem späten Mittelalter.',\n", "'Garmisch-Partenkirchen ist eine kleine Stadt in Bayern, nahe Deutschland.Die Zug',\n", "'Es ist eines der Top-Alpenziele des Landes, sehr beliebt im Winter',\n", "'Im <PERSON> machen Sie eine Roadtrip durch Bayern und genießen den Blick auf die dunkelgrünen Alpen']\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import the model\n", "from simpletransformers.seq2seq import Seq2SeqModel\n", "\n", "# Setting desired arguments\n", "my_args = {    \"train_batch_size\": 2,\n", "               \"num_train_epochs\": 10,\n", "               \"save_eval_checkpoints\": <PERSON>als<PERSON>,\n", "               \"save_model_every_epoch\": <PERSON><PERSON><PERSON>,\n", "               \"evaluate_during_training\": True,\n", "               \"evaluate_generated_text\": True   }\n", "\n", "# Instantiating the model\n", "my_model=Seq2SeqModel(encoder_decoder_name=\"Helsinki-NLP/opus-mt-en-de\",encoder_decoder_type=\"marian\",args=my_args,use_cuda=False)\n", "\n", "\n", "# translating the text\n", "\n", "my_model.predict(['Our experienced writers travel the world to bring you informative and inspirational features, destination roundups, travel ideas, tips and beautiful photos in order to help you plan your next holiday',\n", "                  'Each part of Germany is different, and there are thousands of memorable places to visit.',\n", "                  \"Christmas Markets originated in Germany, and the tradition dates to the Late Middle Ages.\",\n", "                  \"Garmisch-Partenkirchen is a small town in Bavaria, near Germany’s highest mountain Zugspitze, which rises to 9,718 feet (2,962 meters)\",\n", "                  \"It’s one of the country’s top alpine destinations, extremely popular during the winter\",\n", "                  \"In spring, take a road trip through Bavaria and enjoy the view of the dark green Alps and the first alpine wildflowers. \"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 57. How to create a Question-Answering system from given context\n", "Difficulty Level : L4\n", "\n", "Q. Build a Question Answering model that answers questions from the given context using transformers package"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["context=\"\"\" <PERSON> is the best book series according to many people. <PERSON> was written by <PERSON><PERSON><PERSON> .\n", "It is afantasy based novel that provides a thrilling experience to readers.\"\"\"\n", "\n", "question=\"What is <PERSON> ?\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "{'score': 0.2375375191101107, 'start': 17, 'end': 37, 'answer': 'the best book series'}\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Install and import the pipeline of transformers\n", "from transformers import pipeline\n", "\n", "# Get thetask-specific pipeline\n", "my_model=pipeline(task=\"question-answering\")\n", "\n", "context = r\"\"\" <PERSON> is the best book series according to many people. <PERSON> was written by <PERSON><PERSON><PERSON> .\n", "It is afantasy based novel that provides a thrilling experience to readers.\"\"\"\n", "\n", "# Pass the question and context to the model to obtain answer\n", "print(my_model(question=\"What is <PERSON> ?\", context=context))\n", "print(my_model(question=\"Who wrote <PERSON> ?\", context=context))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 58. How to do text generation starting from a given piece of text?\n", "Difficulty Level : L4\n", "\n", "Q. Generate text based on the the starting provided."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["starting=\"It was a bright\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "'It was a bright day in New Jersey\\'s capitol,\" the senator told a reporter after the rally. \"It\\'s a sunny day in New Hampshire, there\\'s a great deal of sunshine.'\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import pipeline from transformers package\n", "from transformers import pipeline\n", "\n", "# Get the task-specific pipeline\n", "my_model=pipeline(task=\"text-generation\")\n", "\n", "# Pass the starting sequence as input to generate text\n", "my_model(starting)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 59. How to classify a text as positive or negative sentiment with transformers?\n", "Difficulty Level : L4\n", "\n", "Q. Find out whether a given text is postive or negative sentiment along with score for predictions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text1=\"It is a pleasant day, I am going for a walk\"\n", "text2=\"I have a terrible headache\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Desired Output :\n", "```\n", "[{'label': 'POSITIVE', 'score': 0.9998570084571838}]\n", "[{'label': 'NEGATIVE', 'score': 0.9994378089904785}]\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import pipeline from transformers package\n", "from transformers import pipeline\n", "\n", "# Get the task specific pipeline\n", "my_model = pipeline(\"sentiment-analysis\")\n", "\n", "# Predicting the sentiment with score\n", "print(my_model(text1))\n", "print(my_model(text2))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}