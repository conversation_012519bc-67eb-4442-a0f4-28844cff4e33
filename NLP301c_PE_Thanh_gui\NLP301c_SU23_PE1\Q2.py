import nltk
from nltk.probability import FreqDist

#Enter the input string
string = "This is <PERSON><PERSON><PERSON>\'s first visit to Vietnam as a Secretary of State.He has already visited Vietnam in 2015 and 2016 as the Deputy Secretary of State under the <PERSON> administration"

# Create a frequency distribution for all words in the text
freqDist = FreqDist(word for word in string)

# Find words that appear at least three times
words = [word for word, freq in freqDist.items() if freq >= 3]

#Print the ouput
print(words)


