""""Question 2: (2 marks)
Write the slice expression that extracts the first three words of text.
Imput:   text = 'She received the news of the discovery with equanimity'
Desired Output:
[She', 'received', 'the']
"""

# Import necessary module from nltk
from nltk.tokenize import word_tokenize

# Input text
text = 'She received the news of the discovery with equanimity'

# Tokenize the text into words
words = word_tokenize(text)

# Extract the first three words
first_three_words = words[:3]

# Print the result
print("First three words:", first_three_words)
